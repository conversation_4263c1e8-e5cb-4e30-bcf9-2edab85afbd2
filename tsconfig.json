{"compilerOptions": {"target": "es2015", "downlevelIteration": true, "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "noImplicitAny": false, "moduleResolution": "Node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "paths": {"@/*": ["./*"]}, "plugins": [{"name": "next"}]}, "ts-node": {"compilerOptions": {"module": "commonjs"}}, "include": ["video.d.ts", "next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "**/*.cjs"], "exclude": ["node_modules"]}