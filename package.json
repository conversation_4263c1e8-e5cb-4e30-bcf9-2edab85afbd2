{"name": "talamo-app-pages", "version": "0.1.0", "private": true, "engines": {"node": ">=18"}, "scripts": {"predev": "npm run fetch-assessment-data", "dev": "next dev", "prebuild": "npm run fetch-assessment-data && npm run codegen", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "prepare": "husky install", "codegen": "graphql-codegen --config graphql.config.ts", "fetch-assessment-data": "ts-node utils/dato-cms/fetch-assessment-data.ts", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"@chakra-ui/icons": "^2.0.19", "@chakra-ui/next-js": "^2.1.4", "@chakra-ui/react": "^2.7.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/cache": "^11.11.0", "@emotion/css": "^11.11.0", "@emotion/react": "^11.11.1", "@emotion/server": "^11.11.0", "@emotion/styled": "^11.11.0", "@graphql-codegen/cli": "^5.0.3", "@graphql-codegen/typed-document-node": "^5.0.11", "@graphql-codegen/typescript": "^4.1.1", "@graphql-codegen/typescript-operations": "^4.3.1", "@graphql-typed-document-node/core": "^3.2.0", "@heroicons/react": "^2.0.18", "@hookform/resolvers": "^3.10.0", "@next/third-parties": "^14.1.0", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tooltip": "^1.2.7", "@react-pdf/font": "^2.5.1", "@react-pdf/renderer": "^4.1.5", "@stripe/react-stripe-js": "^2.3.1", "@stripe/stripe-js": "^2.1.11", "@supabase/auth-helpers-nextjs": "^0.7.2", "@supabase/auth-helpers-react": "^0.4.0", "@supabase/ssr": "^0.1.0", "@supabase/supabase-js": "^2.25.0", "@tanstack/react-query": "^5.27.5", "@tanstack/react-query-devtools": "^5.27.8", "@tanstack/react-table": "^8.13.2", "@types/jest": "^29.5.2", "@types/lodash": "^4.14.202", "@types/node": "20.3.1", "@types/react": "18.2.12", "@types/react-dom": "18.2.5", "axios": "^1.7.4", "chart.js": "^4.4.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cookies-next": "^3.0.0", "cors": "^2.8.5", "crypto": "^1.0.1", "date-fns": "^3.6.0", "eslint-config-next": "latest", "formik": "^2.4.5", "framer-motion": "^10.12.16", "graphql-tag": "^2.12.6", "html-react-parser": "^5.2.2", "i": "^0.3.7", "input-otp": "^1.4.2", "install": "^0.13.0", "jose": "^5.10.0", "json-2-csv": "^5.5.9", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "lottie-react": "^2.4.0", "lucide-react": "^0.475.0", "micro": "^10.0.1", "next": "^14.2.22", "next-connect": "^1.0.0", "next-password-protect": "^1.8.0", "node-fetch": "^3.3.2", "npm": "^10.9.2", "posthog-js": "^1.249.0", "react": "^18.2.0", "react-audio-voice-recorder": "^2.1.2", "react-chartjs-2": "^5.2.0", "react-datocms": "^7.1.0", "react-dom": "^18.2.0", "react-dropzone": "^14.3.5", "react-hook-form": "^7.54.2", "react-howler": "^5.2.0", "react-icons": "^5.4.0", "react-pdf": "^9.2.1", "react-pdf-html": "^2.1.3", "react-timer-hook": "^3.0.8", "stripe": "^17.6.0", "supertest": "^6.3.3", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "typescript": "^5.3.3", "util": "^0.12.5", "uuid": "^9.0.0", "xlsx": "^0.18.5", "yup": "^1.4.0", "zod": "^3.22.3", "zustand": "^4.5.6"}, "devDependencies": {"@ag-media/react-pdf-table": "^2.0.0", "@chromatic-com/storybook": "^3.2.6", "@storybook/addon-essentials": "^8.6.7", "@storybook/addon-onboarding": "^8.6.7", "@storybook/blocks": "^8.6.7", "@storybook/experimental-addon-test": "^8.6.7", "@storybook/experimental-nextjs-vite": "^8.6.7", "@storybook/react": "^8.6.7", "@storybook/test": "^8.6.7", "@tanstack/eslint-plugin-query": "^5.27.7", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^14.0.0", "@vitest/browser": "^3.0.9", "@vitest/coverage-v8": "^3.0.9", "eslint": "^8.57.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.1", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-storybook": "^0.11.6", "husky": "^8.0.0", "jest": "^29.5.0", "jest-environment-jsdom": "^29.5.0", "node-mocks-http": "^1.13.0", "playwright": "^1.51.1", "storybook": "^8.6.7", "tailwindcss": "^3.4.17", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "vitest": "^3.0.9"}, "overrides": {"restructure": "3.0.0"}}