# Talamo | Platform

---

## Table of Contents

- Priorities
- Getting Started
- Database
- CMS
-

## Development priority

1. Voice
2. Integrate with CMS :white_check_mark:
3. First pass of the scale elements :white_check_mark:
4. Scoring
5. Finesse UI
6. Dashboard (super basic) :white_check_mark:

---

## Getting Started

### 1. Install the project

- Install Docker CLI (MacOS + Linux)

```bash
# On MacOS + Linux + Windows (WSL2)
brew install docker
```

- In the root directory, install dependencies

```bash
npm i
```

### 2. Configure environment variables

- Ask a senior developer for the `.env` file.
- Copy the `.env` file to the root directory.

### 3. Configure Supabase Edge functions

To run Supabase Edge functions locally, first ensure Dock<PERSON> is running, then:

```bash
# login
supabase login

# start the server
supabase start
```

If you would like to create a new Edge function

```bash
supabase functions new some_function_name
```

Provision the secrets for the functions in an .env file

```bash
supabase secrets set --env-file ./supabase/functions/.env --project-ref the_project_ref

```

If you would like to deploy a new Edge function

```bash
# the project ref is specified in the Supabase dashboard: https://supabase.com/dashboard/
supabase functions deploy some_function_name --project-ref the_project_ref
```

---

## Database (Supabase)

We use Supabase as the database provider for the platform.

Supabase provides a [PostgreSQL](https://www.postgresql.org/) relational database in the cloud, with a number of additional features such as authentication, cloud functions and storage.

A key feature of Supabase is [Row Level Security](https://supabase.com/docs/guides/auth/row-level-security).

### Data tables

:warning: All tables starting `public.` **should not be publicly available to read/write from (e.g. `public.users`).**

This is an unfortunate naming convention from Supabase to differentiate between user-created tables and the `auth.` tables used to handle authentication.

### Database Table structure

```mermaid
flowchart LR;

a[["auth.users"]]
u[["public.users"]]
o[["public.org"]]
t[["public.assessment_takers"]]
r[["Reports"]]

a-->u
u---|user_id|o
o---|org_id|t
u---|user_id|p
t---|tenant_id|r
```

- `auth.users`
  - User accounts for authentication (contains user profiles)
  -
- `public.users` = User profile information and role-based permissions.
- `public.org` = Organization information created by a user.
- `public.tenants` = Tenants created by an organization.

All assessment data is stored in [Dato CMS](#cms).

### User permission levels

- Level `10 = Admin`

  - Administrators can create and remove organisations and tenants. They can also edit the profile of any user in the organization.

- Levels `3 ... 9 = Reserved for future use`

  - Permission levels 3 &rarr; 9 are reserved for future use.

- Level `2 = Org (aka. Enterprise)`

  - Paying organisation users (tenants allowed).

- Level `1 = User`
  - Paying individual user (tenants not allowed).
- Level `0 = Tenant`

  - A tenant of an organization.

- Level `-1 = Invalid / Banned`
  - An invalid or banned user.

### Organisations + Tenants

- An authenticated user is required to access the platform.
- A user can have one or more organisations.
- An organisation can have one or more tenants.

```
User
    |
    |_ Organisation 1
        - Tenant 1
        - Tenant 2
        - Tenant 3
        - Tenant ...
    |
    |_ Organisation 2
        - Tenant 1
        - Tenant 2
        - Tenant 3
        - Tenant ...
    |
    |_ Organisation ...
        - Tenant ...

```

```sql
CREATE TABLE org (
    id bigint generated always as identity primary key default not null        -- Organization key
    username text           -- Distinct username (unique)
    logo_url text           -- User logo url
    first_name text         -- Account owner name
    last_name text          -- Account owner last name
    email text              -- Account owner email
    full_name text          -- Organization name
    address_country text    -- Organization address
    address_street_1 text     -- Organization address
    address_zip text          -- Organization address
    address_street_2 text     -- Organization address
    is_active boolean         -- Account status (active, inactive, etc)
)

CREATE TABLE tenants (
    id bigint generated always as identity primary key,
    name text -- category name
    constraint org_key foreign key id references org (id) -- Organisation key, the ID of the organization that this tenant belongs to.
)
```

## CMS

We use [DatoCMS](https://www.datocms.com/) to manage our content. Requests to DatoCMS are made from Next.js using GraphQL.

View an example of this [here](#ssr).

### SSR

We make use of Server Side Rendering to prevent unauthenticated users from accessing the platform.

Using `getServerSideProps` in Next.js, resources are fetched on the server from remote resource rendered on the server. This is useful for SEO and for reducing the load on the client.

```mermaid
graph LR;
db[(Supabase)]
cms[(DatoCMS)]
sr(("fetch"))
a[App]
s[Server]

db-.-sr
cms-.-sr
sr-->s-->|SSR + props|a
```

### Client side actions / Cloud functions

Functionality such as updating a profile, editing a score, fetching records etc, are handled via cloud functions (not directly reading from the DB) not from the client.

```mermaid
graph LR;
fn[Supabase Functions]
db[(Supabase)]
tp[(Other API)]
a[App]

a<--->|Authorized request|fn-.-db
fn-.-tp
```

### GraphQL

We use [DatoCMS](https://www.datocms.com/) to manage our content. Requests to Datocms are made using GraphQL.

To reduce the demand on the server, we use [`axios-cache-interceptor`](https://axios-cache-interceptor.js.org/)

```ts
import Axios from 'axios';
import { setupCache } from 'axios-cache-interceptor';

const axios = setupCache(Axios,
    storage: buildMemoryStorage(
    maxEntries: 100,
));

export async function getCMSContent(query) {
    let data = null;
    let error = null;
    try {
       data = await axios({
            url: 'https://graphql.datocms.com/',
            method: 'post',
            headers:{
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'Authorization': `Bearer ${token}`,
                "X-Environment": process.env.CMS_ENVIRONMENT_NAME,
                "X-Include-Drafts": process.env.CMS_INCLUDE_DRAFTS ? true : false,
            },
            data: {
              query,
            }
        }).data;
    } catch (err) {
        error = err;
    }
}
```

#### Protected Page

```tsx
/*
 * SSR
 */
export async function getServerSideProps(ctx) {
  // --------------------------------------------
  // 1/5) Dynamic route | Get the asssessment ID from the page
  // --------------------------------------------
  const assessmentID = ctx.query.id;

  // --------------------------------------------
  // 2/5) User Check | Do we have a registered user who is logged in?
  // --------------------------------------------
  const supabase = createPagesServerClient(ctx);
  const {
    data: { session },
  } = await supabase.auth.getSession();
  const {
    data: { user },
  } = await supabase.auth.getUser();
  if (!session || !user) return redirect(`/login?ref=invalid_session`);
  const userId = user?.id;

  // TODO - Refactor in access permissions for schools
  // --------------------------------------------
  // 3/5) Authorization | Does the user have the right permissions?
  // --------------------------------------------
  const isAuthorized = await isUserAuthorized({
    supabase,
    userId,
    targetRole: authRoles().USER,
  });
  if (!isAuthorized) return redirect(`/error?ref=unauthorized`);

  // --------------------------------------------
  // 4/5) Content | Query the CMS for the content
  // --------------------------------------------
  const query = GET_ASSESSMENT_BY_ID(assessmentID);
  const { data: cmsResponse, error } = await getCMSContent({ query });
  if (error) return redirect(`/error?error=${error.code || "unknown-error"}`);
  const content = cmsResponse?.data;

  // --------------------------------------------
  // 5/5) SSR props | Send the props to the page
  // --------------------------------------------
  const userData = await getUserProfile({ supabase, userId });
  return {
    props: {
      user: {
        userId,
        ...userData,
      },
      content,
    },
  };
}

export default function ExamplePage({ cmsContent }) {
  useEffect(() => {
    trackPageView(window?.location?.pathname);
  }, []);
  return <Layout cmsContent={cmsContent} />;
}
```

#### Public Page

```tsx
export async getServerSideProps() {
 const query = SOME_GRAPHQL_QUERY("...");
  const { data: cmsResponse, error } = await getCMSContent({ query });
  if (error) return redirect(`/error?error=${error.code || "unknown-error"}`);
  const content = cmsResponse?.data;
  return {
    props: {
      content,
    },
  }
}
export async default function ExamplePage({ content }) {
  useEffect(() => {
    trackPageView(window?.location?.pathname);
  }, [])
  return (
    <Layout cmsContent={cmsContent} />
  )
}
```

### Server Side Rendering (with data from DB)

```tsx
import { createPagesServerClient } from "@supabase/auth-helpers-nextjs";

export async function getServerSideProps(ctx) {
  const supabase = createPagesServerClient(ctx);
  const {
    data: { session },
  } = await supabase.auth.getSession();

  // Redirect home if not logged in
  if (!session) {
    return {
      redirect: {
        permantent: false,
        location: `/login?ref=`,
      },
    };
  }

  // Fetch the page data
  const { params } = ctx.query;
  const { data, error } = await supabase.from("posts").select(`
        id, name,
        address->city
    `);
  if (error) {
    return {
      redirect: {
        permantent: false,
        location: `/error?error=${error.code || "unknown-error"}`,
      },
    };
  }

  // Return the page data
  return {
    props: {
      user: session?.user ?? null,
      params: params ?? null,
      data: data ?? null,
    },
  };
}
```

## Marking

### 1. Fetch + encrypt the Scale answers

The following request is made when a scale is loaded.

```mermaid
%%{ init : { "flowchart" : { "curve" : "stepAfter" }}}%%

flowchart TD

%%==============================================================================
m(("Merge"))
subgraph getServerSideProps;
GSID["Query GET_SCALE_BY_ID(id)"]-->|"Do not query answers"|m
GSA["Query GET_SCALE_ANSWERS_UNENCRYPTED(id)"]-->|"Encyrpt scale answers"|m
end;
C["App"]
m--->|"Send to client"|C
```

### 2. Call Supabase edge function to check answer against encrypted answer

```tsx
export async function checkAnswer({
  user,
  scaleItemId,
  answer,
  encryptedAnswer,
}) {
  const requestURL = process.env.SUPABASE_EDGE_FN_URL + "/check-answer";
  const { data, error } = await fetch(requestURL, {
    method: "POST",
    headers: {},
    body: JSON.stringify({
      user,
      scaleItemId,
      answer,
      encryptedAnswer,
    }),
  });
}
```

## Summary (TL;DR)

1. Code is written in Typescript.
2. There is no direct access to the database from the client, all requests to the database must be made through Supabase functions.
3. We manage code in Github.
4. We use the Next.js framework (with Pages router) with Server Side Rendering [(SSR)](#ssr). We will be migrating to [Next.js App router](https://nextjs.org/docs/app/building-your-application/upgrading/app-router-migration) in the future.
5. We use Github pull-requests to deploy to [Cloudflare Workers and Pages](https://www.cloudflare.com/en-gb/plans/developer-platform/).
6. We use [Supabase](https://supabase.com/) for Authentication, Database storage and cloud functions (running on [Deno](https://deno.land/)).
7. We use [Jest, Playwright and React Testing Library](https://playwright.dev/docs/testing-library) for testing.
8. We use [DatoCMS](https://datocms.com) (GraphQL) as our CMS.

---

All or nothing progression

- If the user gets three incorrect answers then saver their progress.
- If the user completes the scale, save their progress.

1. Organisations
2. Users
3. Assessment_Taker
