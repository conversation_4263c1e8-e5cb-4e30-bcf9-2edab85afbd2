export const textActions = {
  TOGGLE_AUDIO: "TOGGLE_AUDIO",
  SET_ANSWER: "SET_ANSWER",
};

export type ITextInitialState = {
  audio: {
    isAudioOn: boolean;
    maxAudioPlaybackCount: number;
    audioPlaybackCount: number;
    isAudioComplete: boolean;
  };
  answer: string;
};

export function textReducer(state, action) {
  switch (action?.type) {
    case textActions.TOGGLE_AUDIO: {
      // Set values
      let newPlaybackCount = state.audio.audioPlaybackCount + 1;
      let audioCompletionState = state.audio.isAudioComplete;
      let audioListenState = true; //!state.audio.isAudioOn;
      // Check if user has reached max audio playback count
      if (newPlaybackCount > state.audio.maxAudioPlaybackCount) {
        newPlaybackCount = state.audio.maxAudioPlaybackCount;
        audioCompletionState = true;
        audioListenState = false;
      }
      return {
        ...state,
        audio: {
          ...state.audio,
          isAudioOn: audioListenState,
          audioPlaybackCount: newPlaybackCount,
          isAudioComplete: audioCompletionState,
        },
      };
    }
    case textActions.SET_ANSWER: {
      return {
        ...state,
        answer: action.payload || "",
      };
    }
    default: {
      return state;
    }
  }
}
