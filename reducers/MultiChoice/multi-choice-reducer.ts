export const multiChoiceActions = {
  RESET_STATE: "RESET_STATE",
  TOGGLE_CLICKED: "TOGGLE_CLICKED",
  END_AUDIO: "END_AUDIO",
  TOGGLE_AUDIO: "TOGGLE_AUDIO",
  SET_ANSWER: "SET_ANSWER",
};

export type IMultiChoiceInitialState = {
  audio: {
    isAudioOn: boolean;
    maxAudioPlaybackCount: number;
    audioPlaybackCount: number;
    isAudioComplete: boolean;
  };
  values: Array<{ [key: string]: any }>;
  answersPickedCount: number;
  maxAnswersPickable: number;
  answers: { [key: string]: string | number | boolean };
};

export function multiChoiceReducer(state, action) {
  switch (action?.type) {
    case multiChoiceActions.TOGGLE_CLICKED: {
      const maxAnswersPickable = state.maxAnswersPickable;
      let newValues = [...state.values];

      if (maxAnswersPickable === 1) {
        newValues = newValues.map((value, index) => ({
          ...value,
          isClicked: index === action.payload.index ? !value.isClicked : false,
        }));
      } else {
        newValues[action.payload.index].isClicked =
          !newValues[action.payload.index].isClicked;
      }

      return {
        ...state,
        values: newValues,
      };
    }

    case multiChoiceActions.TOGGLE_AUDIO: {
      // Set values
      let newPlaybackCount = state.audio.audioPlaybackCount + 1;
      let audioCompletionState = state.audio.isAudioComplete;
      let audioListenState = !state.audio.isAudioOn;
      // Check if user has reached max audio playback count
      if (newPlaybackCount > state.audio.maxAudioPlaybackCount) {
        newPlaybackCount = state.audio.maxAudioPlaybackCount;
        audioCompletionState = true;
        audioListenState = false;
      }
      return {
        ...state,
        audio: {
          ...state.audio,
          isAudioOn: audioListenState,
          audioPlaybackCount: newPlaybackCount,
          isAudioComplete: audioCompletionState,
        },
      };
    }
    case multiChoiceActions.RESET_STATE: {
      return {
        ...action.payload.defaultState,
      };
    }
    case multiChoiceActions.SET_ANSWER: {
      const maxAnswersPickable = state.maxAnswersPickable;
      if (!!state.answers[action.payload.index]) {
        const newAnswers = state.answers;
        delete newAnswers[action.payload.index];
        return {
          ...state,
          answers: { ...newAnswers },
          answersPickedCount: state.answersPickedCount - 1,
        };
      } else {
        if (maxAnswersPickable === 1) {
          return {
            ...state,
            answers: {
              [action.payload.index]: action.payload.value,
            },
            answersPickedCount: 1,
          };
        } else {
          return {
            ...state,
            answers: {
              ...state.answers,
              [action.payload.index]: action.payload.value,
            },
            answersPickedCount: state.answersPickedCount + 1,
          };
        }
      }
    }
    default: {
      return state;
    }
  }
}
