import { NextRequest, NextResponse } from "next/server";
import { v4 as uuidv4 } from "uuid";

import { verifyToken } from "@/lib/jwt";

export async function middleware(req: NextRequest) {
  const response = NextResponse.next();

  const sessionId = req.cookies.get("sessionId")?.value;
  if (!sessionId) {
    response.cookies.set("sessionId", uuidv4(), {
      path: "/",
      maxAge: 60 * 60 * 24,
      sameSite: "lax",
    });
  }

  const token = req.cookies.get("student_session")?.value;
  if (!token) {
    return NextResponse.redirect(new URL("/student/login", req.url));
  }

  try {
    const decoded = await verifyToken(token);
    if (!decoded) {
      return NextResponse.redirect(new URL("/student/login", req.url));
    }
  } catch (err) {
    return NextResponse.redirect(new URL("/student/login", req.url));
  }

  return response;
}

export const config = {
  matcher: ["/dashboard/:path*", "/protected/:path*", "/assessment/:path*"],
};
