/* eslint-disable @typescript-eslint/no-require-imports */
import type { Config } from "tailwindcss";
import { fontFamily } from "tailwindcss/defaultTheme";

const config: Config = {
  darkMode: ["class"],
  content: [
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
    "./pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./node_modules/@shadcn/ui/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      fontFamily: {
        sans: ["var(--font-objectivity)", ...fontFamily.sans],
      },
      maxWidth: {
        onboarding: "768px",
        site: "1440px",
        page: "1216px",
        "page-v2": "1110px",
        admin: "1240px",
        "admin-narrow": "950px",
      },
      borderRadius: {
        button: "140px",
        xxs: "2px",
        xs: "4px",
        sm: "calc(var(--radius) - 4px)",
        md: "calc(var(--radius) - 2px)",
        ml: "10px",
        lg: "var(--radius)",
        xl: "24px",
        xxl: "32px",
        "4xl": "48px",
      },
      boxShadow: {
        box: "-2px 2px 6px rgba(0, 0, 0, 0.14)",
        medium: "1px 4px 23px rgba(0, 0, 0, 0.18)",
        "medium-hover": "3px 6px 32px rgba(0, 0, 0, 0.26)",
        huge: "-14px 24px 60px rgba(0, 0, 0, 0.2)",
      },
      colors: {
        blockout: "#333",
        primary: {
          black: "#111124",
          white: "#FFFFFC",
          purple: "#814EF2",
          purple_hover: "#AB8BF2",
          pink: "#FD5C70",
          dark_pink: "#DB5B49",
          yellow: "#FFCB3C",
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          purple_00: "#5058F3",
          purple_01: "#35276D",
          purple_02: "#AB8BF2",
          purple_03: "#E4D8FE",
          purple_04: "#F7EEFF",
          purple_05: "#F9F6FF",
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        tertiary: {
          pink_01: "#FE7B8B",
          pink_04: "#FE7B8B",
          pink_07: "#FFCAD0",
          yellow_01: "#FFD769",
          yellow_02: "#FFF0C7",
          yellow_03: "#FFF9E9",
          yellow_05: "#FFF0C7",
          yellow_06: "#FFF9E9",
          grey_01: "#9F9F9F",
          green_02: "#AEDA47",
        },
        ui: {
          grey_01: "#52606D",
          grey_02: "#B8B8B8",
          grey_03: "#D9D9D9",
          grey_04: "#F7F7F8",
          alert_orange_01: "#F98119",
          "alert-orange-hover": "#F6A55D",
          alert_orange_02: "#FFCFA3",
          alert_orange_03: "#FFEAD7",
          alert_green_01: "#44B342",
          "alert-green-hover": "#6FD06D",
          alert_green_02: "#EBFFD7",
          alert_red_01: "#E74848",
          "alert-red-hover": "#F17373",
          alert_red_02: "#FFEFEF",
          link: "#006FD6",
        },
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        chart: {
          "1": "hsl(var(--chart-1))",
          "2": "hsl(var(--chart-2))",
          "3": "hsl(var(--chart-3))",
          "4": "hsl(var(--chart-4))",
          "5": "hsl(var(--chart-5))",
        },
      },
      backgroundImage: {
        "blue-grey": "linear-gradient(180deg, #C8D8E5 0%, #7191AF 25.52%)",
      },
      fontSize: {
        h1: ["2.875rem", "3.375rem"],
        h2: ["1.7rem", "2.375rem"],
        h3: ["1.5rem", "2rem"],
        h4: ["1.125rem", "1.625rem"],
        h5: ["1rem", "1.5rem"],
        h6: ["0.875rem", "1.375rem"],
        "paragraph-huge": ["1.5rem", "2rem"],
        "paragraph-large": ["1.125rem", "1.625rem"],
        "paragraph-regular": ["1rem", "1.5rem"],
        "paragraph-sm": ["0.875rem", "1.375rem"],
        "paragraph-xs": ["0.75rem", "1.35rem"],
      },
      spacing: {
        xxxs: "2px",
        xxs: "6px",
        xs: "8px",
        sm: "16px",
        ms: "20px",
        md: "24px",
        ml: "32px",
        lg: "48px",
        lg2: "64px",
        xl: "96px",
        xxl: "128px",
      },
      screens: {
        portable: "708px",
        lg: "1366px",
      },
      keyframes: {
        "caret-blink": {
          "0%,70%,100%": { opacity: "1" },
          "20%,50%": { opacity: "0" },
        },
        "pulse-scale": {
          "0%, 100%": { transform: "scale(1)" },
          "50%": { transform: "scale(1.03)" },
        },
      },
      animation: {
        "caret-blink": "caret-blink 1.25s ease-out infinite",
        "pulse-scale": "pulse-scale 1s ease-in-out infinite",
      },
    },
  },
  plugins: [require("tailwindcss-animate")],
};

export default config;
