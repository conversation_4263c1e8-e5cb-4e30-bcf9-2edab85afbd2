export const answers = {
  "172605956": {
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$XVPTD233hJ/LbDMbEpFxt.nO8.tOlGJOdUnByjiilAvwiNTGHqycC",
      unencrypted: "1,3",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "172798627": {
    responseType: "number series",
    answer: {
      markAs: "series",
      isMarked: true,
      unencrypted: "5",
      values: "$2a$04$Xt6wc34LdHjW3RZ96SuKleJ3ta4mufl92u.ROmGw2kGZyF/rbFw1i",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "173906457": {
    responseType: "text",
    answer: {
      markAs: "metadata_question",
      isMarked: false,
      guide: {
        code: 1,
        description:
          "Assessment taker profile info for Supabase. Marking skipped",
      },
    },
  },
  "177155911": {
    responseType: "number series",
    answer: {
      markAs: "series",
      isMarked: true,
      unencrypted: "2",
      values: "$2a$04$ehsqAbAjbpc2w4TJ4ggIeeljTOpKXwCI2dkBX97I.qaWAMQqJPETm",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "*********": {
    responseType: "number series",
    answer: {
      markAs: "series",
      isMarked: true,
      unencrypted: "3",
      values: "$2a$04$NvtfnyXTKXTUuWW.nrEkGelhTCCjwKthdmvy9W9TTlSbIu7GGh4B.",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "*********": {
    responseType: "number series",
    answer: {
      markAs: "series",
      isMarked: true,
      unencrypted: "9",
      values: "$2a$04$RMUQ/MbNrY.Z9wYfqywSV.gEuR2fQ3EPOeaUlI3jFsIukRAnvmJsu",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "*********": {
    responseType: "number series",
    answer: {
      markAs: "series",
      isMarked: true,
      unencrypted: "4",
      values: "$2a$04$vPFgrq1/AbAODDrhaVJRbe0W/C67ro8U8UvQ090LR04Ex5WWt8Cb.",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "*********": {
    responseType: "number series",
    answer: {
      markAs: "series",
      isMarked: true,
      unencrypted: "18",
      values: "$2a$04$SYIQgJsI/W3s6mCayoqejeuo9kKAcdzLzSZcMWQtNnBh2a5ehd1jC",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177155995": {
    responseType: "number series",
    answer: {
      markAs: "series",
      isMarked: true,
      unencrypted: "13",
      values: "$2a$04$91yQBFJ4x1uX3Uk1yaYss.JbzZdkpCO6nrsSSn8wkTSO5zOH4a.PC",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177155999": {
    responseType: "number series",
    answer: {
      markAs: "series",
      isMarked: true,
      unencrypted: "19",
      values: "$2a$04$PwJtjm03Lr1EV44wi.hgSuIbSbWMSMyf47Fl94pjt9X6Q5x.YbV2K",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177156004": {
    responseType: "number series",
    answer: {
      markAs: "series",
      isMarked: true,
      unencrypted: "13",
      values: "$2a$04$Vie7V276wNgHP3f9WOSi3.g.2TLazbCnj6wW67zEVwnG71xEVN7oy",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177156013": {
    responseType: "number series",
    answer: {
      markAs: "series",
      isMarked: true,
      unencrypted: "13",
      values: "$2a$04$Sw/NItfInU1hsIuwZOg8cOacWGRfNsPV.aZrvTMvivNIl7LCpLdom",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177156024": {
    responseType: "number series",
    answer: {
      markAs: "series",
      isMarked: true,
      unencrypted: "21",
      values: "$2a$04$6OxE8cx4WNTMHoe/BnJ5x.8NQAygyjN5UtJTvyTGs682Ei6DkbThC",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177156069": {
    responseType: "number series",
    answer: {
      markAs: "series",
      isMarked: true,
      unencrypted: "1",
      values: "$2a$04$2bVJhsdxKJVdye7u7.NOf.U9KnQkPvdlX3Eb3jSyQDYCY6bT4dl6y",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177158768": {
    responseType: "number series",
    answer: {
      markAs: "series",
      isMarked: true,
      unencrypted: "6",
      values: "$2a$04$nyP6cH0Fid9mWRGfnsLG4Ogby5YcPUnJ/kZ7IX5JUHJ25BI7RZr5O",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177158782": {
    responseType: "number series",
    answer: {
      markAs: "series",
      isMarked: true,
      unencrypted: "20",
      values: "$2a$04$e7eDcoqsjLs/vzoYc.ONnezgbCsi5aGoxbEmqboAvWTDBZlvG5xWW",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177158783": {
    responseType: "number series",
    answer: {
      markAs: "series",
      isMarked: true,
      unencrypted: "4",
      values: "$2a$04$JUg9P8BLsuobNTnR//XWJOOLruxN6rSCInCKd1FaXgHsjtD3yAkd.",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177158784": {
    responseType: "number series",
    answer: {
      markAs: "series",
      isMarked: true,
      unencrypted: "8",
      values: "$2a$04$WwTsH1NAhKPDPrtReZczp.IP.sC6RxcNxm/mLGEk5mtClRHsB.ZaG",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177158789": {
    responseType: "number series",
    answer: {
      markAs: "series",
      isMarked: true,
      unencrypted: "60",
      values: "$2a$04$WsoMHQ3MuSuOWg/jFMyCnu632hOxj5EtH82xa4Dmt.aoj/KFqZqHS",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "*********": {
    responseType: "number series",
    answer: {
      markAs: "series",
      isMarked: true,
      unencrypted: "2",
      values: "$2a$04$Nn1jcGDvkewoG0.vbLv.kefiCOyetmUcItfNS1AmEd5w9NP2Npo7K",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "*********": {
    responseType: "number series",
    answer: {
      markAs: "series",
      isMarked: true,
      unencrypted: "15",
      values: "$2a$04$ggcofHdtE.PA8M0L1fD4VOw3HAPekDLuR55AJCymrUE5ZFGsvPLFO",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "*********": {
    responseType: "number series",
    answer: {
      markAs: "series",
      isMarked: true,
      unencrypted: "14",
      values: "$2a$04$xtm5MyKPSXsG.UqVsuopWuveKzM04Po8Qh0aQoJdBY1ycZjxTD8f6",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "*********": {
    responseType: "number series",
    answer: {
      markAs: "series",
      isMarked: true,
      unencrypted: "13",
      values: "$2a$04$2qeW6hSxD4H4Q.O1LKx9J.vyGoJvu/o/kksYIC7X3aZ.hgdQtmQyi",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177158815": {
    responseType: "number series",
    answer: {
      markAs: "series",
      isMarked: true,
      unencrypted: "16",
      values: "$2a$04$BX.l8JCngwufUEPZHgtZXejZ4ZCs6ukIB4485Qo7fjrXA9a42I.2.",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177158825": {
    responseType: "number series",
    answer: {
      markAs: "series",
      isMarked: true,
      unencrypted: "36",
      values: "$2a$04$MFsrAwX8ackhMCQUQMwYy.mz8y4JUAzvSJrZ.SlDRPolynBwvECI2",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177158827": {
    responseType: "number series",
    answer: {
      markAs: "series",
      isMarked: true,
      unencrypted: "1",
      values: "$2a$04$J1MnAm7YYJ3ny2/pG74i4eLpccVMUptG.MEd4/T..F6366QQA//Ie",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177158828": {
    responseType: "number series",
    answer: {
      markAs: "series",
      isMarked: true,
      unencrypted: "1",
      values: "$2a$04$MvEkVPnnbFujQtEBP/eIceXfJr5I3nssxpu7GCQtK6NjNj67G952C",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177158837": {
    responseType: "number series",
    answer: {
      markAs: "series",
      isMarked: true,
      unencrypted: "45",
      values: "$2a$04$pet4tITiUTM7taG5zMVvZOy8/FdiosAxGDO4mCkGPYFuMZeZB4XrK",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177158840": {
    responseType: "number series",
    answer: {
      markAs: "series",
      isMarked: true,
      unencrypted: "1",
      values: "$2a$04$3SMqKmopW8lOACg773kHNupMHnQUzKnfRtQ1y3uxDnGhn6Spb6RBu",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177158845": {
    responseType: "number series",
    answer: {
      markAs: "series",
      isMarked: true,
      unencrypted: "5",
      values: "$2a$04$XSe.pmhWS/3JfjNcLq1bmubyzXfp6RbuaJt6cvZ4uhsyvo91mtRXi",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177158858": {
    responseType: "number series",
    answer: {
      markAs: "series",
      isMarked: true,
      unencrypted: "3",
      values: "$2a$04$UjtU5Jlkzi2TC/KgX7cw3utxxznWFdNFsA0nolBY2gsf8EX6kb.pS",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177158859": {
    responseType: "number series",
    answer: {
      markAs: "series",
      isMarked: true,
      unencrypted: "14",
      values: "$2a$04$DmmfONhXJ5WZ7DcMJQ53/.QsAdlKxP2Evo2ttjNl4/XJxee43S49.",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177158862": {
    responseType: "number series",
    answer: {
      markAs: "series",
      isMarked: true,
      unencrypted: "88",
      values: "$2a$04$mV6n1alRYlzxCH5.IdehOuHc8CsTJpK4/sLnTe5RxcGVJggMn4aSG",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177158873": {
    responseType: "number series",
    answer: {
      markAs: "series",
      isMarked: true,
      unencrypted: "4",
      values: "$2a$04$xM/VPTsY5ni5ZT0vIzi//esEwS8.M65rAnyCExxseQWCgICowC4N.",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177158874": {
    responseType: "number series",
    answer: {
      markAs: "series",
      isMarked: true,
      unencrypted: "12",
      values: "$2a$04$jZdz1w/mqRzDFIkg7CJDlOBeX.IMPlGFI9d1s/CaAVj5Xh.oRD4be",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177158889": {
    responseType: "number series",
    answer: {
      markAs: "series",
      isMarked: true,
      values: ["$2a$04$4MnDqjkBaZaQFmOe.YCZ.OSTAR2F1vF/TvDmwKZx6oB9VBXl14BFG"],
      unencrypted: ["16,18"],
      guide: {
        code: 5,
        description:
          "Receives answer value as string of numbers concat togerther",
      },
    },
  },
  "177158897": {
    responseType: "number series",
    answer: {
      markAs: "series",
      isMarked: true,
      unencrypted: "81",
      values: "$2a$04$rrtU17gs6zV/gy5eAo2tPuU9jYob/MgIz4bdJ3HRpyDsn79gGgWy2",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177158903": {
    responseType: "number series",
    answer: {
      markAs: "series",
      isMarked: true,
      unencrypted: "20",
      values: "$2a$04$nubBHwEYZwVrigjYbKfBOee3yxm9CmDJ5/ObnkwnDAwVMCVwnqwma",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177158916": {
    responseType: "number series",
    answer: {
      markAs: "series",
      isMarked: true,
      unencrypted: "2",
      values: "$2a$04$lTTk0DVb1b.u8jYIgKpiQelRRE7Fz4CGO9F1BaZ.UYQoa6UGM/oQ.",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177609694": {
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$NjOZNKEirAAoi8yqhVoqWeLucPJx0orp2XqQa.2uG0ZDG5SA4dWm6",
      unencrypted: "2,5",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177609849": {
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$/kGKli/ry4frDqW6oglL/uJhtlqq//kGonOz0we37fgdCH6L12FV2",
      unencrypted: "3,5",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177609869": {
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$.n0JP/FDKqlHMh.bd1HZ5O.Zq.FLHQ559qB8irJQBxwlBYY9Ye65e",
      unencrypted: "2,4",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177609879": {
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$DbaE7i0eP5aFLwivUAXVfuSiUD36QBpofa8b7KYMymTwgkDeAQmn2",
      unencrypted: "1,4",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177609883": {
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$S35ySLRyh6haRj1WmD502OaKzu8BPxQKJstTi5TzwbxdFwZvOwj6e",
      unencrypted: "2,4",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177609885": {
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$jvjAscLCr0Dk47J9TP7c5.1RQsrMxch2TvA8kwwXZ8vCTps.4Zwy2",
      unencrypted: "1,2",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177609888": {
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$DcFt4aN41b034HpQwXaD9eHBuD2Ib4XjAMNhvZWa5eMaW3CM8/PIm",
      unencrypted: "4,6",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177609896": {
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$o6KxoxFG9hoc4j5zlhEa7eu2yGlVncLhKSl15nmxdscE9xOkgFSFi",
      unencrypted: "3,5",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177609901": {
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$iAgGUdhBO8Q3MzYk.5ZeGu6F.4umaFfoc3FayPTrTS3zNACH//OEe",
      unencrypted: "1,4",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177609904": {
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$wOgBTERdt5VpWqOnfWIaWecPYdjqfuOAzlMqNXANSlHLfaYiaZCza",
      unencrypted: "1,3",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177609906": {
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$Fp7crMAm/aRN4bBl48MQ5O4BXn45LKzyog9M.zYpwyHxBtfTBhy0u",
      unencrypted: "2,5",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177610963": {
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$4VZ0dCMlUxWeteZUxiuxkOP5KBki26d/sqFtML19vZ4KySjXaw6ru",
      unencrypted: "1,4",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177610969": {
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$YOeuy.CCzw1VxuaKIGixieVw8dNkgrm8zOe91sV9lG7GIzyKVIo3m",
      unencrypted: "2,6",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177610970": {
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$Av2GTWL/FHr5tnB1Wh0DnO4EOwUG1vCfPOvara15Tc.iKqztQi2Qa",
      unencrypted: "1,3,5",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177610971": {
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$FcySFI1ntdrDRnjIbpDzjuxXHwCE9OucGhxMF2nfnjDXPozYciPm6",
      unencrypted: "1,4,6",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177610972": {
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$iWGoCbQxcPgfigMAWy3ZXui5lo4l86crTDdFjairjpl01nAuo.qPq",
      unencrypted: "2,4",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177610973": {
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$kqjDG4jQff3S29LX5/KSgeVbWnkrWJdQ0fNBM5KdhjlhiX75h.6Ze",
      unencrypted: "4,6",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177610974": {
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$hP2JRFXuUornTQ6AQu1ZY.Yc9LqtR9Mq55drxuTratvojVbKsLF1K",
      unencrypted: "4,6",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177610979": {
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$iVbnAW79pUnyiTb.Q.fH/.gYFfZhOBg9MdaMwH0NaZjAK8LmFTycK",
      unencrypted: "4,5",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177610983": {
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$gODYfe5SOhDllC8/1tXFu.7BFBSJGEYY03rkPNkOx1OBqPCISK3bC",
      unencrypted: "3,5",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177610985": {
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$5W4fAD0Eke7zQaEHARrxDOj4UUWb9WQ4DnGqPH2mPonEObqzIJovW",
      unencrypted: "2,5,6",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177610986": {
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$tGQravXRZaSelsV3ad5.uOnd6wtBsIgU2.pvJhxFxEf36R6mNWWiq",
      unencrypted: "3,5",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177610991": {
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$YUUr./4t/QEnfMalvhUcce6jjLRqNeuM2tZxooKQ9P13PyU8VUm3W",
      unencrypted: "1,2,5",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177610992": {
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$TMnCHqO4qvEZEGc/gWVyJe831Eey0zVx.nFf/7U0NHOtopQO6hkn.",
      unencrypted: "1,2,4",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177610993": {
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$4XPbh8plOnZl5Kl/cTyZNOSXYRB93WCYnNAKpA0RpTPaTk9cnFuPG",
      unencrypted: "1,4,5",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177610995": {
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$DEmfiYScnxE422yo/zWs3.HPkdWiqmBHyNDra/PvF8KeAG4ri1vA.",
      unencrypted: "2,5,6",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177610997": {
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$J6r5AdD2cq0rANNdi8njpulYaV7xNynLmD5Sj.snVgrhBfrgED3oW",
      unencrypted: "2,3,4",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177732173": {
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$EHq97Ps.qDtwZdJdx.KI0O9B6CYbysMtuU3D8KRx8SOpsWu1CR.ty",
      unencrypted: "1,3",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177734185": {
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$jw.kLJbjeIro5lboBKCx3.KOoqBZjmb0MpBLKFqexNULFP9jlQAWi",
      unencrypted: "1,4",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177734189": {
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$7zuNMU.HuO6TLh4Yk6TeHOobrZpxk4aM2GHUGadkMc1gOWkIW1Zk2",
      unencrypted: "2,5",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177734217": {
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$Yq.u.Uk6sXgWKfO49oZFl.Zsv14bqVdY8rSwZ1RIGWo3/uNaVvJO.",
      unencrypted: "2,4",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177743573": {
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$LrSAo6SiROmzar3NXjDiEeMLndbH..vKCYZitx6ZpDeE9CtB9sil.",
      unencrypted: "2,4",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177743614": {
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$lZAbpy7Daq4/XsTzlrlJneXpWFogw3e9Wa4DAYJktP9WfhYA0v9A.",
      unencrypted: "3,5",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177743637": {
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$oQPhbow5zvHvEOTvq5jiZeL/m8J.rdlwD1JGvQkw6p.lSlabRVoMW",
      unencrypted: "1,5",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177743642": {
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$YlNpygoo263QtICAxlpxfu4xdJsTu1wOSdA5LXr20SdT.n3irCdXS",
      unencrypted: "3,5",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177743924": {
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$JUC/uEfzzxs9dppL5oHYXeymuDxU.Dwy2X2ucuwCJEK4bYX9IuYJu",
      unencrypted: "2,4",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177743929": {
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$hcV/ZwJ.KFaXoc9HK5Dbq.vcvXMJn5GBPLXXauNsNQEfMb8wcZC8u",
      unencrypted: "4,5",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177743949": {
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$zPCHNJQswdizX4wXJXOJL.iYYTEL9Q337gOl8/Eh0DEIveIqKaleS",
      unencrypted: "1,5",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177743982": {
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$B8lqBsXj0oo2wb0BiWDv0u9j2H0TFwn4/ivb3/47/F/IGKKyW7cYu",
      unencrypted: "1,5",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177743983": {
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$59UiD2jy27USzureIW4hL.ZHOeg8xYzhsogiHoUh4DnfcOmuCR/Sy",
      unencrypted: "2,5",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177743987": {
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$zoRllK19B5ZjTABHnNJI/.UrPn14drrH3RKEsD1tdO1B78GLUvQXC",
      unencrypted: "1,5",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177744063": {
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$sDsvovKfPdWxyl2IMD5.gOo.UpnbNqbhPVF374p69UYTpvvvXbGSK",
      unencrypted: "1,5",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177744064": {
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$bOjB8XqgFJEV/DY0mVzRXuL6hy9gnm4qeAg6Rmh3GjVPfBISMAXLW",
      unencrypted: "2,3",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177744094": {
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$XYiYMZLD8Uj.8b1prvoMo.wGjSD4EQxWY0ACYay3/9YT1QvyxqVhe",
      unencrypted: "1,4",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177744655": {
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$x4Qd8aIC5coHNK/B3PKm1uQ7040D4WHoOPNIj2OEZgQAkVSxY5yl6",
      unencrypted: "1,3",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177744813": {
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$eo/YZP4JpaFxr896MV8lBel7HWEFZU/S8gyf/gfHWA9vhFpNx9kgu",
      unencrypted: "1,5",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177744835": {
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$TYHko/1CFZtbYJNGQ6QZ7..jyopzLSH6eFJ8PxIrJ8KJmu0X5OWki",
      unencrypted: "3,5",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177744853": {
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$KQTsMALh2f0DMJq44ZLruukBjiv3CJo5juwlYI2lihMl22JeC686e",
      unencrypted: "3,4",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177744896": {
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$Sug6fT/Kyi5BloPPWhcqRuzE9l.TF/XRfNf4gLlQSrhHYmDS9ayxS",
      unencrypted: "1,4",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177744927": {
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$giDVuLHuNs7Hrm7mUV96SuI3p7572d6PLXRtrDZ9iC.iXrd1GW0Wm",
      unencrypted: "1,3",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177744960": {
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$jcUkePYSFWmVU3o.auKuxuqGfOFsxdgSGOLFvkTSCbF9a4dKWDKqG",
      unencrypted: "2,4",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177744989": {
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$naCyKzmfHlePTr4mnXRXM.IJE2V7zdam4fr/b3KwsatI7UiPqbsHy",
      unencrypted: "1,4",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177745005": {
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$ky/HQ64fXXYBLtU/.Dqq7e9Ihec8wXmBvW375I1OBZF.wxdKLnE8.",
      unencrypted: "2,3",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177745026": {
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$.FOaJzIMt0GsdhXjnP5WuODTQUHRFgHNR3sR8GQjtPcMbTQaTvVji",
      unencrypted: "1,2",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178620232": {
    responseType: "multiple choice text",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$VAHHLRLv.5kx6n8iJ9d03u920lhMwy5fSnvYpXvS856xBm3jxR80C",
      unencrypted: "large",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178620289": {
    responseType: "multiple choice text",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$ZYKEqpMv77.xBgELzi4CdexeYW.pOtcRQGfvHGnkNGuva6oCaur96",
      unencrypted: "sleep",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178620444": {
    responseType: "multiple choice text",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$zDUqTIyRLOr8S59jx.52ZOd7/aWPQ6mqnsp8lplT.IPEoelTRNeua",
      unencrypted: "mad",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178620461": {
    responseType: "multiple choice text",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$Ee0/xOE6khjSBK0V1J1rO.ccgnto8ZaFIyWlAwllKw4W9XSwRHXxW",
      unencrypted: "tiny",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178622900": {
    responseType: "multiple choice text",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$OJKLTg11WZm7u86sXupvou5H3W8hctQDd0aFGkBK5a0JdohEG7FHq",
      unencrypted: "mummy",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178622905": {
    responseType: "multiple choice text",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$wkI.PDoBa94bsGZNDkHJQulGMX4NAaT8uUv5ndZgmCLcWb98p2Aj6",
      unencrypted: "laugh",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178622911": {
    responseType: "multiple choice text",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$5f0.J3kVx8I2I93r3MH/Y.R9ilt7hH7Lmn0q9kL7LCgeKLN44w632",
      unencrypted: "see",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178622931": {
    responseType: "multiple choice text",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$jmv/JVFwMRzN9KPdpuQG9OML60sCwuAD1pp6kpLwerBawYKlcq2YO",
      unencrypted: "cook",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178622933": {
    responseType: "multiple choice text",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$pTxoln1Wp.JbYOI/X8ZuY.OmqCvoEORGGeeiOlbKLQjsTifVxEkqu",
      unencrypted: "gorgeous",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178622934": {
    responseType: "multiple choice text",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$IUCRAJLlTtzIUydQQcvnWeU5ZR0eSg4NYFxVskDKLxyVv6jgsjxnS",
      unencrypted: "grass",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178622955": {
    responseType: "multiple choice text",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$ikK3BUhBTnTX3O2Zkt8fI.B/vu4bl71Y08d90lepk6xSi877gJVti",
      unencrypted: "vehicle",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178622987": {
    responseType: "multiple choice text",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$W8sPdCePyEgCRhVGMRnNxuMx7H.XMlz1P.4lpzt0rxmSjGrtj/8R6",
      unencrypted: "road",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178622989": {
    responseType: "multiple choice text",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$1j9qos/3ybHWVBJOqQ2rGuiwu94hi8ZjPT60crWScmybXVzlqLPN6",
      unencrypted: "try",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178622994": {
    responseType: "multiple choice text",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$908kxn0wEKqXrLwzgf5e9uUjkmNIk1zvg8BzDT95PINvvGkIwuqha",
      unencrypted: "fake",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178622996": {
    responseType: "multiple choice text",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$tfuvdL8jzra23JLxFm5vMeZ/Par5vFMXKCX1XBNR3Di4Fi71Opfxy",
      unencrypted: "consume",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178623000": {
    responseType: "multiple choice text",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$C2z5g3IPP5B2Ggf2Fqjr2uX/X1rp69xXWA0ClKE/KDvSoYd19iR4G",
      unencrypted: "feral",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178623003": {
    responseType: "multiple choice text",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$q.AuZAQqiADwSS./sxnXe.vnB2kL81et/dtK/.nwLHn8GB4BWT19u",
      unencrypted: "hide",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178623004": {
    responseType: "multiple choice text",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$NcGNffh8bcCjqKik9A9awupneAHjVyhtysVd3pS7mZaTYtyuYNHxm",
      unencrypted: "piece",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178623007": {
    responseType: "multiple choice text",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$cWU1uJlrCpGsu0x3ZPb2Vug6pmVvI2He9mN9eVnWazu0k8q1k69qO",
      unencrypted: "bright",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178623008": {
    responseType: "multiple choice text",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$vyUM1Mf.Fz7P3PJJfv3maOuQMOAZ3EnC6G6JSne3Dlcqd4zd5Zwu6",
      unencrypted: "calm",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178623011": {
    responseType: "multiple choice text",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$0n/UMVDoZpSyJByozAOVJ.3kmw0JZSFVEjIvLpDhuiWntQL6oEAfy",
      unencrypted: "persuade",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178623013": {
    responseType: "multiple choice text",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$PHfwg.Iob7aDj/RyOUReW.VUo7BFdPcBDnxguoWqj9G9Vv9lBuIv6",
      unencrypted: "surrender",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178623015": {
    responseType: "multiple choice text",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$SKj1NKpqkNPYiupsD/b6quhcNSbu/6Exu.CdszMmJjlXbwfmu5uae",
      unencrypted: "scarce",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178623022": {
    responseType: "multiple choice text",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$296NrWT2wrmUiBstDgFNKe1//.mG4Dzk/JFRC6C8O.V1xwrcRcpr6",
      unencrypted: "perceptive",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178623033": {
    responseType: "multiple choice text",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$mcVgcPvIPdSPfGd2iYy3hefWF/mZ6KU5f0obbo/yALsiC.xkHYxnG",
      unencrypted: "unclear",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178623043": {
    responseType: "multiple choice text",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$7oNz6IbAVOAIiVcf5nf13eKAKmbKiV5Mw5ZR01ZiaqiPpGTAf7/0e",
      unencrypted: "renowned",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178623047": {
    responseType: "multiple choice text",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$VJACW0tXx2H9oCFRSnhQuelw5tvf8yqe9X2UEd4p0gylqLBBpML0.",
      unencrypted: "rebuke",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178623049": {
    responseType: "multiple choice text",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$eQBdzUGdxMPtDK4/LrjZv.xZu/fXyo4fcNMxD9JQRk2tQNTh9LslW",
      unencrypted: "severe",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178623050": {
    responseType: "multiple choice text",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$HVBIcg4iddEzOWa.GLmdKOri8vmXiu0fyeqHs3vHfjAnYoC04bzOq",
      unencrypted: "bright",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178623052": {
    responseType: "multiple choice text",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$.mk41veHukiw.d8uRomZ2uPY34nkWKCIUaoXzG215zjCQIRlLCtJ2",
      unencrypted: "deny",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178623053": {
    responseType: "multiple choice text",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$gyfRmcM2GCDV8fIYoBlcxu/J4HJj7WVRedG/5XHUE4YwhxxhRxHzG",
      unencrypted: "harass",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178623056": {
    responseType: "multiple choice text",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$YENMHbSYv8kf4NkgNjlxmeY152SfwrzUE28XBr2NAS8KAP9xyVoqO",
      unencrypted: "bountiful",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178623061": {
    responseType: "multiple choice text",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$/gZQ4Pze6qqTixIifp1kK.Lg95EHl3dNRX4Pgwuj.jI/l818IFNaO",
      unencrypted: "radiance",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178634773": {
    responseType: "multiple choice text",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$0IwzPHJLQHmdpm7mNzSeTeNbWEp9.RIH97sCprStsl4vqNqaaoIhK",
      unencrypted: "no",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178634776": {
    responseType: "multiple choice text",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$9LugfIJqjPybAbccwlCs1.DIGjlsLc4KApufMLI7FlT64ea3RSSwq",
      unencrypted: "down",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178634779": {
    responseType: "multiple choice text",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$92LDfqhV0fXNTrvhSpAaA.L7KCypFck6QgSJwsrm1wVCL.PVp/EuS",
      unencrypted: "bad",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178634781": {
    responseType: "multiple choice text",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$iaeDlV23TJqM/yWraYfMV.kxzSwv4saWJ6tWpcNwtHcEjw4KiA09y",
      unencrypted: "stand",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178634782": {
    responseType: "multiple choice text",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$YaiRHV7fLDxPJl4xz7IpquVHhzCCmvqOhUx14hvLfOR2pi/s/NNwy",
      unencrypted: "back",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178634785": {
    responseType: "multiple choice text",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$oG3HYoDe.xdGuevYhcvt.uVjpNTV.pJ3sZrJrIK2U9.MVcKgwwlrW",
      unencrypted: "young",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178634786": {
    responseType: "multiple choice text",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$fkrJdhyMCH6C55/K7lorV.oEANSnyzZo473SvnuWcMeJ9UO5ORDtG",
      unencrypted: "small",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178634787": {
    responseType: "multiple choice text",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$ZNn0UufuIuIN7KPsOqme5eVuEDMo03eg2i.cgi3rbfDYCA/WT6oqS",
      unencrypted: "sister",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178634790": {
    responseType: "multiple choice text",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$sbSlHy8VYQuLiGANqs0ejum8FwI520v7uF7D.jGlAzab.Tpzv92Lm",
      unencrypted: "cold",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178634791": {
    responseType: "multiple choice text",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$83uwOw1uU.7tw5oLDw8hvu0cS1yU40Xr7IBSng0s7L7haGjI5JfnC",
      unencrypted: "girl",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178634792": {
    responseType: "multiple choice text",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$R5OK6qHvY3/lBFK3/QRgZuUi2bhg2JzkNHYzbtRbCkyAShmYdHknq",
      unencrypted: "deduct",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178634794": {
    responseType: "multiple choice text",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$OVdM8Va9E.BBJ/hM2s1q3evpV0PbJQd7B7t9JOicvXHCMB7BvK3Ni",
      unencrypted: "answer",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178634795": {
    responseType: "multiple choice text",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$5W3kXMZJh4dp4SROCpQhSOvXKEM8Bys/2TzxLY8C4yVVnXWOIokby",
      unencrypted: "rough",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178634796": {
    responseType: "multiple choice text",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$37geQiFE1XlE25TtT76lq.aAXOLEfXtexiNAVmDwsItHFrtqkBEw2",
      unencrypted: "foe",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178634797": {
    responseType: "multiple choice text",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$YdCSEG2fp5gGW3WKCqRD9.zLxNadL54bDGlL/bqXbBO6rRWaDW4gG",
      unencrypted: "blunt",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178634799": {
    responseType: "multiple choice text",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$7Uu2cKqT8tiIvwXnSCAN4evlujHgBZwKdlTK6uUdDyPEpGNUq5PYy",
      unencrypted: "laugh",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178634800": {
    responseType: "multiple choice text",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$FPpkAnV9kW/3IWDmHr4GxO19AbUm6g6.dR7/tfEmC5AK0PXn0XORy",
      unencrypted: "wide",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178634801": {
    responseType: "multiple choice text",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$Pn4QPfdeMIp9KX8Km9nG3OEbnCscyvG334Y.MrITtUdxQqWjG0VNS",
      unencrypted: "singular",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178634802": {
    responseType: "multiple choice text",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$AnCrDK8298Sm6gZ0vdSguOPAwOUFH.ojVzS7kSbnBz.6P3KBIaunq",
      unencrypted: "bold",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178634803": {
    responseType: "multiple choice text",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$ekLqjwAupu4Hdx.wZSPHS.jSFZVJglQ7k9Sw6NuIYW9EO0RWCrUiG",
      unencrypted: "masculine",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178634804": {
    responseType: "multiple choice text",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$IAofvBTVCy2v8S79e4fSSupbs8USi7oySImNIaH5BRk7BQt8a7jhW",
      unencrypted: "energetic",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178634805": {
    responseType: "multiple choice text",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$dq9teO7E6otJ25KFwmqrI.a9mvCfdEMjGDX7u1D8ELFpEb25LEnta",
      unencrypted: "specific",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178634806": {
    responseType: "multiple choice text",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$BtyLavycRhdWenWIfniR0uGGXX72V7QkCQ47rhfEpl07.qoyPi1nC",
      unencrypted: "dissipate",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178634807": {
    responseType: "multiple choice text",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$cXLmZJ3Me0zZWyorDYUm2etCBy7PlkYGDuosnxamqQ6KlXYD8bl8i",
      unencrypted: "flexible",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178634808": {
    responseType: "multiple choice text",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$Im.nSvY2Mr6CBJHi6JEeMOF.BdNI0JRbQPhzkv4zS1dpdcpdvHija",
      unencrypted: "former",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178634809": {
    responseType: "multiple choice text",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$dSwEMUNAc.vjOutH8wFyTuq.cg5VpAVHWVxNH52DZ6SMnZs7hQxKC",
      unencrypted: "agitate",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "*********": {
    responseType: "multiple choice text",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$d3BkhrS79LlqK9kx/Zhyg.ij5qVwTaxSYWwWgoSAEKiF0KoomXJci",
      unencrypted: "logical",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "*********": {
    responseType: "multiple choice text",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$q6AOhvww.UGXtdVPbT6PoOms63Tf1uk2NmCCG3IxOZTA54WbsmBs6",
      unencrypted: "outgoing",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "*********": {
    responseType: "multiple choice text",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$NJuwOgi9uJB3kHFcqVKq5OXJBNU5oNjpIAoyUsU8qfTmLsC59igVK",
      unencrypted: "weak",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178634813": {
    responseType: "multiple choice text",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$oissEdyvDPFtExkSB6m8nO/XlCAD8940rMAl5P.yaM1.lEK/8rurK",
      unencrypted: "elegant",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178634815": {
    responseType: "multiple choice text",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$3rClTT9dozyipHccpWwnnephH5k9PvNJr7QOqsU/XzqG4Cd2hj0vG",
      unencrypted: "amenable",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178634816": {
    responseType: "multiple choice text",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$YpOcoMoe57fpNrMLg8u5Ze9xDqVNtT1Tv4Rm5Iaa/8xSmPfdFwfTu",
      unencrypted: "unhappy",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178634817": {
    responseType: "multiple choice text",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$HwyYQma8GJGFrLe//6iCKuBjRwLrP/.J1ENWzprBQFQvwP1KrLqzS",
      unencrypted: "serious",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178642178": {
    responseType: "multiple choice text",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$BOxK4d..l2J14yzAaIpL.********************************",
      unencrypted: "exacerbate",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178738937": {
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      isMarked: true,
      unencrypted: ["3", "8", "6"],
      values: [
        "$2a$04$1YBm0AaoQDEXWIDo43DzXu4R9S5oCenWr1Xp..OrMJ8qomtmP8Ds6",
        "$2a$04$cigbu41iKMZsFDH/Xj0nWe/Q950u6N5ZJexZPPTCMxS9Yr/fvPjDa",
        "$2a$04$Q3NrgC6S561vwDIq1P4utuiixSgB7EP91S2mU0u.Af2ReW45dFUYy",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178738975": {
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      isMarked: true,
      unencrypted: ["7", "5", "8", "2"],
      values: [
        "$2a$04$jybqbdDsrnpQmO.QVvxd/eStWZpvlo.QyDhZsHrz/bI8MX4oMquVm",
        "$2a$04$.rXeogHFnpCiDbUielp.pucb2336wiXlUqAh8vAUWx95YtE/3Duiu",
        "$2a$04$ouLeiNJqxq.TmF3rLzWS3uavLW97Imzvl7kGB6rAvmKh/DVBwV6lS",
        "$2a$04$Zc5yYgdYQyMPGcMqg8Yo7uwqcCWIQjzfNAOztzsG5Y5dwhEsz2tuq",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178739040": {
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      isMarked: true,
      unencrypted: ["8", "6"],
      values: [
        "$2a$04$ApgTAFg9RNxK/JQ9coGnqOjyjkE2GZNqw3JAAH4wN9sRYWE3MezKa",
        "$2a$04$nFlvrm54UD4zEHeg6.aFDONlTqM1QDSAoePUy1WbYHFXNU8LkYS.a",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178739449": {
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      isMarked: true,
      unencrypted: ["6", "4", "7", "5"],
      values: [
        "$2a$04$rn2P4DMaJCxTNmSymTMbX.3qgCqUpbpC.eoqlfXhcnRBDy/gSIyie",
        "$2a$04$SQkv4ZL7pWhJ4ru07e31bu1UcNK3d/bakvonMm843zNuY/WI102vy",
        "$2a$04$BxB8QzVcW/ibswA/FGU6EO9JPV1Kccf3.SBwoKp9qvLiKNbDAODwW",
        "$2a$04$J2lKM57txMJc/lOaOeK9Ne.rP7D5AF2Iuq.XKqEDirPZDSCye22Mq",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178739580": {
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      isMarked: true,
      unencrypted: ["2", "5"],
      values: [
        "$2a$04$yKDNDgrR55bkbZPOwxTzM.c1wAI24m/CyUHymo6/LcP5A0uFuJEye",
        "$2a$04$HtKBMvR3j9rJ5AFjKJyk0uUtcxFWqWYIGg3T6xYvmTZMTxEU2iIKu",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178739581": {
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      isMarked: true,
      unencrypted: ["7", "3", "6"],
      values: [
        "$2a$04$xwr.oSt6pH1JdzjMw70Ysu8MRAHoDK9Ykox9LKsyHbj2OTF3y3kha",
        "$2a$04$rgI6Mw65dA5StNCX.1DFlu7VPF5gfuML.K08rfrIbaaWLZkHXA/cy",
        "$2a$04$NwnlUIIRx5xodTyIRUaiw.CMZZwZYfZWE3PaeNc5yZALqRSik1yia",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178739598": {
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      isMarked: true,
      unencrypted: ["1", "6", "3", "9"],
      values: [
        "$2a$04$xMPr09bLgaC1j2y8ynfNV.pHySiyD.wzvwKSS48AT5psuqEp/7vdq",
        "$2a$04$jyz/nJfRxH5Tf53dQLqhNeXVSAWi9dwdbL5DeNuqhAUF4IkxzvwLe",
        "$2a$04$.PDXyjEOdRhR39uGClLBuOPsjTp9tPEFBewDVdfMwufQk2kq48KW.",
        "$2a$04$XAV0utqbrMsl4XNqImq3juSjroQyJj1o.b32azP5qwwN9XUsmLQ3W",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178739600": {
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      isMarked: true,
      unencrypted: ["9", "3"],
      values: [
        "$2a$04$rmg8et4GsRaGndOLGUUNbuRqxyPdwuju121bZRLcQKhwFq89ATLFq",
        "$2a$04$p9rn0cNsFhcqjP2jvMpG5OK1iwO3yASl6b3jBviLdjIEGEnADkoOm",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178739603": {
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      isMarked: true,
      unencrypted: ["4", "7"],
      values: [
        "$2a$04$SjeUEk0BIGf./i3lsfzbIOD.QBNktWtncDzoihrXoOOlUGfDjUTXO",
        "$2a$04$IpLvSEcO5ZGyvNIzldmPy.9oXtN0zAbLQsLc8EGdYJgAaxb4/FSRS",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178739605": {
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      isMarked: true,
      unencrypted: ["1", "6"],
      values: [
        "$2a$04$O79zUf3yg7HqVAt2DbdK/uiYoM04RmkfuxD0GKKIc.kh3VKOsi8Ly",
        "$2a$04$JKzlAVHTfwg8My6jnU0ZA.Pivw/KSoblLSMVSwVlOb/f10leJRysW",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178739606": {
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      isMarked: true,
      unencrypted: ["8", "5"],
      values: [
        "$2a$04$TfPlbyeXHJVY88Jyjf1blOg.FN4mg3MRv1mWRKLjNuueT3.FL1o/m",
        "$2a$04$AG1PJ1lvYgrMQ9O1h/8f2egO8n3h3w8InYRUEf8OXquLnMUrc24RG",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178739607": {
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      isMarked: true,
      unencrypted: ["3", "9", "4"],
      values: [
        "$2a$04$VtE1g5Blwue0Rbma1cr3w.yrJgBnFCvehXIdjGs6pCXGys8p47T8i",
        "$2a$04$u8nQ8lUsFLO2x5swhqMT/udmzBmm26fZnOdFotTLjKgdctHiKn7Ya",
        "$2a$04$K5i.AGYIqv8Fvw/03cPOVO6I7Cvh2fAjPFXF3gMrxR.iZyfkm33zW",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178739616": {
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      isMarked: true,
      unencrypted: ["8", "1", "6"],
      values: [
        "$2a$04$oWK1qtlmJcZ7PP3riwNUG.rjvrXTUj8l0mIrt/zkLRkssMwwqTpAS",
        "$2a$04$DFyup6780kEemcmErr8o.OWIE45oh5NLT.S.SQ9oU2gBj/EK8PkJS",
        "$2a$04$H6xl2lzpDAmOovUNqWk1EOaFrwoV27LCXSs6/2zf9FEPlKHd/UqWa",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178739617": {
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      isMarked: true,
      unencrypted: ["5", "9", "2"],
      values: [
        "$2a$04$GGcx2kIsPyyAyJJGEGFtxO7x3ygPw9Kae/ei/jKddeWXdpuZ3UCva",
        "$2a$04$xcvRW.TEO39pN9ocCk31aOWnbfaPwlxBgkv52L3E0co0LTN/WQuIK",
        "$2a$04$QEsMsixegTRA4cE24MidzufHgIrhyfEyUYIheu.sdmKJCCfpWGGIS",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178739618": {
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      isMarked: true,
      unencrypted: ["3", "7", "4"],
      values: [
        "$2a$04$mjrmwZnYEDSuDd1eIBi61uBigV91/pduCfdumDTwBPoMWxeZv/jgO",
        "$2a$04$iInIjuRD2o0GOqnU3/UQ/.xUvbB8UYpNkK/7KrFLa195K25aSqo3C",
        "$2a$04$IXlyoKNNYNHEU7yrY4FUg.sgvzu9PnMzDm92G7578Am2VcseK72ka",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178739619": {
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      isMarked: true,
      unencrypted: ["8", "5", "2", "6"],
      values: [
        "$2a$04$JM3nuoXFnWnMDGWMFXtFw.4Rzde1sIEZOu5Ir0rWygDHCheCotWXC",
        "$2a$04$p/nDObFoQDQdZh92ANK/muvR.TqwJ0W3rI3hZV.4/jVzoKns66Xpi",
        "$2a$04$N2j3nUXK4kvp.VhkPKppcebz6m703uH7l13c9Y.GrzW9ufy/2Cs1q",
        "$2a$04$m/kggeVsB9iMzeMaa32DsOt1ewtTCztFk7eQf3ueq9say2GP4KXO.",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178739620": {
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      isMarked: true,
      unencrypted: ["4", "7", "3", "1"],
      values: [
        "$2a$04$QWOLw4AGZNsX7ZwJSRkaPeMFUfhT0.klSY6U4rNhsv7TTjnkdNaUC",
        "$2a$04$Hs/EaOIKGPc2DjHLraD/LuZOXXR8nQYq5Djtq9xsYKf0iIGvxh17W",
        "$2a$04$ehK.0bOD7MogVLPvvcNrNO0SISD3IR7u0dSiEW6dVOUIGPU/tgApK",
        "$2a$04$eNCMpYWQi7XjCl/knh6IJeAQebi/ucD0e73Vuwbq5FV6DgEPXmDVq",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178739621": {
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      isMarked: true,
      unencrypted: ["3", "6", "2", "9"],
      values: [
        "$2a$04$gwvwtjz/Pox/5fkFt5xHauOG/J98.VuZRcMhSRNcE4Iev/X1ZCsRC",
        "$2a$04$qG4w1kQgVziFOh4w.0sXP.Qq2M3HUUea.ZjqyygrXwX6E7QfSLyAy",
        "$2a$04$.G/pDotZbWnunF9ISkD74.bF1Xh1G5ERbOLkCqjDYu5Z2/l.7loWy",
        "$2a$04$q2Y5lijYni.3F5SBKS2cSu/Dg5HeeQHUsbrFOBdaPEqPUoHxiCrPC",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178739622": {
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      isMarked: true,
      unencrypted: ["9", "5", "7", "2"],
      values: [
        "$2a$04$33v9CHXxi3jpqlF9OLDGYO1OUbVe9zWxOfRVyPjLQrD/ubffUO0gy",
        "$2a$04$1Aod3aLmtdBr3oXnnyNpMuKDa76UIwjtT8PQbVZ2e5w./2asXLJF2",
        "$2a$04$Rmsq.UU6iNmPu/MYRIomJuiTyrG44IMi19aZi/RAmuEUaH9ItbFK2",
        "$2a$04$rTWLAsxWF//YU/Eo0GNvwesof.9uG8epUI54xg2u0DKJBCFPUN2FG",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178739624": {
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      isMarked: true,
      unencrypted: ["5", "9", "2", "4", "7"],
      values: [
        "$2a$04$7fWOHgfj8kTMMmSOduw/n.nrKhcn2mbUbcuAgKQbSuOFV.JiOWA1u",
        "$2a$04$DS6Mvb9zAsWLoyvvgVcaOOWYMxJXcgWOCWiWA6LZq1OZvrerJd4AK",
        "$2a$04$GZM/YT6uP81e3pvEm8B54OB5ojDP4Q34mSzuGv72bwp4KtZ0s.E8y",
        "$2a$04$D1E5dtX92bnT7b8wMYxkluL2Jy6PM4xtE9/FXPNfvo9NZnOTFyGXu",
        "$2a$04$XVD07GEwWHdCzTSMCCQ1Au4Vbt/PxFlsdZsn4Ylp.RjX1vPt4/c9W",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178739625": {
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      isMarked: true,
      unencrypted: ["1", "6", "4", "8", "5"],
      values: [
        "$2a$04$at4mENqoHPUdjxaje4CIPu0YVNLAEOBsb9A1gpIzLLaDzNnbvDQqa",
        "$2a$04$iQEnh3MmGYwneLJtqwDu1.68R.CUY4BLsTblp1FJn2KlCSNgvxbNe",
        "$2a$04$VJkXe5xlU4rscbrY2cOUNOAg9u4jJ5X3FxrTPiUmn34.LIzvpunc2",
        "$2a$04$q2XJgmhp16brXoYyuCG8iuYs9hb.YG/kRelNam1yvCzstEx4cwxSe",
        "$2a$04$vky6fsoeZPGv0B3bxNn9ruTAvQNZ1n8D3xQd4/LIh.A2q8Bpt97t.",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178739626": {
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      isMarked: true,
      unencrypted: ["5", "2", "8", "3", "7"],
      values: [
        "$2a$04$Dz01SFxka5fH3YLBW2UL3ONw6ItQKiRnUVulNs2QipXHVQxc62eYa",
        "$2a$04$IBcPLcehuUWc8e.JZDzlzey41fX.5e0AOQ/tkV5fPd24qAkVbNrPu",
        "$2a$04$KvarKpJ8xAXbxJoxPu6ELuidCBCr5mVXCcjfPqbMAko6klbcMBeZC",
        "$2a$04$Yu5XZpPxm.FK.22pAn7.xuDiivnuji8MzY.IjCqh4PL6zee2T9xt2",
        "$2a$04$wb8GMT2UZ8MqyLFfjL/joerlaIdklhi1ZltM7T8l4fy1IBQYXJBtG",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178739627": {
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      isMarked: true,
      unencrypted: ["8", "4", "1", "6", "9"],
      values: [
        "$2a$04$A15lBf0zqaeTPhEpZptmjekChkxqJrJVnbmzNrp6cxei0NgIjYSlC",
        "$2a$04$ivOWt7kV.FxAavAZhOgh5uMkiA15DbXjtSSclTsesWra/HvL1TCpa",
        "$2a$04$qcoHeCsfitQwGPs2uuAWmuzFGM4jWmkqrrcicuM0ADxBGgEBYMiXi",
        "$2a$04$FLRN6Fb8pLtKTLUirsbl6.hC8UYsIZkaQHBPUQ/GnVI3vxQICnUc6",
        "$2a$04$cZLoFsgTPpXEYa6pRFx0nuNRneLNqLG39rEY.zYfwf4GL3LMcdyNK",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178739628": {
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      isMarked: true,
      unencrypted: ["4", "3", "2", "8", "6"],
      values: [
        "$2a$04$W7fNul8poXvZpXW6Ma0YMOE0a68ZbtTaUmHacrWhgfvsTJAVenLcq",
        "$2a$04$vBty6E3sZMhbHvK7nddiFOG4qK/CM9MXInyjK2KV.X8./WqSx.ddG",
        "$2a$04$DUGKs.FvAAZQ4pkJH0yLR.QrmVfA0GqH5csyv0NbeA/9LkGAWmwbe",
        "$2a$04$Mv0eOLXk/GR8pPPy/K4T6Ozw/L3y.f2GfaagGg7fEJDE.qigOMn.a",
        "$2a$04$NgDMgwemAjO7unYw3erSuetEDVuZo4OSy9AEKmgg1CpEXR3zzTKy.",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178739629": {
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      isMarked: true,
      unencrypted: ["2", "5", "9", "3", "7", "4"],
      values: [
        "$2a$04$Q5MC.bM.cksoFl4kAJIaiuRohaiix7xZ68wETRhCz6cif0/ZPpiC6",
        "$2a$04$wBWDCF3pXoDDxenYyMgIKO39ts9pqHW82m9pRFcf4gpNHThlslJUm",
        "$2a$04$MK.A53PrgKTRerSvWohSXe/Wt1TC/sbp3qKTDb.ji7HIdlS1Z31EG",
        "$2a$04$K5NjY2s1kJs7YOhEPTd3oebY/yHLt0KqCoWkNa0Ypo3Cc2I3CQ0zm",
        "$2a$04$4blqTiTmHM7LUDQvLQCJU.LmUhfon2NVEiu.wjxcMNSfmjZ5WUFL6",
        "$2a$04$wcZ9NeH7PnIESP.L8wzTzuyCnxf0X8IffttGIkE/FurVtfc9UqVAe",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178739630": {
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      isMarked: true,
      unencrypted: ["7", "3", "6", "1", "5", "2"],
      values: [
        "$2a$04$LUzNQgogIkEE3e.PeyGhUuD2OZKhxQRD8b4Me.i0zJvr3ZEPCx5Ze",
        "$2a$04$FeZ077vFdI1b.C2YFJjxO.0NIHVNhse8MreWsTFTfg68BCPwKj33u",
        "$2a$04$SXBIS9kFqxvtf.m/H3fhfuUz/w2hp0CiENMtCivF7NvccL29FQziG",
        "$2a$04$k8ov4pYQha1Txkh5DdK7.uxkSRy5tWG/wYHfNik31IE8lbprCjqwO",
        "$2a$04$1Z2jsiSeo/ZUFJWtHNgMPONS7kWvjHhzZbFlkffxvqQpNrvB9psQK",
        "$2a$04$71ilObOE7ltzHPQNWXs5YOsKsLhDHGFz2XGYw.W0ll.q1n736262i",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178739631": {
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      isMarked: true,
      unencrypted: ["2", "6", "8", "5", "9", "4"],
      values: [
        "$2a$04$iozbyQM3clUXJu6SzeD8euP1kTDru0GPl78u7qfK/pFoud1ZowMBm",
        "$2a$04$5ZI848HhFnjZUHLqw0x6OuBqTkScuOATyxK84k0Rt2KggNEotNwqa",
        "$2a$04$WCeaERELGZLiqCPydqZDg.KBy2fjRwuoU0mi7XRyKBELe77PR1roi",
        "$2a$04$bl1ZPVy2pxCY1naAq7aZ1OXWtCPf4kjq6ijCckjCmZhoRowwjaYmG",
        "$2a$04$1enZGWCYA6zCEX9cZntJPOwyEqgWZNxezi2DleMIRQh9s10iWbVLu",
        "$2a$04$rBJCAeB7u4z2hI9TzJ0u8Obsiitj5wvvdOHIchD5lbguLIxvdHMga",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178739632": {
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      isMarked: true,
      unencrypted: ["3", "9", "4", "2", "7", "1"],
      values: [
        "$2a$04$YaKWQqtHs.q9g/ERER.dLegoGwnDoKvHjIQqpUTZobVo.URc6yYg2",
        "$2a$04$fYPFgs5EB99ZvayONEbmZeQvbkZUBkGFAz0oHkCDegDuo/2UxLmj.",
        "$2a$04$HStEFWwYgOwLOdMG/Cuv4e7goIxFi5e8awCq3PcVzNFwxENiWvcGu",
        "$2a$04$3ONo.fa4UgUM5r3qX4omieonZy38mLcalYsqWXsh2GKk.53OuWH82",
        "$2a$04$AsMoS7G9xZlLAa2qrXbavOqrOaL03TWWw0aDbVIp/awYCeG5xVzWm",
        "$2a$04$t3c6dbZX4RMHiiUcQtLD9emw8SHbL21j.B.f9J.7JMWF7Tzu3MCzq",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178739633": {
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      isMarked: true,
      unencrypted: ["8", "2", "7", "3", "9", "5"],
      values: [
        "$2a$04$/jAKPN7YhwHxbhpFqwg1u.1Eox6qS4tUL0p58R1uPm4W9gueAptIC",
        "$2a$04$UDoMNIhOyuzYSUQA5FVYbe5heZ1ZgX8CxJEYr0AVXb1gl51Spbj6u",
        "$2a$04$MtKuFQ51Ts0qZwCoe2e9uOUlH7CdHCQ4SENhXEvzJRnn2csACdujq",
        "$2a$04$jnI9yTKvSCY14L0oXGpjPOPbFcALsjGeOegtt8rDmaTBQkRzWHJOS",
        "$2a$04$X49HiCPhgp/mPaq/uRzjcuTEuBg6f8/usqQqDEGNI1qoHD556GHJS",
        "$2a$04$YSPVp1Gb4TL.xqjTBM6KCeCYGt8nDoqD9w4x5UvBte4IpEl1uG2H.",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178739634": {
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      isMarked: true,
      unencrypted: ["8", "1", "6", "3", "7", "8", "5"],
      values: [
        "$2a$04$qnVB5rawwUC7kPqTde7/VuR.KeXydb6SVXgqwamj1ZoKeIu8wDdWS",
        "$2a$04$PSFKVdEv78dSLIFeA6ni7OwHN6h444MtAkRB1GUOinmeaWdbg0zqK",
        "$2a$04$IZBySIr10wnveg6h7qZ5wOA8lFr575z8FDvQms3kD3xHRxmUs47dO",
        "$2a$04$AmhemRDvgp9RoliA2W1HreJZA1b9mVaeaRVFqI78sODn73ObHNqQW",
        "$2a$04$MJkDWHkOJI1aatnct7PtBeaYLtVf7UDas03jI.GolAzjOeiqueCl.",
        "$2a$04$BNtVqRAC8PFHT01Kn5RyI.lYgKtjAbtSqj7HMTeAD73aeKkzFWSXa",
        "$2a$04$2g5m8Lii4qQ44j9AbYq7huAgJ7Ho4gnHBh6Zutxf80to./7KqeVRu",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178739635": {
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      isMarked: true,
      unencrypted: ["9", "3", "6", "1", "7", "5", "8"],
      values: [
        "$2a$04$l3aKM3iS8mad.u9EaNYIm.oOi/Hd.eCQtsdHA/A009XDZwVci9uyi",
        "$2a$04$Ke8QXyIMtfati2Xax3P/buF65I3LcjUyoJjsnlDuQ4wArPtCc4oeG",
        "$2a$04$4Rz8vjSotps4TvgcAXaQveyXuDUciHV.7taYM3x4HKovSz6YQc4u.",
        "$2a$04$0MKS/HHxUhihAfJKMLZVnOdOMJ4Q8dIRKM0.xn4KnjZwZMMUkw252",
        "$2a$04$zA7x/90ct5LrE31Nby721u48JkeQ2XlLUjIEhzAOTjlOAYHTtY1Da",
        "$2a$04$MwsQykAh749zYD448S5bl.q7DtCJ1y92Iv/6BYzrs0dX9X9ja2CqO",
        "$2a$04$uH6y58oB3o6iYaglQGacBeMBDtfhiuniXeXA.dr/pbGyfBgKTYfAG",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178739636": {
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      isMarked: true,
      unencrypted: ["6", "3", "1", "8", "4", "7", "2"],
      values: [
        "$2a$04$8yyyVJ/V/o0tgXy1Xuqp7.Oa7idbcRqyzk0soojz0Uco.g6MwrPnW",
        "$2a$04$UjTlhTBYOLxd8nqJVw9Qn..16s/Izp8syyJxtskZniTuLiDjWgEUC",
        "$2a$04$XTHBXsN87HIcxu2uuImXYOKF0QLqRjAygOn419zoE/l4ShCcOxTlK",
        "$2a$04$et5AkvUCs21NusmHc4KY1O8D5atVSMsVPi2o.bihHYzMfv8RbLaBm",
        "$2a$04$JLqmHSCX5A1bzjK9zzdwle6uKSVNS0QZgESb5u8oiDWdVWJ3c0T0u",
        "$2a$04$t6Lala.0mD4SFoOtw1p7dO/zBgKMZK3dzVhV8/5H8rXlXPh5yKR2S",
        "$2a$04$7JlmTDprfAtISAXD1mC83OS9wXEmOfsWzKLEgqcdyPpZHC6u6Tvja",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178739637": {
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      isMarked: true,
      unencrypted: ["1", "8", "3", "6", "9", "2", "5"],
      values: [
        "$2a$04$LTp8V7k1wzChn5QRhKwK7O51pUbMaTvXRWd4YdvFtwFx2II2u.tMy",
        "$2a$04$kumOmAX8i/XDiwt/x4bPS.XCkQM1d9j31nG3ne8XxL2MPXn5Iw3Cq",
        "$2a$04$xd5gVvWGObltKkvjVw3a3uV.NzL7HifviWCdFjBf7/FSgzp1miZpK",
        "$2a$04$37HlYH4ReSOv.Z8roSAere5fJHxdv42cylTlm2El4rqfKdXTJUFGu",
        "$2a$04$GatFft4MTTvjxTY09NpSgelm6zQx3P.vinY7qyRDwETERvL.hGp4O",
        "$2a$04$CUu9m4qcb5fd9agtOO6EW.3hXr9NTA1cAg0kk5mZRXu0mTVDeVb3O",
        "$2a$04$QutQUndKiwc3nSqKp9wt0.8DQhjXDKyeSUpTcrr0JjUWiBB3rV31m",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178739642": {
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      isMarked: true,
      unencrypted: ["3", "6", "1", "7", "4", "2", "8"],
      values: [
        "$2a$04$gmO/dlwkRSTlvkhBLE0xe.dIhickQRsCUeoQgKnho5z4/TZbbjsGm",
        "$2a$04$cuSlDrKp8DuJM6ZqbsCVXuJFmokGh1WKPm7dLYIS/4i.oOyLFpIaW",
        "$2a$04$8.TLx29NuK6y5hncuVBIv.0E6WaeoXwAyCpGdGtYosYPqDEckxBDq",
        "$2a$04$/FTorv9lb4okpk7Rm1rkG.I1BXancgUKwK9Qr1f1LsSzZPm.kNQMq",
        "$2a$04$ubiGnFa.wpoqeTdtqPcPwe9sjJvdjykcGYo/AOkgIwGzRk700Qseq",
        "$2a$04$xvFY1T6z.s5Q1R1hzLjAaOG5.YeU61/Ea7Pf0KfDtRZvGu70ef7gW",
        "$2a$04$HMiIo09WpvlsMQr/pT4CjOW/25iwqGZoQeY1sLgGzx7s0Socuib7q",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178739643": {
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      isMarked: true,
      unencrypted: ["2", "9", "6", "8", "3", "7", "1", "4"],
      values: [
        "$2a$04$J7s6Kq9i8Eyqrdmwc3PMR.2vE/7Mvt2LZ0lbikzzb8HPytAagzrUS",
        "$2a$04$.PtJlLfF.fKj0ZSTjiB7TOWpVS7q8JXC7P/8vzlGycWBVToYxt.eG",
        "$2a$04$8OmcGT3OeNYn0uZZxT38HeHoBLikX.sndXqWIT578FhdVf3S.kQUC",
        "$2a$04$WaC6lf/UJdVazA4htxYntO5xNCT2uhhCVSYAmaT/.Rs6iwMNDITX6",
        "$2a$04$qwrpRct2kAQjN4jV/H4yqu5PBgZ1Thlqnv0p3e1plpNvIQpkA1v9K",
        "$2a$04$e2K.teIzKTrghRAygBy1LuSwQGUVkvp1/2QEZ2qQ.p/9.mn52Dpn.",
        "$2a$04$C7tznt31/6qK4SJ1ujggteSFtJ9ukz8HmIQyWjFAhLfmVk.ztw6XK",
        "$2a$04$rzgmwSl9OsL/3G6Kq7lWmOg.HwwLZ0akG7iAK1cmWXRyvsIA5GQji",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178739644": {
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      isMarked: true,
      unencrypted: ["3", "1", "4", "6", "2", "7", "9", "5"],
      values: [
        "$2a$04$gPEUZQ9Ozazx8by87VnNyu5Mr6dyioN4H0RL4D0CJr4R7emi/eAF2",
        "$2a$04$wJkexVxEeyBsmYwUo41ZAeGeL/UOfSEvau8pmgYEuwzqpIYDUtI7a",
        "$2a$04$kNYwRsfaHlEenNv0ChoEUeNWnye7Vv65B9iaOJKvHWfN527zoNEYi",
        "$2a$04$YhSqlYQ3bDvdjMRb/B9jj.XeZGGoIn2D66.3.oeglaouMgE6pELH.",
        "$2a$04$V2X6I27qWbylVvO4W7GmyOFX3lGh6WO6oL/GCTGbqYvM4kn6ZAyOG",
        "$2a$04$r.4NH18uyGd1J7LvFdmEYu/5eh19rCJD.q0v1dvSOEASiytMhivrW",
        "$2a$04$zSqHGzlv8Qu2DEMsqDQVfuTgzcZfYjJjTB.10dGyGCI.JEHm.1.Nu",
        "$2a$04$EJCGDc73kqrNeRIR8Uw9f.lqdx4HNHg8Jq5WEJRHfNaxnROa0vefu",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178739645": {
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      isMarked: true,
      unencrypted: ["7", "2", "6", "9", "4", "1", "5", "8"],
      values: [
        "$2a$04$3ClpN4955C4IFuwaSxeLD.DU4wQk3U0cCjhw4C3NKtkHqybYAicBC",
        "$2a$04$Nque2Uh7gGClpAKeHg83FO6aV.P.3ACkhZgzNG63r3W.vH5wLXvnW",
        "$2a$04$Pkgr8F5vQEKSV1lCc5fL4ecyQwKoQLpARuAKfJ3Homb47/B8g2PO.",
        "$2a$04$gyFSjcRliOXUvSmWQIMa.OBjo8yHCnFvJcD1bRclk3eLoh8yFwQJS",
        "$2a$04$8gHd6UCOxXord97IljlH7uhzUHzTuD9exbgnDNuVrlgEsAjrFE8fC",
        "$2a$04$QPNAucCyaYdRdGJNax3Vheo5R/aTqwugqDd3/03dzLy4VLT.IAskq",
        "$2a$04$AhaeMDBGraSKRJ57AGKHYel3LEcmBFs9yY/lywipwYsf5aw9uGW7C",
        "$2a$04$yxvh8r1dL2i.PerjAeh5GORFoHZqIZxAQzHNWNN1qqX5j9dKgMXIC",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178739647": {
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      isMarked: true,
      unencrypted: ["2", "5", "4", "7", "3", "1", "6", "9"],
      values: [
        "$2a$04$uvZZWl0uue8buXkd.YB4zOQ/J1g22KHV0quQRY5Yh.ONDOMHdeAky",
        "$2a$04$3dNgyT6JdMkhP4D0GdA3Cev3z2IEOfX.6ZhfQl6XKkmHCd82lgiyK",
        "$2a$04$2FbDx1/SW0rvOxYZkVPk2uAixvvdVZBDqnXIDn.GMxVeXHYgpudBG",
        "$2a$04$i5a.rZt22aDhbqHDd5h2rOOYJE3OsiuqG3/EnvAOTko05O81nOZqq",
        "$2a$04$g/9qDxqN9MV5GHUSMBri8.yRk/KSMwTmCijzhon5wQ9FIcroop5m.",
        "$2a$04$23hGmn21RBJiYXF4udJcuuoi5LYjpPCFNgLER5zx2tep1mXiBcTLq",
        "$2a$04$Hc4Utse.acyd72Bew9A2y.P79ExQaqrv.TUuwM0UjQN1M2yWN9W1m",
        "$2a$04$Yq3XhvEVwO/gZ8AM1eACR.7yrzG588RlgNLxHzX/nY60N/joFZXty",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178740090": {
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      isMarked: true,
      values: "60",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740091": {
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      isMarked: true,
      values: "60",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740124": {
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      isMarked: true,
      values: "60",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740142": {
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      isMarked: true,
      values: "60",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740143": {
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      isMarked: true,
      values: "60",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740145": {
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      isMarked: true,
      values: "60",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740147": {
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      isMarked: true,
      values: "60",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740148": {
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      isMarked: true,
      values: "60",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740149": {
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      isMarked: true,
      values: "60",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740150": {
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      isMarked: true,
      values: "60",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740151": {
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      isMarked: true,
      values: "60",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740152": {
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      isMarked: true,
      values: "60",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740153": {
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      isMarked: true,
      values: "60",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740156": {
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      isMarked: true,
      values: "60",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740157": {
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      isMarked: true,
      values: "60",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740158": {
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      isMarked: true,
      values: "60",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740160": {
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      isMarked: true,
      values: "60",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740164": {
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      isMarked: true,
      values: "60",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740165": {
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      isMarked: true,
      values: "60",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740166": {
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      isMarked: true,
      values: "60",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740167": {
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      isMarked: true,
      values: "60",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740191": {
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      isMarked: true,
      values: "60",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740196": {
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      isMarked: true,
      values: "60",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740199": {
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      isMarked: true,
      values: "60",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740201": {
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      isMarked: true,
      values: "60",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740202": {
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      isMarked: true,
      values: "60",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740203": {
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      isMarked: true,
      values: "60",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740204": {
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      isMarked: true,
      values: "60",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740205": {
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      isMarked: true,
      values: "60",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740207": {
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      isMarked: true,
      values: "60",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740208": {
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      isMarked: true,
      values: "60",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740210": {
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      isMarked: true,
      values: "60",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740211": {
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      isMarked: true,
      values: "60",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740212": {
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      isMarked: true,
      values: "60",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740213": {
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      isMarked: true,
      values: "60",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740215": {
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      isMarked: true,
      values: "60",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740217": {
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      isMarked: true,
      values: "60",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740218": {
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      isMarked: true,
      values: "60",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740219": {
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      isMarked: true,
      values: "60",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740221": {
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      isMarked: true,
      values: "60",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740222": {
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      isMarked: true,
      values: "60",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740327": {
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$JokjR1lx0AYtfIvakMI4KeD0ilJ1vpbG3WxJzH7oiDUbMxxLaYlne",
      unencrypted: "first",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178740347": {
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$7Z0VDYhiyys9rBv2fc0dfu7RsdGEXJaidVQSxyPzSY7t1K8Ve/9Da",
      unencrypted: "mountain",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178740349": {
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$maNNJpqIC36sVYwqSRGt/.WDLOnjV/i9ZfZafV18THsIoW9ajHo3e",
      unencrypted: "he",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178740354": {
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$QpWKhRAj2PIPnh5RV9zauuS26m43lCm66nHlLguY08L01b79ZCOWO",
      unencrypted: "the",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178740373": {
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$TFczQ85ROrjSh1R.D/mh/eB5q3wKZETs9BwH5/dGwOpHzeG1JmBru",
      unencrypted: "my",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178740387": {
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$8EQqI8wrzIThgkIBjh2eMeqTXXk/HBLQhPYyzSetK0FodTGTw1rgG",
      unencrypted: "had",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178740390": {
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$rN5AkmdVVVIubXVBChQDPuTKI7trXS7OA9JXiGCBTtwrhvKmhFrwS",
      unencrypted: "ten",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178740394": {
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$FDubIqlUsTFCretDLRqDVOQEpU.K/YR9P0XZSZRWNm3C6gmEuEe7m",
      unencrypted: "bee",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178740395": {
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$eUJsdcRq6b0JpGTRIDfao.kPV7hMtNCnyNdLG9XDvb.efw9/rAiru",
      unencrypted: "was",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178740398": {
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$6uzHXwvZ4Ab0h.yA3RKfJO98WvNiORKP3NIwfuwdmhtg7wMfjcNxm",
      unencrypted: "mother",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178740401": {
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$tLmH5zncd5YmdtMq93LWS.H3S1PY3iOlxi9dYlVxvZqpgAMoZ7.XS",
      unencrypted: "house",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178740404": {
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$gdQCvHuiePuCp..rHkzhDOY98M.Ti8PWhtlWwINdYVH2KgwZ2URWi",
      unencrypted: "funny",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178740406": {
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$mpjPFT/u6jO5nlI7PsL4..JoqRoNQDpOVdVg2pBc6ravQFZoiFIPG",
      unencrypted: "said",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178740408": {
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$RLSsunmPLEtyD.Q1YQ7B8.B0Ew.o9g0IB18otZCLpGBro3QPX.KiO",
      unencrypted: "place",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178740419": {
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$AMcRb2LG8aDNhHKZmVnliOgHA3Wf/HQRcstUbb/l.gBwXuXUjhDDK",
      unencrypted: "shoe",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178740420": {
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$diyGSk3WQFAVkU7xColAFOvE2184Gzh1wf.m0MsWIpo8SDFpaFFWq",
      unencrypted: "were",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178740421": {
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$WWhEPcj0baP5InqyIDTCGu2KjNk/9UWWaGFX4j1gFYK4m0tseNHpy",
      unencrypted: "early",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178740444": {
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$oyEEGSEFgsqhuy40y0t/m.dVzhOd7EGWYdJo04FnGflirqlOlnGNm",
      unencrypted: "before",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178740445": {
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$XqVpHzXlLJwIFh1IRh78e.RMSlMdSVcOeSX5540mSfWi7DuluGSzq",
      unencrypted: "handle",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178740446": {
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$K.RQKu13jVOz6kX4McZI8e9S3znQsYofiJRL/L46uCenRCfs44Y1K",
      unencrypted: "second",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178740449": {
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$m2790Nd/fN1GtPnpmnNBZ.UuHkCE/Y3/r2A4KLYpSK.W2bcGQXvWq",
      unencrypted: "question",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178740452": {
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$pMDG5Cv4C4aaxWQ/Pg869e7rg5lwYNvONqXwLqhyHBw3s3tS40Fk6",
      unencrypted: "popular",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178740453": {
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$KNBF3xR6eD2EzaRTUVmwqOgioZRnmLDamq7S7OPmrXMCtyHEB.y9u",
      unencrypted: "laughing",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178740454": {
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$JHn3dj/z6UTYrcsoR5R9u.QqrQYRcdGEeQkNVdEROE0aFeAbyZ4fq",
      unencrypted: "garage",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178740455": {
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$m8UgBjPTs72W9Xwd8KxaIufWXH6hqC5awMv1womf6X1sxOj47h3JS",
      unencrypted: "international",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178740456": {
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$snzrcdYsHlDQQSWEgXQJ6ec6J70YeVpTYBwM5drytsUFzZ4.HfIYK",
      unencrypted: "general",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178740458": {
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$9qz/aZGnIvXH/B9Y7q77kOh/ZyaIdWbDGqUvaA5/m7MG5HmVE3Jcu",
      unencrypted: "doubt",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178740462": {
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$.PXhEZcU.Rl5EDpKy//KQuTJRY3jJZnklqMYqV9jLZJpGZ8Kqxtpi",
      unencrypted: "beautiful",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178740463": {
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$jdjT4/5e.nyzc38ccxqAuesvs8hRPcV36.ex1tXQu7rjMiObWagP2",
      unencrypted: "concrete",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178740464": {
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$C8.vsmHDPAkq044zdqYqOuBvD05a3oqfVCI4KvxMvJMEvpCnB9v5i",
      unencrypted: "bicycle",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178741517": {
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$FTXqKlBNRQLDpxf.NqRYy.nq.1TVt3A4EUJau1uM7VPsGQV2LN4dq",
      unencrypted: "tomorrow",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178741519": {
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$vAJcFNjg.ZNYiLb9IhY51Oyt29hMyP.jhFS9CrJUp0.O0KAiXjWiG",
      unencrypted: "advertisement",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178741521": {
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$pVIhjKQ0LfXnXp/.8aKvLOYdOehVxWSyywwdROPvHToRAdo9NMLDO",
      unencrypted: "coax",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178741526": {
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$KB1xSYh88y7WlPyMDFBgS.mQOBT4jCoapkXWyFUXqbRmi8hgnoUAC",
      unencrypted: "congenial",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178741527": {
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$bgt2/F35.qmp3SIKvKl20eaQyFD.OKdEBOO3MDmgCDRzB8sMcz4FK",
      unencrypted: "squirrel",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178741530": {
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$37UYzOLNprzMS/ACTQ8Wy.D36IfZ/Y/WP5bJw8ALXqR7C9b3MkykO",
      unencrypted: "necessary",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178741531": {
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$CqBQBco7/hFH3hTNZjp.VuBgv1k5cjvJKYibOMaFfJW3Gr6tMO5Re",
      unencrypted: "mortgage",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178741532": {
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$a4RVEuPkoPaYchg3oqE0S.xP7uH1Aq.joBODMUPlu1mIBWBi9lSk6",
      unencrypted: "arrogance",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178760533": {
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$ijSZzd1Qd69.Aw6/EMvl0.VeEKR0OFYlFv/C7mrO1Us/c5GER09Bi",
      unencrypted: "exquisite",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178799883": {
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$8aMRoq9wmMTJ6nYpbAZP.u2iVKsfOIYLCSbY.XBGqqsDL9j.XcV7e",
      unencrypted: "anonymous",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178799885": {
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$0p7eucaJbMu227CvvnP54.QNgkZIMnpfAb4yU0htO..68sIq9ju9u",
      unencrypted: "zephyr",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178799889": {
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$8BjMWpTkh7Ij8DS3wZSEPuQzwjKHZo024rU9DP9ARiHbSmbUO30Vi",
      unencrypted: "dilemma",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178799891": {
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$mlr4JQAFqtnHw1fcefaMfOReaZMTx2tBVXLR09hpGCDcWH7DlCO7a",
      unencrypted: "acquaintance",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178799897": {
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$ndKwKRxoYvACxSJKv.gRZuY9eDl6qGlKgakr3f1OM.SD9Uk5a22nK",
      unencrypted: "exacerbate",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178799899": {
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$yazUfMJOhU4aw4E9Ptjoi.ZfaIPsbF8okN/WbzqRB.1s1fKg7Wo92",
      unencrypted: "conscientious",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178799901": {
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$7loP9Q7lI1p5l/w/KgXk0uzFojDO7xq8VA3XoZGlRoVqd9ufWD7MK",
      unencrypted: "questionnaire",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178799902": {
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$lBjJXLlQQmDHySa7XcQ3lOKjP3lT23ruGJnXGdZ07WYXq3I3CWV0q",
      unencrypted: "camouflage",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178799905": {
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$NvUwbQh8z0Lzf9BEEoaVKOafrGR9Ohijt1ZZs.OsQbtrHzTfYgfrK",
      unencrypted: "iridescent",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178799906": {
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$sG015Ub9r9QsnaUJgxbsL.JgZY5AJdft.PgCdqtMeDgM5GPsGhLFW",
      unencrypted: "impugn",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178799907": {
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$E0s9cHAKkGbI50KQGnUKSe7GbBxMZtrgWZvFg8VusEfp6LTDv/SDW",
      unencrypted: "scintillant",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178799908": {
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$HfLUc0sqhQmG8hvOAC7uM.PRd/tqzJKyi/r8CFAK92Juh/LD5bg4m",
      unencrypted: "lachrymose",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "178832427": {
    responseType: "text",
    answer: {
      markAs: "metadata_question",
      isMarked: false,
      guide: {
        code: 1,
        description:
          "Assessment taker profile info for Supabase. Marking skipped",
      },
    },
  },
  "178832428": {
    responseType: "multiple choice text",
    answer: {
      markAs: "metadata_question",
      isMarked: false,
      guide: {
        code: 1,
        description:
          "Assessment taker profile info for Supabase. Marking skipped",
      },
    },
  },
  "178832430": {
    responseType: "multiple choice text",
    answer: {
      markAs: "metadata_question",
      isMarked: false,
      guide: {
        code: 1,
        description:
          "Assessment taker profile info for Supabase. Marking skipped",
      },
    },
  },
  "181771222": {
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      isMarked: true,
      values: "60",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "181772006": {
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      isMarked: true,
      values: "60",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "181772014": {
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      isMarked: true,
      values: "60",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "181772038": {
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      isMarked: true,
      values: "60",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "181772041": {
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      isMarked: true,
      values: "60",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "181772044": {
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      isMarked: true,
      values: "60",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "181772045": {
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      isMarked: true,
      values: "60",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "181825373": {
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      isMarked: true,
      unencrypted: [
        "man",
        "boy",
        "child",
        "daddy",
        "girl",
        "lady",
        "mommy",
        "person",
        "woman",
      ],
      values: [
        "$2a$04$SWy48oCVp4/vS/nkUWu/RucIySgOlxjoAOLPFVS0g1yua6RWoTTLG",
        "$2a$04$fihVFp42lPEJCZg.L8t3d.Wa28lsN80XujaFUj6OAwQoJCIjhQ/ZS",
        "$2a$04$L.50dRlyKw5zqmbIsC2pPu6mc0j96l5SQDrAsZAgtkKIfsNzSzOlq",
        "$2a$04$tQApLiSMKgPDzI4s7JTtEOn.lTwBLAQxu0xh8fPdDAuRrARIIQsBW",
        "$2a$04$U1MvFMHm/EeIesVxK8LiBekJ53FnSfIlB4pChQ3qv.bW3uZsEmM9u",
        "$2a$04$jppMOQHzmtJfNmgMFx0yhe8D6eZVGnxcpCT0Gb.2SlEU/rhl6vrF.",
        "$2a$04$Ij.jIx8tev7kD2LQSbbe1uJSrnFGWLNgQNKPN4EXnivtWUX3PpBLi",
        "$2a$04$xJoL/lXCwcl5lKaEXpJcG.N2AQX6P59R4XvYn2YKM0IBwugNr96ey",
        "$2a$04$BW/jSmG51jCkaU6M4d0/n.Hdk/7pORCNmBgg1Fo5hY5Na8M.vwEfG",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "181825460": {
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      isMarked: true,
      unencrypted: ["stairs", "steps"],
      values: [
        "$2a$04$46RLjoVb9l1ydh88DKrCPePRI6hbmjX3Ljdi.fdHnpScSQtSL3AZi",
        "$2a$04$fPxNGuVuDHYlHBndDnS1COXgXvKLKLIMxCXtW.G.zwf0zxUrA6UWC",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "181825465": {
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$ER.Cax408b3AAsBogW5F4.JhnSI39iZ89mH96Ktjj/QbCvccYXRyG",
      unencrypted: "table",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "181825540": {
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$9t1WuD2DWmYUtScAffF.cOWJpzvgGmG9yuwSknAQTIKISWAoLWwXu",
      unencrypted: "chair",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "181825541": {
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      isMarked: true,
      unencrypted: ["sofa", "couch", "davenport", "chesterfield"],
      values: [
        "$2a$04$BPB2Dx/PblLNv1lH0DFIIOneH/WMYiZ7OmlpnjMkB5LJt4JBMhKgW",
        "$2a$04$o2MELiqt5aSd91GdXW2tQeVZjJ9sBvep.eEFxh06WzrEOn7/pcvGy",
        "$2a$04$nNEUb6IFquAl.Tl5bJ8x5.S1dKfnn361YHxUJzAa3Z1HbH9ay45uq",
        "$2a$04$LekBVN8.nOKLJASmQAsVveCDiVG2iRHUMPhHbHHiQpOYebBzF0uHa",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "181825957": {
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      isMarked: true,
      unencrypted: ["is", "looks", "was"],
      values: [
        "$2a$04$6N.d4GQGc5Zso/n/hgJQHuw0BcmPwuEaxbwCFsXK9trrt.i0AwMLG",
        "$2a$04$K4B5YdeCeCAdK2zre5ZJkeGmJpMXIr7qz/CTVV09iiHEtAlvu2Eda",
        "$2a$04$7HEnoGmjYE1EnPk9OTTR2u5A5qr8cmDFcChpRIhN/wndBXPjH0c92",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "181825960": {
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      isMarked: true,
      unencrypted: ["under", "beneath"],
      values: [
        "$2a$04$D5boWdB./C0fmOe/NsOqsuab2/tiYbmptE8rOGM9Bcg.sqqsnDgRy",
        "$2a$04$dF.yQH0ay0fM3rCU3P.4Z.FNcHD2KT9M7S.F.oYh/gg8lGXoMWwNO",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "181825961": {
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$1hzBPUUhygDaXkV6I6c7medn.xFlUwxBrnpN.ZYEc8HX.zv2A0G3G",
      unencrypted: "book",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "181825962": {
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      isMarked: true,
      unencrypted: ["pick", "clean", "straighten", "tidy"],
      values: [
        "$2a$04$Gsm2qtQiBWnSLpGrBrKc8eTz2Yw8koQfo.maJ.14RyGYsrfBVtnfS",
        "$2a$04$SI50kIWgAwuvJhQ..P8oSuQjaFgR4XEp9QTG6mlK47zgKFaA1drVO",
        "$2a$04$cHAQ4SNomLp4ZeRHz3bvguDBnvjRnPSHnO5oOt/kmJTO/3ReucQDC",
        "$2a$04$3HZGQy3dAB31WAd9x2.2M.wHln26T0RGdixGPmTICiKQTmf3fAUKm",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "181825964": {
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      isMarked: true,
      unencrypted: ["dinner", "breakfast", "lunch", "supper"],
      values: [
        "$2a$04$Mh9dGJoE7RwhHIENHJ.qse7QwXWDj6wdLjsC5tJ4kwzeyQIlw46IS",
        "$2a$04$Wd/XDgPCRg8pIWDF3owAI.Pd8m0folX47uV28B8irPY57P83AZGkq",
        "$2a$04$4ZtH8238DtZlHh0.VCYyVON7ZPRwo/9gToeYqnA5P9LY473SgbCse",
        "$2a$04$SBVku.6UmAgga8YP49zN6.fjsihb0677XfhEzFC/3pETR2nhgbQSi",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "181825983": {
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      isMarked: true,
      unencrypted: ["read", "checkout", "do", "explore", "get"],
      values: [
        "$2a$04$COPvO8nRpYF7.8yEIwqTdODelIbIeFz1O1CAdoyZrvKov.ST0qAJq",
        "$2a$04$t8SP6xiQpeN6hcR9vWuan.ka.55gJQQR5nH.m8/il64iUlUtvLoze",
        "$2a$04$SWOfVuGhDs5ikEDny3tv1OvWxWbdF9k0S4Mr.tJGPz0xWNBzThg/K",
        "$2a$04$acOWJSyt4NuKkGVNFY93p.ZRHI2I5Rea6qg3PKVW78/QFpzW1WPO2",
        "$2a$04$1kYmwDSmEp8xos.8/RRWD.0JctB0T3IDDSoQLsMIFIAal6/UcG62q",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "181825985": {
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      isMarked: true,
      unencrypted: ["down", "back"],
      values: [
        "$2a$04$b953v13vZEJMyqent3yx6uHGssJiYB36/Zbe34raJeayg/0xddIg.",
        "$2a$04$RrEYTVsrj/uaAxwoeHBKY.HQoSWFdVMUushqEUCtOEmV3ySsgq8xC",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "181825988": {
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      isMarked: true,
      unencrypted: ["every", "at", "night"],
      values: [
        "$2a$04$eqwxP88UMU076MQVqB2AW.gNJLKcBZrWylb5XG6OHqpSNXggfqtwa",
        "$2a$04$ZgravjnbC34qx5pRNmnFZOccnVjv9kywUYCOHXmY4y30/S6zbkmuq",
        "$2a$04$KIDWTDJW657DGU8yw/szi.ZzPE3EBX0TgbfZkuuf.PRUhFxPgVEW6",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "181825990": {
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      isMarked: true,
      unencrypted: ["cold", "freezing"],
      values: [
        "$2a$04$RTrOxYgZwB/e79npqSQUPOlf.20KXHHxzXZN8J4KYy8VhBal8X7Di",
        "$2a$04$eGaPVsXi5yFsBHAbvj06B.D/ygwpKyVylZlnhoL.OG3OeQX9lTf3u",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "181825992": {
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      isMarked: true,
      unencrypted: ["ringing", "beeping", "buzzing", "vibrating"],
      values: [
        "$2a$04$rWBDdySxHJxb/w4PnXbMTO.8gMAjKGwJrxYfZMWPX8qS0OQ/w2Pym",
        "$2a$04$B/qUpuDBzLlb7EsEOOSwDOi4.jBdzdFqUAm/0Cp7Qhta3YG7Cdrjq",
        "$2a$04$Lgo0OKhku8uzapmFP3FVNe1DkYTZaTy/1tWOoXBwfwsBgherCav9y",
        "$2a$04$B6G2CoLVSoBFOW/i6Lx.eeoLF1BQexQFntGWHNGl90ITy6XMZUY0u",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "181825995": {
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$deOwiIslS.qRiAQZHafaBu1jjKHp//exiTSke2LHg2eeZntAKg73K",
      unencrypted: "egg",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "181825997": {
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$qW5nBtDn0zdZ526JyznX4OHhHwLfUXNhrr4Dj7IW/bYCKPlNaVT3e",
      unencrypted: "band-aid,bandage,towel, plaster",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "181826004": {
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$.o4v7VfM04Hqr8b5oPqO9eNjvIIbLhxMNNxM5mbSZpOvXItjp8BkO",
      unencrypted: "water",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "181826009": {
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      isMarked: true,
      unencrypted: ["fly", "glide"],
      values: [
        "$2a$04$D6X.erVOrDo9SFQPbj1MbuSIB97R2dutNoC1XRR7i5SMPeMe7rIkm",
        "$2a$04$V4TuN7UhugiRDk0n.MjJcOmSbvkzC9oNq4Uc58TupgZ6sqRLek25O",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "181826012": {
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      isMarked: true,
      unencrypted: ["round", "rounded", "circular", "shaped", "spherical"],
      values: [
        "$2a$04$5QnePZuFRMHRrjHCEhW1AebrTiS7Z7gtNUipU31.i3ZAcjiEUTzDO",
        "$2a$04$Gg805iMVnOoremDoZCcpXui7TA/GIt8ERtlyDummOZ7L0yVNFyrHu",
        "$2a$04$xwItFaYAvwQPSAyLtKw1Ve4psKin/STS6h7mxaHsoLyDRKcJhEpdG",
        "$2a$04$Vzp6UisXI.YD29GvFXAuEuo4atdaP9oIlTGHnG3NUf.XCqs2sPayu",
        "$2a$04$pwGi9iCwwuZfEXSHQDQ./endVB5Ju6FQhAjMTauo0x.sgV58EeS8q",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "181826137": {
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      isMarked: true,
      unencrypted: ["rained", "hailed", "snowed"],
      values: [
        "$2a$04$RSt5JRol/Gc.aOBATMjWK.7eCr5bGcwk2vrcvlRhuA1A75hv2BoD2",
        "$2a$04$3MInEBBkTpsy59XPxEzZEe6TcaR2ndzZC8F1VqGndgBO3g0KRdyUC",
        "$2a$04$H7hMtryhWLjzqbgpO.YjmuFIS3OonI/Ohhd8cNW6PIPmi4S9Sq93W",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "181826139": {
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      isMarked: true,
      unencrypted: ["speech", "lecture", "presentation"],
      values: [
        "$2a$04$iMOTUo0AW9JHhwZH4.bFIOg9BOKUUADyhdeWQNCUy2A2ctwLQmd4m",
        "$2a$04$E714UfVX3uiJllb2y4puqeCTpVjK9pplo/W76N439zaC7hlEs7U06",
        "$2a$04$vp2ANqHdE.a4atxP.CXsDOmD39Z9cbqaAI8La9eIO0Ns/T/MR/Nu6",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "181826143": {
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$jr2Fo8MR5LbvzT8dPDeraOOl26uZc2ZR373zL85JruuPVLTbMzUX6",
      unencrypted: "four",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "181826147": {
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      isMarked: true,
      unencrypted: [
        "fear",
        "apprehension",
        "dilemma",
        "feelings",
        "fright",
        "plight",
      ],
      values: [
        "$2a$04$y38vBQI2vMbrgcgLlSwZCuRvxvdoZ7AGjkeUka38Id.kBS1lDna5y",
        "$2a$04$TGira6OXfOmE9TtrKRI.4.ncJkuGyvAqVcFVwy5HhHU845vlnqi6.",
        "$2a$04$jLU1kAM3AVL1i3W17GlZwOdNwcFh96DcOWXRN2jfWYaALwBqmz21G",
        "$2a$04$E6OtRv03yAMI/C5MfdMt6etan8.VIRQv6KlA0t..t0p3UNxpqvq3.",
        "$2a$04$wH41gNv1mwkzUWVPGUUoHuCmuqoOsJTJ8nFT4eD8LcwCHTYBDtoSW",
        "$2a$04$eFhCpVQSqSt.8Aheh6Q40.dAp2hJImucL9vz73CnWANKDcUe6pjNS",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "181826148": {
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      isMarked: true,
      unencrypted: ["money", "investments", "savings"],
      values: [
        "$2a$04$nOGLJ4R.WYEYMShI93XNxu.4.0OxrZjOIQKpkecnIwtdXg9NyoNoW",
        "$2a$04$gCnQdLnuVQitd6hnC2z/b.HDGHjoI7wAdq81x7WJ557vQ0YV5LnPe",
        "$2a$04$0Rkpwvdvnd9T9CCGWdKcWuDfmSi7utu8bP.UJMo.yrEHWDZXS/rNq",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "*********": {
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      isMarked: true,
      unencrypted: ["drivers", "cars", "driving", "travel", "travelers"],
      values: [
        "$2a$04$9Ewv4hfJ63jd9RIbNp535.7OAqi7E8syiF9Prpre2MiRjk4dNKare",
        "$2a$04$y.Vwz4.ZwqEdRF0I836tU.A/mPz1CTrpxeNgz3dLSAMwY4So7RIM6",
        "$2a$04$fVit/l4nCTmFTGEtxj3DxeqHK7cEjTiLAN99if4RrhJjyahbJmMhi",
        "$2a$04$oy3DdMi22kq9DTQy.ey3peUmJIPrKkUPqWt/EePTu48Yv8zubf93O",
        "$2a$04$tGlbPEjNSEaxQSWJlc0BAu94rNhIFWd5udUcRwXrfDMGdcEwfGVTy",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "*********": {
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$MXnQk8Q81s5o8BycW.pdruEjCNb5odvH/S08iyGnhvJrLthbLCVIO",
      unencrypted: "sun",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "*********": {
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$qC0oCQKN97HC.enGGpvVJus0cSellsqC4ubjHgRJmo4uNo3ZN/AN6",
      unencrypted: "roots",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "181826157": {
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$GhiYjoy/m7K.DsSbrAuM9.ROCAlHqk.w7J3ClFKHuhhgPGKvUT99e",
      unencrypted: "leaves",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "182597969": {
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      isMarked: true,
      unencrypted: [
        "secretive",
        "confidential",
        "discreet",
        "exclusive",
        "mysterious",
        "private",
        "sneaky",
      ],
      values: [
        "$2a$04$OqF1wYW2BLT/l.bZlYVx0erZiwx6Fl8JEwe0OjIXJ1Dm6CPnkvlLq",
        "$2a$04$Slga/hHgGdiYJfAtydizSu3rEfLgQxmjTNJ/GubdVK5johS9/2IOu",
        "$2a$04$wa1n43HQ5Fzm.tVCmXyeaeir/DFELjaxmud2luJCIN61EdCI5mSLq",
        "$2a$04$u9WwHJPvFA/ms6WPa/cL1eeYA1x9uE6cYoejGayfI/PbUcJ44VvJm",
        "$2a$04$E1abFGuZvWTf8ouCP2qsIui20Xh5Zx5hz8Upm7odCeJW5NfqzEEsi",
        "$2a$04$WBE5JGP2YLip5AClFptS9./uMUeQEegZrj7Q4fNU/zSekRHHKGFXi",
        "$2a$04$TlFGziY.RAZD51IEm329PupgbRzx1CIAl3122UlfRBdhwmnTjCP3a",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "182597974": {
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      isMarked: true,
      unencrypted: ["nomads", "wanderers"],
      values: [
        "$2a$04$4bRSoyaITJ/6ykCX89I4G.uB.QuZDJuBe/Ahm2VF7IBkez6xzAXOC",
        "$2a$04$/nnHPsS16s1.5gknYT6otug.fB.a5JOnAWdgDkbdpat4LdB3vpwL6",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "182597984": {
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      isMarked: true,
      unencrypted: ["help", "support", "volunteering"],
      values: [
        "$2a$04$rbiNVMWyWBbcvKEFXn1zsuRW4aOqfTCIJj.Xz0XQplhS9LeHrXQvy",
        "$2a$04$I/6G.F6GnPo8RPG450p1muzv0qGOlpWgVig3DlfH7tboh2xU3jTgm",
        "$2a$04$MNwUdWFSIWiauW4VC6GKQe.dILBIpubzfFny/8KjUdOhHyO2FgDkm",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "182597990": {
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      isMarked: true,
      unencrypted: ["ready", "prepared", "willing"],
      values: [
        "$2a$04$rX6R3Z.OkaLY/8wFFTw/s.LZ7nzFAyZlLui2zQeUxkou55y49flIS",
        "$2a$04$7R2b8QO5yNp5SVX1H43LhO/20HCVWbGmOj5imAXr3L0zheMwY9JXS",
        "$2a$04$3GcyLzpGxq2QNVEiFA7CqeE0btqfzxpUpiqlb5FX60aBh9x9gkdeO",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "182597996": {
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      isMarked: true,
      unencrypted: ["writing", "keyboarding", "typing"],
      values: [
        "$2a$04$Blb0HhbZ.OJ8J/.0TwMd9eoN5Fk17ROPZZ4ob5yUHUrjdtIYVOclG",
        "$2a$04$KhJVmJOstaBfahy1U5hkxudNHTJs.Jq5sIfZcl4lLDrHDjyNAu3hy",
        "$2a$04$jrkxKY59s8mBCZuSlWazTeVs4pRrJdYpVf2e3uDBGV2tFDTvMZ4UK",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "182598092": {
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      isMarked: true,
      unencrypted: ["birds", "chicks", "penguins"],
      values: [
        "$2a$04$sTSLp88FMatjMgWuEw3NIu6rc2Oc63cYAKYZSlfp0pouGymKBwSUW",
        "$2a$04$U8ipduuQNcqWtAcKxOcC1exjIah.EeXRQiAS3Ojd72LUz7ErtDrLu",
        "$2a$04$wx0.uBuMIh3OtoNQ9hzr7eBQk/QYxari4gjJZSbin8JR3EUb2h.XO",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "182598100": {
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$xyiAk1cyuYU/AvoZKYNrQuLeABG0bwWeTOZN2MX4H5gfDqXwcYcvS",
      unencrypted: "roots",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "182598107": {
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$4AuwswHKXUCltdTXzcNdye1/yLUD413RN9grwHiicA86.Y9CbuL6O",
      unencrypted: "parasitic",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "182608819": {
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$KWyAWcSa/GjV7Ju7n2OZqufx8xhp3dBszBLE6wpJSb4otsctktnAG",
      unencrypted: "tides",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "182608889": {
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      isMarked: true,
      unencrypted: [
        "false",
        "bogus",
        "fake",
        "falsified",
        "fraudulent",
        "unethical",
      ],
      values: [
        "$2a$04$hBuPE4d9gJ0BuFpWk8QjDuTDp/TiBhIzfK5MAUrQJC9tBX/aGCucu",
        "$2a$04$zZ9/vJFZIj3hQuVqVB3hp.H5y4PSFxy3Q8mAsXe2BRRV8y90iUcSW",
        "$2a$04$ea8pcz8U.SSXcPggqVyYx.5NBE4E28xHbtCq1S/g5Dfb714SEJjjO",
        "$2a$04$mkQ0rqN1OYiZoYXPl2DAl.XKcX3yy/CYoQ1H6FPVADz.4qK13U5Hy",
        "$2a$04$xG5PTL8QNEDcFnykn9D5XOcZNDVpwTRzWJusWapYSlgHB89p1HxpC",
        "$2a$04$vluxn8ksUxUkL4vMWKBhVe9ZwxC1NUW/xQG2i6CJiA3rLnZMwR18i",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "182608892": {
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      isMarked: true,
      unencrypted: [
        "value",
        "appeal",
        "demand",
        "impact",
        "importance",
        "necessity",
        "need",
        "popularity",
        "relevance",
        "significance",
        "usefulness",
        "utility",
        "worth",
      ],
      values: [
        "$2a$04$YwOUx4SC8Tb/w6IEkzTo7OhOZDEdaQ3A5xis2sicLuwC1lI7FN1.m",
        "$2a$04$XufkaHuNCSjQYBWUYGJeW.BfhwRWAUBt5Bz6n5qNYuUT0aoBit5Dm",
        "$2a$04$8/q4Yh0/mrXLsLAoW2nxlu/YzAPblQaThffcKXN6Rna87PMyPc5bO",
        "$2a$04$0xmyTiP46bjMB09zX5vGpe.8HyUIB0p8zXbB8GiOXV816wnTodaWW",
        "$2a$04$OPH72a68m5RkOS1ddUWR0.KK64QWcFDU8noExODBsZNl7WpzNjrCC",
        "$2a$04$Wa8RFR3ZI1Je2uT3Ew.k0eHSkTc4PsWWGXiBy1BWRVKX7pDfBzf/a",
        "$2a$04$aK7KKtmrHlHxgGoUf.yTuuhN8.Len81o8Y1qRce2SXdvR1ZGhi5ku",
        "$2a$04$YCshzMgQ8pS5QH9SfVR7.eNciSEFSgdzDbaV9H16ZrIGVISEdjMLC",
        "$2a$04$c7XV6cQXXc0UJO8WXaVOEeW5hQKpxQJ/jLgFR2HcZfJYYYWgOAXP.",
        "$2a$04$9RYjIkuRNgyiKPaTdpqyCOmZf/1utNqI/2Xhak2dsaSGaxzNSI0mK",
        "$2a$04$C2xlNfaIMb..vsdyKBG2g.Lr6gb4RHrRzJ9S9Anb4TNBEhepffI6S",
        "$2a$04$2m/xOwbhbayH2U2giDKyxu09kmTWYafNdfQDdm7J3A16WiPb1TCFW",
        "$2a$04$UivepWTVcePVX/Smy.Qju.uxTYULOeCweTFaMnMBHJ1jKEE9ERJm2",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "182608895": {
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      isMarked: true,
      unencrypted: ["lawnmower", "engine", "mower", "mowing", "tractor"],
      values: [
        "$2a$04$mY0C4F0knSG8jMbisSjxNOMvZCnT6G3ACbCjGPKZsCp6uX16AzTNu",
        "$2a$04$F6MYcon5sjHKu5hL0vGq0OAYH.C5fyKFT3S2X8OjqBsz8KLzTLOTi",
        "$2a$04$dzJEPRcWb6GM4R5Jkrmvk./Ido1z4K2fP4NmPBtklKhNNjZw1lksC",
        "$2a$04$dOfCCR19QIMq1fo1E7g.R.Ri.DO7u.H0pUk2J.lfden6VgQLTZ3ba",
        "$2a$04$J9mpbzODelvC.PzhUuIM0ORBh/9I8lffPD.R1kIS0BRsbG9/oPuMK",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "182613067": {
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      isMarked: true,
      unencrypted: ["peaceful", "gentle", "subtle", "underlying"],
      values: [
        "$2a$04$UShjdggRkpuCZDfnBAJvxu1Oh3sXrUCntO9N8pwTKRstnNwLyl/ne",
        "$2a$04$lGu9./bg6x9vwvYFhfWGjeyBUt4iQ8AhQSZY7WBGb8qZOU5un3jE.",
        "$2a$04$eF8oT8HG4tC5OllGgLoGMe6Da5VrC0xdP/5WWtBnIs7.hQqisHYYu",
        "$2a$04$y/fQcJL1Ti5ahqEftVRGVOtZAcZ9eW1dY2dTxP5ZIb.HUqcfgPWMO",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "182613068": {
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      isMarked: true,
      unencrypted: [
        "regarding",
        "about",
        "concerning",
        "criticizing",
        "critiquing",
        "describing",
        "reviewing",
      ],
      values: [
        "$2a$04$UPNDMaoN9s1jVydrH.27ieSr/Vw4W0M09ltz3Mi9z5hruE/EqGxta",
        "$2a$04$lwmKLDrTAQ85aoFRXg2d6egyVR8o24kAfEhnBUkNjYdG/2T6c7tw6",
        "$2a$04$jqz54OsARqQS8bkgYufwH.paQOe94gCMB7r2cVpuAqC3GdgIYeFVy",
        "$2a$04$nRlQJ7dbZ076mBLxXyTaE.gw6GNuyOIQN1WqZoKOY/uzZMmi20tu2",
        "$2a$04$vsS0u0m6dYDw.lUhzYBPA.VBdVXDU6P0NUeLcfAKXx/zaig1raEEq",
        "$2a$04$TtWoprY9VlkZgsuy7EOCQea2NutyfziePcYrAM8VXVYwm6ayhq3t6",
        "$2a$04$bCzbPEz8mqXVEUbViRavhe2kav0/12NZRWJ.SrahWWe6aJ6GAXG1G",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "182873923": {
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      isMarked: false,
      values: "$2a$04$sT.msZxx8fHdis0lXLmQs.9Ns6L13SrDHSEnp.lQXCBOLJt/ZACHO",
      unencrypted: "same",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "182873924": {
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      isMarked: false,
      values: "$2a$04$xWixt3NI2QVKGeztRAFGruSkBHPkTPLWJwH9isN4v6XYt70kZyifu",
      unencrypted: "different",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "182874061": {
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$J2IOUMxnvWMUFMp4ZaYjTOFSlxhbXB0lzEzjK20hE0A44LIpLSRD.",
      unencrypted: "different",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "182874062": {
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$dOLnCYiDkDsGzyKLelMwL.Miae9FU6YjYTyzI3HsbCtXBgDxP6I8K",
      unencrypted: "same",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "182874063": {
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$QJePNIhAby4vZOV0qznFcO1NQJDz64E6Wvwrjd6.rSyV5jQprLIlC",
      unencrypted: "different",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "182874064": {
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$et66K3F2rV7wlcJ3dLTqnOQk5EFShBG8C8ofPPkDfEPaY2G.w9J/G",
      unencrypted: "different",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "182874080": {
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$D2D8ft4ma8t0hAjY.akYOOskbvd72kKR33GpWcx9d6afXz77tjc0q",
      unencrypted: "same",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "182874081": {
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$hlH6GxHR0SeFIHDratxzK.rJ7mZE4E44sKOL3YOrZXkurUDOYgXWO",
      unencrypted: "same",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "182874086": {
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$28TJ0XGPBSRIdgPGMZClmeIdSkddIx.IaWH/k48p0s5f.e8u85fOK",
      unencrypted: "different",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "182874090": {
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$QikRPZgUPqww9WVxCB7xB.yIdO1Z7xwJojLsa5ytFERYjNiUt802u",
      unencrypted: "different",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "182874091": {
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$w73Uj2PGmfYcNeBXSTQsYu67p3q1OocNZIrBY0zgQ16A6CtC5jL7a",
      unencrypted: "same",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "182874092": {
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$xOfN9sibdnImA3gUpHco3OeNVG.nyJMHN2MVatLTZSQYqeY1AHnI.",
      unencrypted: "different",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "182874093": {
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$IKmRLZCrEG1dvdHZ/ruIxemqjObKg0CaIqua0O9mpxs91GSSdJdJW",
      unencrypted: "same",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "182874112": {
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$5xIrLTfTeeXDbIJVlZ5a1Os/P0IlSjH63Dx2J7fQPgixGIH9ja0Qe",
      unencrypted: "different",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "182874114": {
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$fJbtDhIEu/aRkbnDJWBVd.h0P.TVlNK1Jp7OuSAZOXMnmuYbcU8s.",
      unencrypted: "different",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "182874115": {
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$rNBV6zFrUz3XE/ZFKSKrTe5lVdP6VgKP8VhoOT3LqkPN3c3h6ZYSy",
      unencrypted: "same",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "182874142": {
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$p3bi6zMwqTWDss1XSgyaI.q78KTKEUKV.F8PwPPqkXJB5fNBlJQ4y",
      unencrypted: "same",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "182874144": {
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$LEIeZKOXdrbSh07BKyOfYuLVroez/xe0YwZ/X/9kh7L2O56.lVnxC",
      unencrypted: "same",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "182874145": {
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$eVsRABgDZ232CKAV8gtevuFSBlVabIaTqFBHVsYBxSctXX75aY48W",
      unencrypted: "different",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "182874148": {
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$GiUJADX81y/X0LwWMQ8hgeDY5QJ29HqrNwoI4T8VnnI.14xUMOfXi",
      unencrypted: "same",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "182874151": {
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$VvczcJmxhkjhIahZofTGAeo2CVeAQC4RWI9fDSQImiLx8Je1igvUa",
      unencrypted: "same",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "182874152": {
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      isMarked: true,
      values: "$2a$04$5qVWX0Yeb8USOpam7F0Q2OPDz2tGuwLrgOSI9bHhpbaWmDFPHe4eu",
      unencrypted: "same",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "185847412": {
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      isMarked: true,
      values: "80",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "185847413": {
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      isMarked: true,
      values: "80",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "185847425": {
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      isMarked: true,
      values: "80",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "185847430": {
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      isMarked: true,
      values: "80",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "185847432": {
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      isMarked: true,
      values: "80",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "185847435": {
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      isMarked: true,
      values: "80",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "185847436": {
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      isMarked: true,
      values: "80",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "185847448": {
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      isMarked: true,
      values: "80",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "185847449": {
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      isMarked: true,
      values: "80",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "185847450": {
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      isMarked: true,
      values: "80",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "185847452": {
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      isMarked: true,
      values: "80",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "185847453": {
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      isMarked: true,
      values: "80",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "185847454": {
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      isMarked: true,
      values: "80",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "185847455": {
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      isMarked: true,
      values: "80",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "185847456": {
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      isMarked: true,
      values: "80",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "185847457": {
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      isMarked: true,
      values: "80",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "185847458": {
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      isMarked: true,
      values: "80",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "185847459": {
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      isMarked: true,
      values: "80",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "185847460": {
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      isMarked: true,
      values: "80",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "189989426": {
    responseType: "multiple choice text",
    answer: {
      markAs: "multiple_choice",
      isMarked: false,
      values: "$2a$04$RlqkLD9wlRxki7JDLqPsPuCiEf2PoA.AyqD3yrdex6vKkWvsmRdSC",
      unencrypted: "",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "189989428": {
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      isMarked: false,
      values: "$2a$04$W/ZbUDhUXqftR517vUJvM.HsrMG7WLOj0YuxX2pUnS9EVDTKGPUDC",
      unencrypted: "",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "189989430": {
    responseType: "multiple choice text",
    answer: {
      markAs: "multiple_choice",
      isMarked: false,
      values: "$2a$04$wuBaTVI84QSzIoBceh1LJ.R0k.4Gp4qVlYePA0b5EZFQ36c3kLKO.",
      unencrypted: "",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "189989444": {
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      isMarked: false,
      values: "$2a$04$nUrk0i7Hmf1MZoBzJKyYKOwSVpUuoNDKCwSBJKy3X.cuLfwVcrbAa",
      unencrypted: "",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "189989449": {
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      isMarked: false,
      values: "$2a$04$576cyZIrenXcTt7jmz/npu5EYwMlLRCnCKkGtrNRTzE77zwl6fYjy",
      unencrypted: "",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "189989450": {
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      isMarked: false,
      values: "$2a$04$zJ67O7UAGWEfzf5LtZzEmuZYfmthAqSvkLkL6stlvhuUjztqgyBhy",
      unencrypted: "",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "189989451": {
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      isMarked: false,
      values: "$2a$04$zFEMw9WCccFWVS/YonociOK8CTZqKQRTEnRHwVykEvChMhIWuMllO",
      unencrypted: "",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "*********": {
    responseType: "multiple choice text",
    answer: {
      markAs: "multiple_choice",
      isMarked: false,
      values: "$2a$04$hTifEQ5JLwwYItFtTwheIOQ9PlHUTfZIyndT/4jN1sAcnnreF5.f.",
      unencrypted: "",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "*********": {
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      isMarked: false,
      values: "$2a$04$Rd5gsfylgRpwO2ueX/rIHucjRBIhf2WXxUZkY6zXeYv.ToRWkr39a",
      unencrypted: "",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "*********": {
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      isMarked: false,
      values: "$2a$04$JYNAKh7/Iv.OGvN4kVwKdulHxlbMiwjQt2mwLJ5kQGK4TZVQogr6K",
      unencrypted: "",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "*********": {
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      isMarked: false,
      values: "$2a$04$arEUfd6xhNmzEK77ETvyY.hivQS/UaEtscUsAdP9eDxe4ibCMxoyS",
      unencrypted: "",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "189989460": {
    responseType: "multiple choice text",
    answer: {
      markAs: "multiple_choice",
      isMarked: false,
      values: "$2a$04$Muly2H39HuzMfCwJwR8O5.UJeixlHXOtiSA1HSWzOxprmXKYoVEky",
      unencrypted: "",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "189989462": {
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      isMarked: false,
      values: "$2a$04$9O2lw9oXEqu9RXMhLNakEOvkQRSgrjuvNDPrAUFgyFD9AwDfUUF8e",
      unencrypted: "",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "189989463": {
    responseType: "multiple choice text",
    answer: {
      markAs: "multiple_choice",
      isMarked: false,
      values: "$2a$04$31XkNm5RxirmAbRKiH4vuenzarPgdEBolfSjE/EPgipmMU/uSZDeG",
      unencrypted: "",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "189989464": {
    responseType: "multiple choice text",
    answer: {
      markAs: "multiple_choice",
      isMarked: false,
      values: "$2a$04$kUZli.3hbatiJGjnOcfgEuRTQZodtiFPsVnUoQ3wwGuLzk.xoVhw6",
      unencrypted: "",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "189989465": {
    responseType: "multiple choice text",
    answer: {
      markAs: "multiple_choice",
      isMarked: false,
      values: "$2a$04$MMUeEqanZ3Ks0jzXpxpuJ.af513mVAXi77qK1JcduC82sQyJJ8cUm",
      unencrypted: "",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "189989466": {
    responseType: "multiple choice text",
    answer: {
      markAs: "multiple_choice",
      isMarked: false,
      values: "$2a$04$yirKsISeo/8Zlkt2.v1um.iP7NDhhy7n3etFocKGBhKo.PsckPgt2",
      unencrypted: "",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "189989513": {
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      isMarked: false,
      values: "$2a$04$Mlb/8mtLOBc89nvgOgcsg.yXNYFelvrqeJphrLFfS6BYifTUUP/16",
      unencrypted: "",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "189989518": {
    responseType: "number series",
    answer: {
      markAs: "series",
      isMarked: true,
      unencrypted: "11",
      values: "$2a$04$75XGsli8eU90GMqOdKN.D.r94tKjzcYFSR4kv.TyquSL.8tJFkpz2",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "189989519": {
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$nnQk32pr7GHbMiDjoXj5QuUV0kHhmbGTyoG0eq0h/6I0bBx997A.S",
      unencrypted: "1,2",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "189989524": {
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      isMarked: true,
      values: "$2a$04$UBGBSBBLRihf/Smmj.f.vOZrpock8YwyM6CE3XCseko7TT/6Eqdp.",
      unencrypted: "1,3",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
};
