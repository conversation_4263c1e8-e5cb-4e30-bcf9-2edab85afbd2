// ------------------------------
// SUPABASE
// ------------------------------
export type SupbaseKeys = {
  supabaseUrl: string;
  supabaseKey: string;
};

export const supabaseConfig: SupbaseKeys = {
  supabaseUrl: process.env.NEXT_PUBLIC_SUPABASE_V2_URL!,
  supabaseKey: process.env.SUPABASE_V2_SERVICE_ROLE_KEY!,
};
export const ASSESSMENT_DATA_TABLE = "assessmentData";
export const STUDENT_DATA_TABLE = "studentData";

// ------------------------------
// RESEARCH
// ------------------------------
export const RESEARCH_TABLE = "research";
export const RESEARCH_ASSESSMENT_ID = "B4lbrH-2R0CEedVIrM6org";
export const RESEARCH_REDIRECT_URL = "/research/complete";

export const CORRECT_ANSWER_VOICE_URL =
  "https://www.datocms-assets.com/103689/1688725428-general-correct.mp3";

export const TIME_UP_ANSWER_VOICE_URL =
  "https://www.datocms-assets.com/103689/1695652270-generic-timeout.mp3";
export const TIME = {
  MILLISECONDS: {
    ONE_SECOND: 1000,
    ONE_MINUTE: 60000,
    ONE_HOUR: 3600000,
    ONE_DAY: 86400000,
  },
  SECONDS: {
    ONE_SECOND: 1,
    ONE_MINUTE: 60,
    ONE_HOUR: 3600,
    ONE_DAY: 86400,
  },
};
