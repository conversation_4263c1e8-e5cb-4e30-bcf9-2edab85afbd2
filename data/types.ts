import type { ResponsiveImageType } from "react-datocms";
// -------------------------
// Logic functions types
// -------------------------
export type FeedbackContext =
  | "info"
  | "error"
  | "warning"
  | "success"
  | "timeout"
  | "group_time_up";
export interface IScaleItemLogic {
  assessmentId: string;
  scaleItem: IScaleItemRecord;
  group: IScaleItemGroupRecord;
  scaleSpellCheckOn?: boolean;
  onSubmit: (args?: any) => any;
  nextItem: (args?: any) => any;
  isStimulusComplete?: boolean;
  tooltipTimout?: boolean;
}
export interface INudge {
  scaleItemNudgeAudio: FileField;
  scaleItemNudgeText: string;
  scaleItemNudgeTime: number;
}

// -------------------------
// Assessment content types
// -------------------------
export type ItemId = string | number;
export type FileField = {
  id: ItemId;
  title?: string;
  alt?: string;
  url: string;
  responsiveImage?: ResponsiveImageType;
};
export interface IScaleRecord {
  id: ItemId;
  scaleStart: FileField;
  scaleComplete: FileField;
  scaleItemGroups: IScaleItemGroupRecord[];
  scaleTitle: string;
  scaleDisplayName?: string;
  scaleSpellCheckOn?: boolean;
}
export interface IOnboardingSlideRecord {
  id: ItemId;
  onboardingText?: string;
  onboardingAudio: FileField;
  onboardingImage: FileField;
  onboardingImagev2: FileField;
  onboardingVideo?: {
    url: string;
    video: {
      mp4Url: string;
      streamingUrl: string;
      thumbnailUrl: string;
      width: number;
      height: number;
    };
  };
}

export interface IScaleItemGroupRecord {
  id: string;
  scaleItemGroupAboveAverage: boolean;
  scaleItemGroupIsSample: boolean;
  scaleItemGroupItems: IScaleItemRecord[];
  scaleItemGroupMaxAge: number;
  scaleItemGroupMaxIncorrect: number;
  scaleItemGroupOnboardingContent: IOnboardingSlideRecord[];
  scaleItemGroupTimeLimit: number;
  scaleItemGroupShowTimer: boolean;
  scaleItemGroupTitle: string;
}

export interface IIncorrectAnswerRecord {
  id: string;
  incorrectAnswerAction: string;
  incorrectAnswerAudio: FileField;
  incorrectAnswerText: string;
}
export type ScaleItemResponseType =
  | "binary"
  | "multiple choice text"
  | "multiple choice image"
  | "multiple choice image large"
  | "multiple choice audio"
  | "text"
  | "voice"
  | "number series"
  | "character sequence"
  | "pair cancellation"
  | "numbers"
  | "coding"
  | "keypad";

export type ScaleItemStimulusType =
  | "text"
  | "image"
  | "image and text"
  | "audio"
  | "pair cancellation"
  | "number series"
  | "coding";

export interface IScaleItemRecord {
  id: ItemId;
  maxAnswersPickable: number;
  scaleItemAnswer: string;
  scaleItemCorrectAudio: FileField;
  scaleItemCorrectText: string;
  scaleItemIncorrectState: IIncorrectAnswerRecord[];
  scaleItemInstructionAudio: FileField;
  scaleItemInstructionText: string;
  scaleItemIsMarked: boolean;
  scaleItemMediaStimulus: [FileField];
  scaleItemNumberOfReplays: number | null;
  scaleItemIsMetadata: boolean | null;
  scaleItemMetadataFieldName: string;
  scaleItemNudgeAudio: FileField;
  scaleItemNudgeText: string;
  scaleItemNudgeTime: number;
  scaleItemResponseMedia: FileField[];
  scaleItemResponseType: ScaleItemResponseType;
  scaleItemResponseValues: string;
  scaleItemStimulusType: ScaleItemStimulusType;
  scaleItemTextStimulus: string;
  scaleItemTimeoutAction: string;
  scaleItemTimeoutAudio: FileField;
  scaleItemTimeoutText: string;
  scaleItemTimeoutTime: number;
  scaleItemTitle: string;
  scaleItemCheckPhonemes: boolean;
  scaleItemVoicePhonetics: string;
  scaleItemVoiceTarget: string;
}
