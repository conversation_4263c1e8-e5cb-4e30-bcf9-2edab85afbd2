import { useRouter } from "next/router";
import { useEffect, useState } from "react";

import { loadingRoutes } from "@/data/loadingRoutes";

export const useLoader = () => {
  const router = useRouter();
  const [loading, setLoading] = useState(false);

  const matchedRoute = loadingRoutes.find(
    (routeObj) => routeObj.route === router.pathname
  );
  const shouldShowLoader = !!matchedRoute;
  const loaderVariant = matchedRoute ? matchedRoute.variant : "snoozy";
  const loaderText = matchedRoute ? matchedRoute.text : "Thinking";

  useEffect(() => {
    const handleRouteChangeStart = () => {
      if (shouldShowLoader) {
        setLoading(true);
      }
    };

    const handleRouteChangeComplete = () => {
      setLoading(false);
    };

    router.events.on("routeChangeStart", handleRouteChangeStart);
    router.events.on("routeChangeComplete", handleRouteChangeComplete);
    router.events.on("routeChangeError", handleRouteChangeComplete);

    return () => {
      router.events.off("routeChangeStart", handleRouteChangeStart);
      router.events.off("routeChangeComplete", handleRouteChangeComplete);
      router.events.off("routeChangeError", handleRouteChangeComplete);
    };
  }, [router, shouldShowLoader]);

  return { loading, loaderVariant, loaderText };
};
