import { create } from "zustand";

import { AdminRole } from "@/types/admin";

type AdminUser = {
  role: AdminRole;
  name: string;
  schoolId: number;
  schoolName: string;
};

export interface IAdminStore {
  adminUser: AdminUser | null;
  setAdminUser: (adminUser: AdminUser | null) => void;
  selectedIds: number[];
  setSelectedIds: (ids: number[]) => void;
}

export const useAdminStore = create<IAdminStore>()((set) => ({
  adminUser: null,
  setAdminUser: (adminUser) => set(() => ({ adminUser: adminUser })),
  selectedIds: [],
  setSelectedIds: (ids) => set({ selectedIds: ids }),
}));
