import { useCallback, useState } from "react";

const useMicrophoneAccess = () => {
  const [microphoneAccess, setMicrophoneAccess] = useState<boolean | null>(
    null
  );
  const [error, setError] = useState<Error | null>(null);

  const checkMicrophoneAccess = useCallback(async () => {
    return new Promise<boolean>(async (resolve, reject) => {
      try {
        const stream = await navigator.mediaDevices.getUserMedia({
          audio: true,
        });
        const tracks = stream.getTracks();
        tracks.forEach((track) => track.stop());

        setMicrophoneAccess(true);
        resolve(true);
      } catch (err) {
        setMicrophoneAccess(false);
        setError(err as Error);
        resolve(false);
      }
    });
  }, []);

  const promptAccess = useCallback(async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      setMicrophoneAccess(true);
      const tracks = stream.getTracks();
      tracks.forEach((track) => track.stop());
    } catch (err) {
      setMicrophoneAccess(false);
      setError(err as Error);
    }
  }, []);

  return {
    microphoneAccess,
    error,
    checkMicrophoneAccess,
    promptAccess,
  };
};

export default useMicrophoneAccess;
