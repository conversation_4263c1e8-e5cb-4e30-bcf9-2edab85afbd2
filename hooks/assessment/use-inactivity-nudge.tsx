"use client";

import { useEffect, useRef, useState } from "react";

export const useInactivityNudge = (enabled: boolean, durationS: number) => {
  const [shouldShowModal, setShouldShowModal] = useState(false);
  const idleTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const countdownLoggerRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (!enabled) return;

    const resetTimer = () => {
      clearTimeout(idleTimeoutRef.current!);
      clearInterval(countdownLoggerRef.current!);
      setShouldShowModal(false);

      let remaining = durationS;

      countdownLoggerRef.current = setInterval(() => {
        remaining--;
      }, 1000);

      idleTimeoutRef.current = setTimeout(() => {
        clearInterval(countdownLoggerRef.current!);
        setShouldShowModal(true);
      }, durationS * 1000);
    };

    document.addEventListener("mousedown", resetTimer);
    document.addEventListener("keydown", resetTimer);

    resetTimer();

    return () => {
      document.removeEventListener("mousedown", resetTimer);
      document.removeEventListener("keydown", resetTimer);
      clearTimeout(idleTimeoutRef.current!);
      clearInterval(countdownLoggerRef.current!);
    };
  }, [enabled, durationS]);

  const cancel = () => {
    setShouldShowModal(false);
    clearTimeout(idleTimeoutRef.current!);
    clearInterval(countdownLoggerRef.current!);
    idleTimeoutRef.current = setTimeout(() => {
      setShouldShowModal(true);
    }, durationS * 1000);
  };

  return {
    shouldShowModal,
    cancel,
  };
};
