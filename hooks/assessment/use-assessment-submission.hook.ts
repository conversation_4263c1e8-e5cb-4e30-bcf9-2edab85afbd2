import { useContext } from "react";

import { StudentContext } from "@/context/student-context";
import { useAssessment } from "@/hooks/assessment/use-assessment.hook";
import { useSaveScaleResponses } from "@/hooks/mutations/useSaveScaleResponses";
import { useAssessmentResponsesStore } from "@/stores/use-assessment-responses";
import { useTimeTakenStore } from "@/stores/use-time-taken-store";

interface AssessmentSubmissionParams {
  selectedResponses: string[];
  isCorrect: boolean | null;
  skipped: boolean;
  reasonForExit?: "kickout" | "timeout" | "finished";
}

export const useAssessmentSubmission = ({
  assessmentData,
  scaleId,
  scaleItemId,
  filteredScaleItemIds,
}: {
  assessmentData: any;
  scaleId: string;
  scaleItemId: string;
  filteredScaleItemIds: string[];
}) => {
  const { student } = useContext(StudentContext);

  const timeTakenMs = useTimeTakenStore((s) => s.elapsedTimeMs);

  const { currentScale, currentScaleItem } = useAssessment({
    assessmentData,
    scaleId,
    scaleItemId,
  });

  const { saveScaleResponses, showRetryFailedModal, setShowRetryFailedModal } =
    useSaveScaleResponses();

  const allAssessmentItemIds = assessmentData.assessmentContent
    .filter(
      (
        content
      ): content is Extract<
        (typeof assessmentData.assessmentContent)[number],
        { __typename: "NovaScaleRecord" }
      > => content.__typename === "NovaScaleRecord"
    )
    .flatMap((scale) =>
      scale.scaleContent.filter(
        (item): item is { __typename: "NovaScaleItemRecord"; id: string } =>
          item.__typename === "NovaScaleItemRecord" && !!item.id
      )
    )
    .map((item) => item.id);

  const allScaleIds = assessmentData.assessmentContent
    .filter(
      (
        content
      ): content is Extract<
        (typeof assessmentData.assessmentContent)[number],
        { __typename: "NovaScaleRecord" }
      > => content.__typename === "NovaScaleRecord"
    )
    .map((scale) => scale.id);

  const isLastScaleInAssessment =
    currentScale?.id === allScaleIds[allScaleIds.length - 1];

  const isLastItemInAssessment =
    scaleItemId === allAssessmentItemIds[allAssessmentItemIds.length - 1];

  const currentItemIndex = filteredScaleItemIds.findIndex(
    (id) => id === scaleItemId
  );
  const isLastItemInScale =
    currentItemIndex === filteredScaleItemIds.length - 1;

  const submit = async ({
    selectedResponses,
    isCorrect,
    skipped,
    reasonForExit,
  }: AssessmentSubmissionParams): Promise<{
    networkError: boolean;
    success: boolean;
  }> => {
    if (!currentScale || !currentScaleItem)
      return { networkError: true, success: false };

    let networkError = false;
    let success = false;

    const store = useAssessmentResponsesStore.getState();
    const hasSubmitted = selectedResponses.length > 0;

    console.log("SCALE ITEM", scaleItemId);
    console.log({ isLastItemInAssessment });
    console.log({ isLastItemInScale });
    console.log({ isLastScaleInAssessment });
    console.log({ reasonForExit });

    store.addItemResponse({
      scaleId: currentScale.id,
      scaleName: currentScale.displayName,
      item: {
        id: currentScaleItem.id,
        name: currentScaleItem.internalId,
        practice: currentScaleItem.practiceItem ?? false,
        seen: true,
        skipped: skipped,
        timeTakenMs,
        score: hasSubmitted ? (isCorrect ? 1 : 0) : null,
        answer: selectedResponses,
      },
    });

    if (
      isLastItemInScale ||
      reasonForExit === "timeout" ||
      reasonForExit === "kickout"
    ) {
      store.completeScale({
        scaleId: currentScale.id,
        reasonForExit: reasonForExit ?? "finished",
      });
    }

    //Assessment completion logic
    if (
      isLastScaleInAssessment &&
      (isLastItemInScale ||
        reasonForExit === "timeout" ||
        reasonForExit === "kickout")
    ) {
      const assessment = store.getAssessment();
      const totalTimeMs =
        assessment?.scales.reduce(
          (sum, scale) =>
            sum +
            scale.items.reduce(
              (itemSum, item) => itemSum + item.timeTakenMs,
              0
            ),
          0
        ) ?? 0;

      store.completeAssessment({
        dateCompleted: new Date().toISOString(),
        totalTimeMs,
      });

      if (student && assessment?.id) {
        try {
          let assessmentResultId: string | null = null;
          try {
            const saveResult = await saveScaleResponses.mutateAsync({
              assessment_id: assessment?.id,
              student_id: student.id,
              student_code: student.code,
              assessment_response: assessment,
            });
            assessmentResultId = saveResult?.assessmentResultId;
          } catch (saveError) {
            console.error("Failed to save final assessment:", saveError);
          }

          await fetch("/api/assessment/send-assessment-data", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
              assessment,
              student,
              completedAt: new Date().toISOString(),
              assessmentResultId,
            }),
          });
        } catch (error) {
          console.error("Failed to send assessment data:", error);
        }
      }
    }

    try {
      const assessment = store.getAssessment();
      const shouldSaveScaleToDb =
        isLastItemInScale ||
        reasonForExit === "timeout" ||
        reasonForExit === "kickout";

      if (student) {
        let assessmentResultId: string | null = null;
        if (shouldSaveScaleToDb) {
          const saveResult = await saveScaleResponses.mutateAsync({
            assessment_id: assessment?.id,
            student_id: student.id,
            student_code: student.code,
            assessment_response: assessment,
          });
          assessmentResultId = saveResult?.assessmentResultId;
        }
        success = true;
      }
    } catch (err) {
      console.error("Failed to save scale", err);
      networkError = true;
      success = false;
    }

    return { networkError, success };
  };

  return {
    submit,
    showRetryFailedModal,
    setShowRetryFailedModal,
  };
};
