import { useMemo } from "react";

import {
  GetNovaAssessmentQuery,
  NovaScaleItemRecord,
} from "@/types/graphql/graphql";

interface IUseAssessment {
  assessmentData: GetNovaAssessmentQuery["novaAssessment"];
  scaleId: string;
  scaleItemId?: string;
  scaleItemIndex?: number;
  filteredScaleItemIds?: string[];
}

const isNovaAssessmentRecord = (
  assessment: GetNovaAssessmentQuery["novaAssessment"]
): assessment is Extract<
  GetNovaAssessmentQuery["novaAssessment"],
  { __typename: "NovaAssessmentRecord" }
> => {
  return assessment?.__typename === "NovaAssessmentRecord";
};

export const useAssessment = ({
  assessmentData,
  scaleId,
  scaleItemId,
  scaleItemIndex,
  filteredScaleItemIds,
}: IUseAssessment) => {
  return useMemo(() => {
    if (!isNovaAssessmentRecord(assessmentData)) {
      return {
        scales: [],
        currentScale: null,
        currentScaleItem: null,
        nextScale: null,
        nextScaleId: null,
        nextScaleFirstItemId: null,
        isFirstItemInScale: false,
        isLastItemInScale: false,
        isLastItemInAssessment: false,
        isScaleTimerActive: false,
        nextScaleItem: null,
        currentIndex: -1,
        progress: {
          scale: { current: 0, total: 0 },
          item: { current: 0, total: 0 },
        },
      };
    }

    const scales = assessmentData.assessmentContent.filter(
      (
        content
      ): content is Extract<
        typeof content,
        { __typename: "NovaScaleRecord" }
      > => content.__typename === "NovaScaleRecord"
    );

    const totalScales = scales.length;
    const currentScaleIndex = scales.findIndex((scale) => scale.id === scaleId);
    const currentScale = scales[currentScaleIndex] || null;

    if (!currentScale) {
      return {
        scales,
        currentScale: null,
        currentScaleItem: null,
        nextScale: null,
        nextScaleId: null,
        nextScaleFirstItemId: null,
        isFirstItemInScale: false,
        isLastItemInScale: false,
        isLastItemInAssessment: false,
        isScaleTimerActive: false,
        nextScaleItem: null,
        currentIndex: -1,
        progress: {
          scale: { current: 0, total: totalScales },
          item: { current: 0, total: 0 },
        },
      };
    }

    const allScaleItems = currentScale.scaleContent.filter(
      (
        item
      ): item is Extract<typeof item, { __typename: "NovaScaleItemRecord" }> =>
        item.__typename === "NovaScaleItemRecord"
    );

    const scaleItems = filteredScaleItemIds
      ? allScaleItems.filter((item) => filteredScaleItemIds.includes(item.id))
      : allScaleItems;

    const totalItemsInScale = scaleItems.length;

    const currentIndex =
      typeof scaleItemIndex === "number"
        ? scaleItemIndex
        : scaleItems.findIndex((item) => item.id === scaleItemId);

    const currentScaleItem =
      currentIndex !== -1
        ? (scaleItems[currentIndex] as NovaScaleItemRecord)
        : null;

    const firstScaleItemId = scaleItems.length > 0 ? scaleItems[0].id : null;
    const lastScaleItemId =
      scaleItems.length > 0 ? scaleItems[scaleItems.length - 1].id : null;

    const nextScaleItem =
      currentIndex !== -1 && currentIndex < scaleItems.length - 1
        ? scaleItems[currentIndex + 1]
        : null;

    const isFirstItemInScale = currentIndex === 0;
    const isLastItemInScale = currentIndex === scaleItems.length - 1;

    const nextScale =
      currentScaleIndex + 1 < totalScales
        ? scales[currentScaleIndex + 1]
        : null;

    const nextScaleFirstItemId =
      nextScale?.scaleContent.find(
        (item) => item.__typename === "NovaScaleItemRecord"
      )?.id || null;

    const isLastItemInAssessment =
      isLastItemInScale && currentScaleIndex === totalScales - 1;

    const assessmentName = assessmentData?.displayName;
    const scaleName = currentScale?.displayName;

    return {
      assessmentName,
      scaleName,
      scales,
      currentScale,
      scaleItems,
      currentScaleItem,
      currentIndex,
      firstScaleItemId,
      lastScaleItemId,
      nextScaleItem,
      nextScale,
      nextScaleId: nextScale?.id || null,
      nextScaleFirstItemId,
      isFirstItemInScale,
      isLastItemInScale,
      isLastItemInAssessment,
      isScaleTimerActive:
        isFirstItemInScale &&
        Boolean(currentScale.scaleTimerDuration) &&
        Boolean(currentScale.scaleTimer),
      progress: {
        scale: { current: currentScaleIndex + 1, total: totalScales },
        item: { current: currentIndex + 1, total: totalItemsInScale },
      },
    };
  }, [
    assessmentData,
    scaleId,
    scaleItemId,
    scaleItemIndex,
    filteredScaleItemIds,
  ]);
};
