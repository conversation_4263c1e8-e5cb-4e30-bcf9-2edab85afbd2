import { useMutation, useQueryClient } from "@tanstack/react-query";

import { CACHE_KEYS } from "@/lib/cache";
import { supabase } from "@/utils/supabase";

interface ToggleHideReportInput {
  reportId: number;
  hideReport: boolean;
}

export const toggleHideReport = async ({
  reportId,
  hideReport,
}: ToggleHideReportInput) => {
  const { data, error } = await supabase
    .from("studentReports")
    .update({ hide_report: hideReport })
    .eq("id", reportId);

  if (error) {
    console.error(`Error updating report ID ${reportId}: ${error.message}`);
    throw new Error(error.message);
  }

  return data;
};

export const useToggleHideReportMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: ["ToggleHideReport"],
    mutationFn: toggleHideReport,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [CACHE_KEYS.ANALYSIS_BY_SCHOOL],
      });
    },
  });
};
