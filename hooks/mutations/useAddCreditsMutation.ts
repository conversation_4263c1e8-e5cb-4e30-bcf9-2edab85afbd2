import { useMutation, useQueryClient } from "@tanstack/react-query";

import { CACHE_KEYS } from "@/lib/cache";
import { CreditStatus } from "@/types/admin";
import { supabase } from "@/utils/supabase";

interface addCreditsInput {
  schoolId?: number;
  orgId?: number;
  numCredits?: number;
}

export const addCredits = async ({
  schoolId,
  orgId,
  numCredits = 1,
}: addCreditsInput) => {
  const entries = Array.from({ length: numCredits }, () => ({
    school_id: schoolId,
    org_id: orgId,
    status: CreditStatus.Unassigned,
  }));

  const { data, error } = await supabase.from("credits").insert(entries);

  if (error) {
    throw new Error(error.message);
  }

  return data;
};

export const useAddCreditsMutation = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: [CACHE_KEYS.CREDITS],
    mutationFn: addCredits,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [CACHE_KEYS.SCHOOLS] });
      queryClient.invalidateQueries({ queryKey: [CACHE_KEYS.CREDITS] });
      queryClient.invalidateQueries({ queryKey: [CACHE_KEYS.SCHOOL_BY_ID] });
    },
  });
};
