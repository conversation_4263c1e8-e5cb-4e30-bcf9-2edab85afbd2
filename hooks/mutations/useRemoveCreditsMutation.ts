import { useMutation, useQueryClient } from "@tanstack/react-query";

import { CACHE_KEYS } from "@/lib/cache";
import { CreditStatus } from "@/types/admin";
import { supabase } from "@/utils/supabase";

interface removeCreditsInput {
  schoolId?: number;
  orgId?: number;
  numCredits?: number;
}

export const removeCredits = async ({
  schoolId,
  orgId,
  numCredits = 1,
}: removeCreditsInput) => {
  let selectQuery = supabase
    .from("credits")
    .select("id", { count: "exact" })
    .eq("status", CreditStatus.Unassigned)
    .limit(numCredits);

  if (schoolId) {
    selectQuery = selectQuery.eq("school_id", schoolId);
  }
  if (orgId) {
    selectQuery = selectQuery.eq("org_id", orgId);
  }

  const { data: selectData, error: selectError, count } = await selectQuery;

  if (selectError) {
    throw new Error(selectError.message);
  }

  if (count && count > 0) {
    const idsToDelete = selectData.map((row) => row.id);
    const { data: deleteData, error: deleteError } = await supabase
      .from("credits")
      .delete()
      .in("id", idsToDelete);

    if (deleteError) {
      throw new Error(deleteError.message);
    }

    return deleteData;
  } else {
    return [];
  }
};

export const useRemoveCreditsMutation = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: ["RemoveCredits"],
    mutationFn: removeCredits,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [CACHE_KEYS.SCHOOLS] });
      queryClient.invalidateQueries({ queryKey: [CACHE_KEYS.CREDITS] });
      queryClient.invalidateQueries({ queryKey: [CACHE_KEYS.SCHOOL_BY_ID] });
    },
  });
};
