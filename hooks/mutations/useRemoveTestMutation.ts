import { useMutation, useQueryClient } from "@tanstack/react-query";

import { CACHE_KEYS } from "@/lib/cache";
import { supabase } from "@/utils/supabase";

interface RemoveTestInput {
  studentIds: number[];
  schoolId: number;
}

export const removeTest = async ({ studentIds, schoolId }: RemoveTestInput) => {
  const removedCreditIds: number[] = [];

  for (const studentId of studentIds) {
    const { data: updateData, error: updateError } = await supabase
      .from("credits")
      .update({
        status: "Unassigned",
        assessment_id: null,
        student_id: null,
        redeemed_at: null,
      })
      .eq("school_id", schoolId)
      .eq("status", "Assigned")
      .eq("student_id", studentId)
      .order("id", { ascending: true })
      .limit(1)
      .select("id");

    if (updateError) {
      console.error(
        `Error removing test for student ID ${studentId}: ${updateError.message}`
      );
      continue;
    }

    if (updateData && updateData.length > 0) {
      removedCreditIds.push(updateData[0].id);
    }
  }

  return {
    creditIds: removedCreditIds,
  };
};

export const useRemoveTestMutation = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: ["RemoveTest"],
    mutationFn: removeTest,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [CACHE_KEYS.STUDENTS_BY_SCHOOL],
      });
      queryClient.invalidateQueries({ queryKey: [CACHE_KEYS.STUDENTS] });
      queryClient.invalidateQueries({
        queryKey: [CACHE_KEYS.CREDITS_BY_SCHOOL],
      });
    },
  });
};
