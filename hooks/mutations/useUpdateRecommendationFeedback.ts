import { useMutation, useQueryClient } from "@tanstack/react-query";

import { CACHE_KEYS } from "@/lib/cache";
import { RecommendationsFeedback } from "@/types/analysis";
import { supabase } from "@/utils/supabase";

interface UpdateRecommendationsFeedbackInput {
  reportId?: number;
  feedback?: RecommendationsFeedback[];
}

export const updateRecommendationsFeedback = async ({
  reportId,
  feedback,
}: UpdateRecommendationsFeedbackInput) => {
  console.log(feedback);
  const { data: updateData, error: updateError } = await supabase
    .from("studentReports")
    .update({ recommendations_feedback: feedback })
    .eq("id", reportId)
    .select("id, recommendations_feedback");
  if (updateError) {
    throw new Error(updateError.message);
  }

  return {
    feedbackData: updateData,
  };
};

export const useUpdateRecommendationsFeedbackMutation = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: [CACHE_KEYS.RECOMMENDATIONS_FEEDBACK],
    mutationFn: updateRecommendationsFeedback,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [CACHE_KEYS.ANALYSIS_BY_ID] });
    },
  });
};
