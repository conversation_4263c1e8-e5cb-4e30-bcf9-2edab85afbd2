import { useMutation, useQueryClient } from "@tanstack/react-query";

import { SCHOOL_TEST_ID } from "@/constants/constants";
import { CACHE_KEYS } from "@/lib/cache";
import { supabase } from "@/utils/supabase";

interface AssignTestInput {
  studentIds: number[];
  schoolId: number;
}

export const assignTest = async ({ studentIds, schoolId }: AssignTestInput) => {
  const assignedCreditIds: number[] = [];

  for (const studentId of studentIds) {
    const { data: updateData, error: updateError } = await supabase
      .from("credits")
      .update({
        status: "Assigned",
        assessment_id: SCHOOL_TEST_ID,
        student_id: studentId,
      })
      .eq("school_id", schoolId)
      .eq("status", "Unassigned")
      .is("student_id", null)
      .order("id", { ascending: true })
      .limit(1)
      .select("id");

    if (updateError) {
      console.error(
        `Error updating student ID ${studentId}: ${updateError.message}`
      );
      continue;
    }

    if (updateData && updateData.length > 0) {
      assignedCreditIds.push(updateData[0].id);
    }
  }

  return {
    creditIds: assignedCreditIds,
  };
};

export const useAssignTestMutation = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: ["AssignTest"],
    mutationFn: assignTest,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [CACHE_KEYS.STUDENTS_BY_SCHOOL],
      });
      queryClient.invalidateQueries({ queryKey: [CACHE_KEYS.STUDENTS] });
      queryClient.invalidateQueries({
        queryKey: [CACHE_KEYS.CREDITS_BY_SCHOOL],
      });
    },
  });
};
