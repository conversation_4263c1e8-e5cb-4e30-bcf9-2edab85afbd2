import { useMutation, useQueryClient } from "@tanstack/react-query";

import { CACHE_KEYS } from "@/lib/cache";

export const addStudents = async ({ students }) => {
  const response = await fetch("/api/add-students", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({ students }),
  });

  if (!response.ok) {
    throw new Error("Network response was not ok");
  }

  return response.json();
};

export const useAddStudentsMutation = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: ["AddStudents"],
    mutationFn: addStudents,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [CACHE_KEYS.STUDENTS] });
    },
  });
};
