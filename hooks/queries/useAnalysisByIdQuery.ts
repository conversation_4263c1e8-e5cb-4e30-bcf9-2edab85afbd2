import { useQuery } from "@tanstack/react-query";

import { CACHE_KEYS } from "@/lib/cache";
import { AnalysisData, Student } from "@/types/analysis";
import { supabase } from "@/utils/supabase";

interface FetchByReportIdInput {
  studentCode?: string;
}

export const fetchAnalysisById = async ({
  studentCode,
}: FetchByReportIdInput): Promise<AnalysisData | null> => {
  const { data, error } = await supabase
    .from("studentReports")
    .select(
      `id,
        analysis,
        result_data,
        hide_report,
        student:student_code (
          first_names,
          surname, 
          year,
          date_of_birth
        ),
        student_code,
        assessment_meta,
        recommendations_feedback
      `
    )
    .eq("student_code", studentCode)
    .single();

  if (error && error.code !== "PGRST116") {
    throw new Error(error.message);
  }

  if (!data) {
    return null;
  }

  const student = Array.isArray(data.student) ? data.student[0] : data.student;

  if (!student) {
    throw new Error("No student data found");
  }

  const analysisData: AnalysisData = {
    id: data.id,
    analysis: data.analysis,
    result_data: data.result_data,
    hide_report: data.hide_report,
    student: student as Student,
    student_code: data.student_code,
    assessment_meta: data.assessment_meta,
    recommendations_feedback: data.recommendations_feedback,
  };

  return analysisData;
};

export const useAnalysisByIdQuery = ({ studentCode }: FetchByReportIdInput) => {
  return useQuery({
    queryKey: [CACHE_KEYS.ANALYSIS_BY_ID, studentCode],
    queryFn: () => fetchAnalysisById({ studentCode }),
    enabled: !!studentCode,
  });
};
