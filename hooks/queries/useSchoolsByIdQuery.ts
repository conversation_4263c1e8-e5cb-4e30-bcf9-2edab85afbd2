import { useQuery } from "@tanstack/react-query";

import { CACHE_KEYS } from "@/lib/cache";
import { supabase } from "@/utils/supabase";

import { SchoolDetailView } from "./useSchoolsQuery";

interface fetchSchoolByIdInput {
  schoolId?: number;
}

export const fetchSchoolById = async ({ schoolId }: fetchSchoolByIdInput) => {
  const { data, error } = await supabase
    .from("view_school_details")
    .select(
      `school_id, 
    school_name, 
    admin_name, 
    admin_email, 
    invite_status,
    org_name, 
    org_id,
    location,
    school_type,
    school_age,
    unassigned_credits,
    total_credits,
    redeemed_credits,
    student_count`
    )
    .eq("school_id", schoolId)
    .single();

  const typedData = data as SchoolDetailView;

  if (error) {
    throw new Error(error.message);
  }

  return typedData;
};

export const useSchoolsByIdQuery = ({ schoolId }: fetchSchoolByIdInput) => {
  return useQuery({
    queryKey: [CACHE_KEYS.SCHOOL_BY_ID],
    queryFn: () => fetchSchoolById({ schoolId }),
  });
};
