import { useQuery } from "@tanstack/react-query";

import { CACHE_KEYS } from "@/lib/cache";
import { supabase } from "@/utils/supabase";

interface FetchByStudentCodeInput {
  studentCode?: string;
}

export const fetchStudentByCode = async ({
  studentCode,
}: FetchByStudentCodeInput) => {
  const { data, error } = await supabase
    .from("student")
    .select(
      `student_id,
        student_code,
        school_id,
        first_names,
        surname, 
        year,
        date_of_birth,
        adhd,
        dyslexia,
        spelling_ability,
        reading_ability,
        other_conditions,
        academic_ability,
        processing_speed,
        deactivated
      `
    )
    .eq("student_code", studentCode)
    .single();

  if (error) {
    throw new Error(error.message);
  }

  return data;
};

export const useStudentByCodeQuery = ({
  studentCode,
}: FetchByStudentCodeInput) => {
  return useQuery({
    queryKey: [CACHE_KEYS.STUDENT_BY_CODE, studentCode],
    queryFn: () => fetchStudentByCode({ studentCode }),
    enabled: !!studentCode,
  });
};
