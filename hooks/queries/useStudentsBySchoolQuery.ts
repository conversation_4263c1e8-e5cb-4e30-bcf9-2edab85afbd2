import { useInfiniteQuery, useQuery } from "@tanstack/react-query";

import { CACHE_KEYS } from "@/lib/cache";
import { supabase } from "@/utils/supabase";

export interface StudentView {
  student_id: number;
  student_code: string;
  school_id: number;
  year: number;
  first_names: string;
  surname: string;
  date_of_birth: string;
  adhd?: string;
  dyslexia?: string;
  spelling_ability?: string;
  reading_ability?: string;
  other_conditions?: string;
  academic_ability?: string;
  deactivated: boolean;
}

export const fetchStudentBySchoolId = async ({
  schoolId,
  page = 0,
  pageSize = 100,
}: {
  schoolId?: number;
  page?: number;
  pageSize?: number;
}) => {
  const from = page * pageSize;
  const to = from + pageSize - 1;

  const { data, error, count } = await supabase
    .from("student")
    .select(
      `
      student_id, 
      student_code, 
      school_id, 
      first_names, 
      year,
      surname,
      date_of_birth, 
      adhd,
      dyslexia,
      spelling_ability,
      reading_ability,
      other_conditions,
      academic_ability,
      deactivated
    `,
      { count: "exact" }
    )
    .eq("school_id", schoolId)
    .eq("deactivated", false)
    .order("surname_lower", { ascending: true })
    .range(from, to);

  if (error) {
    throw new Error(error.message);
  }

  return {
    data: data as StudentView[],
    totalCount: count ?? 0,
  };
};

export const useStudentsBySchoolQuery = ({
  schoolId,
  page = 0,
  pageSize = 100,
}: {
  schoolId?: number;
  page?: number;
  pageSize?: number;
}) => {
  return useQuery({
    queryKey: [CACHE_KEYS.STUDENTS_BY_SCHOOL, schoolId, page, pageSize],
    queryFn: () => fetchStudentBySchoolId({ schoolId, page, pageSize }),
    staleTime: 5 * 60 * 1000,
    placeholderData: (previousData) => previousData,
  });
};

export const useInfiniteStudentsBySchoolQuery = ({
  schoolId,
  pageSize = 100,
}: {
  schoolId?: number;
  pageSize?: number;
}) => {
  return useInfiniteQuery({
    queryKey: [CACHE_KEYS.STUDENTS_BY_SCHOOL, "infinite", schoolId, pageSize],
    queryFn: ({ pageParam = 0 }) =>
      fetchStudentBySchoolId({ schoolId, page: pageParam, pageSize }),
    initialPageParam: 0,
    getNextPageParam: (lastPage, allPages) => {
      const totalLoaded = allPages.reduce(
        (sum, page) => sum + page.data.length,
        0
      );
      return totalLoaded < lastPage.totalCount ? allPages.length : undefined;
    },
    staleTime: 5 * 60 * 1000,
  });
};
