// Fetch additional student data
import { useQuery } from "@tanstack/react-query";

import { CACHE_KEYS } from "@/lib/cache";
import { supabase } from "@/utils/supabase";

export interface ICreditBySchool {
  id: number;
  student_id: number;
  school_id: number;
  org_id: number;
  assessment_id?: string;
  status: string;
  redeemed_at?: string;
  created_at: string;
}

export const fetchCreditBySchool = async (schoolId: number) => {
  const { data, error } = await supabase
    .from("credits")
    .select(
      "id, student_id, school_id, org_id, assessment_id, status, redeemed_at, created_at"
    )
    .eq("school_id", schoolId);

  if (error) {
    throw new Error(error.message);
  }

  return data as ICreditBySchool[];
};

export const useCreditsBySchoolQuery = ({ schoolId }: { schoolId: number }) => {
  return useQuery({
    queryKey: [CACHE_KEYS.CREDITS_BY_SCHOOL, schoolId],
    queryFn: () => fetchCreditBySchool(schoolId),
  });
};
