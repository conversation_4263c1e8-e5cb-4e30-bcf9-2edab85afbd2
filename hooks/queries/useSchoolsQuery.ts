import { useQuery } from "@tanstack/react-query";

import { CACHE_KEYS } from "@/lib/cache";
import { InviteStatus } from "@/types/admin";
import { supabase } from "@/utils/supabase";

export interface SchoolDetailView {
  school_id: number;
  school_name: string;
  admin_name: string;
  admin_email: string;
  invite_status: InviteStatus;
  org_name?: string;
  org_id?: number;
  location: string;
  school_type: string;
  school_age: number;
  unassigned_credits: number;
  total_credits: number;
  redeemed_credits: number;
  student_count: number;
}

export const fetchSchools = async () => {
  const { data, error } = await supabase
    .from("view_school_details")
    .select(
      `school_id, 
    school_name, 
    admin_name, 
    admin_email, 
    invite_status,
    org_name, 
    org_id,
    location,
    school_type,
    school_age,
    unassigned_credits,
    total_credits,
    redeemed_credits,
    student_count`
    )
    .order("created_at", { ascending: false });

  const typedData = data as SchoolDetailView[];

  if (error) {
    throw new Error(error.message);
  }

  return typedData;
};

export const useSchoolsQuery = () => {
  return useQuery({ queryKey: [CACHE_KEYS.SCHOOLS], queryFn: fetchSchools });
};
