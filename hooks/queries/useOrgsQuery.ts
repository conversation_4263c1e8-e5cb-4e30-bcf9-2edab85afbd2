import { useQuery } from "@tanstack/react-query";

import { CACHE_KEYS } from "@/lib/cache";
import { InviteStatus } from "@/types/admin";
import { supabase } from "@/utils/supabase";

export interface OrgDetailView {
  org_id: number;
  org_name: string;
  admin_name: string;
  admin_email: string;
  invite_status: InviteStatus;
  school_count: number;
}

export const fetchOrgs = async () => {
  const { data, error } = await supabase
    .from("view_organisation_details")
    .select(
      `org_id, 
      org_name, 
      admin_name, 
      admin_email, 
      invite_status,
      school_count`
    )
    .order("created_at", { ascending: false });

  const typedData = data as OrgDetailView[];

  if (error) {
    throw new Error(error.message);
  }

  return typedData;
};

export const useOrgsQuery = () => {
  return useQuery({ queryKey: [CACHE_KEYS.ORGS], queryFn: fetchOrgs });
};
