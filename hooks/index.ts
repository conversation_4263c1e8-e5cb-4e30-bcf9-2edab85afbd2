import { SupabaseClient } from "@supabase/supabase-js";
import { create } from "zustand";

import {
  FeedbackContext,
  FileField,
  INudge,
  IScaleItemGroupRecord,
  IScaleItemRecord,
} from "@/data/types";

// -------------------------
// Types
// -------------------------
type FeedbackType = {
  isVisible: boolean;
  context: FeedbackContext;
  breakdown?: boolean[];
};
type NudgeFeedbackType = FeedbackType & {
  audio: FileField | null;
  text: string | null;
};

export interface IAssessmentStore {
  user: object;
  attempts: number;
  totalScore: number;
  assessmentProgress?: object;
  nudge?: NudgeFeedbackType;
  feedback: FeedbackType;
  maxIncorrectAllowedForScale: number;
  scales: string[] | number[];
  scalesComplete: string[] | number[];
  scalesUnlocked: string[] | number[];
  isCompleteScaleRunning: boolean;
  resetAttempts: () => any;
  incrementAttempts: () => any;
  setAssessmentScales: (idList: string[] | number[]) => void;
  setFeedback: (options: FeedbackType) => void;
  setNudgeData: ({ isVisible, audio, text, context }: NudgeFeedbackType) => any;
  resetFeedback: () => void;
  resetNudgeData: () => void;
  setMaxIncorrectAllowedForScale: (maxIncorrect: number) => void;
  setScaleComplete: ({
    callback,
    scaleId,
  }: {
    callback: (args?: any) => any;
    scaleId: string;
  }) => Promise<void>;
  setScaleUnlocked: (scaleId: string) => void;
  overwriteAssessmentProgress: (args?: any) => any;
  setAssessmentProgress: (args?: any) => any;
  setTotalScore: (score: number) => void;
  resetTotalScore: () => void;
  startCompleteScale: () => void;
  stopCompleteScale: () => void;
  resetAssessmentStore: () => void;
}

// -------------------------
// State + State Hooks
// -------------------------
export const feedbackStateDefault: FeedbackType = {
  isVisible: false,
  context: "error",
};
const nudgeStateDefault: NudgeFeedbackType = {
  isVisible: false,
  context: "warning",
  audio: null,
  text: null,
};
const nudgeStateDisabled: NudgeFeedbackType = {
  isVisible: false,
  context: "warning",
  audio: null,
  text: null,
};

const defaultState = {
  user: {},
  attempts: 0,
  totalScore: 0,
  assessmentProgress: {},
  nudge: nudgeStateDefault,
  feedback: feedbackStateDefault,
  maxIncorrectAllowedForScale: 0,
  scales: [],
  scalesUnlocked: [],
  scalesComplete: [],
  isCompleteScaleRunning: false,
};

export const useAssessmentStore = create<IAssessmentStore>()((set) => ({
  ...defaultState,
  setMaxIncorrectAllowedForScale: (maxIncorrect: number) =>
    set((state) => ({ maxIncorrectAllowedForScale: maxIncorrect })),
  resetNudgeData: () => set((state) => ({ nudge: nudgeStateDefault })),
  resetFeedback: () => set((state) => ({ feedback: feedbackStateDefault })),
  setAssessmentScales: (idList: string[] | number[]) =>
    set((state) => ({ scales: idList })),
  overwriteAssessmentProgress: (data) =>
    set((state) => ({ assessmentProgress: { ...data } })),
  setAssessmentProgress: ({
    supabase,
    callback,
    userId,
    answer,
    metadata,
    assessmentId,
    scaleItem,
    group,
    score,
  }: {
    supabase?: SupabaseClient;
    callback?: (args?: any) => any;
    answer: any;
    metadata?: any;
    userId?: string;
    assessmentId: string | number;
    scaleItem: IScaleItemRecord;
    group: IScaleItemGroupRecord;
    score: number;
  }) => {
    return set((state: any) => {
      // Set variable for consecutive incorrect answers submitted
      let consecutiveIncorrect =
        state?.assessmentProgress?.[assessmentId]?.[group.scaleItemGroupTitle]
          ?.consecutiveIncorrect || 0;

      // Set flag max incorrect answers reached
      let endScaleMaxIncorrect;
      if (
        state.maxIncorrectAllowedForScale > 1 &&
        consecutiveIncorrect > state.maxIncorrectAllowedForScale
      ) {
        endScaleMaxIncorrect = true;
      } else {
        endScaleMaxIncorrect = false;
      }

      // Set variable to tally correct / incorrect answers submitted
      let tallyIncorrect =
        state?.assessmentProgress?.[assessmentId]?.[group.scaleItemGroupTitle]
          ?.incorrect || 0;
      let tallyCorrect =
        state?.assessmentProgress?.[assessmentId]?.[group.scaleItemGroupTitle]
          ?.correct || 0;
      if (score > 0) {
        tallyCorrect += 1;
        consecutiveIncorrect = 0;
        endScaleMaxIncorrect = false;
      } else {
        tallyIncorrect += 1;
        consecutiveIncorrect += 1;
      }
      const _metadata = scaleItem?.scaleItemIsMetadata ? metadata : {};
      const updatedProgress: any = {
        assessmentProgress: {
          ...state.assessmentProgress,
          meta: {
            ...state?.assessmentProgress?.meta,
            ..._metadata,
          },
          answers: {
            [assessmentId]: {
              ...state?.assessmentProgress?.answers?.[assessmentId],
              [group?.scaleItemGroupTitle]: {
                ...state?.assessmentProgress?.answers?.[assessmentId]?.[
                  group.scaleItemGroupTitle
                ],
                endScaleMaxIncorrect,
                consecutiveIncorrect,
                correct: tallyCorrect,
                incorrect: tallyIncorrect,
                [scaleItem.scaleItemTitle]: {
                  ...state?.assessmentProgress?.answers?.[assessmentId]?.[
                    group.scaleItemGroupTitle
                  ]?.[scaleItem.scaleItemTitle],
                  answer,
                  score,
                },
              },
            },
          },
        },
      };
      const newTotalScore = group.scaleItemGroupIsSample
        ? state.totalScore
        : state.totalScore + score;

      return { ...updatedProgress, totalScore: newTotalScore };
    });
  },
  setFeedback: (options: FeedbackType) =>
    set((state) => ({ feedback: { ...state.feedback, ...options } })),
  setNudgeData: (nudgeData: NudgeFeedbackType) =>
    set((state) => ({ nudge: nudgeStateDisabled })),
  // ! Temporarily removed
  // set((state) => ({ nudge: nudgeData })),
  setScaleComplete: async ({ scaleId, callback }) => {
    await callback();

    return set((state) => {
      const nextScale =
        state.scales[state.scales.indexOf(scaleId as never) + 1];

      const updatedProgress = {
        scalesComplete: [...state.scalesComplete, scaleId] as any[],
        scalesUnlocked: [...state.scalesUnlocked, nextScale] as any[],
      };

      localStorage.setItem(
        "scales_unlocked",
        JSON.stringify(updatedProgress?.scalesUnlocked)
      );
      localStorage.setItem("next_scale", String(nextScale));

      return {
        ...state,
        ...updatedProgress,
      };
    });
  },
  setScaleUnlocked: (scaleId: string | number) =>
    set((state) => ({
      scalesUnlocked: [...state.scalesUnlocked, scaleId] as any[],
    })),
  resetAttempts: () => set((state) => ({ attempts: 0 })),
  incrementAttempts: () => set((state) => ({ attempts: state.attempts + 1 })),
  setTotalScore: (score: number) => set(() => ({ totalScore: score })),
  resetTotalScore: () => set(() => ({ totalScore: 0 })),
  startCompleteScale: () => set({ isCompleteScaleRunning: true }),
  stopCompleteScale: () => set({ isCompleteScaleRunning: false }),
  resetAssessmentStore: () => set(() => ({ ...defaultState })),
}));
