import "dotenv/config";

import type { IGraphQLConfig } from "graphql-config";

const config: IGraphQLConfig = {
  schema: [
    {
      "https://graphql.datocms.com": {
        headers: {
          Authorization: `Bearer ${process.env.NEXT_PUBLIC_DATO_CMS_API_KEY}`,
          "X-Exclude-Invalid": "true",
        },
      },
    },
  ],
  documents: ["./**/*.graphql"],
  extensions: {
    codegen: {
      generates: {
        "types/graphql/": {
          preset: "client",
          presetConfig: {
            fragmentMasking: { unmaskFunctionName: "getFragmentData" },
          },
          config: {
            strictScalars: true,
            scalars: {
              BooleanType: "boolean",
              CustomData: "Record<string, unknown>",
              Date: "string",
              DateTime: "string",
              FloatType: "number",
              IntType: "number",
              ItemId: "string",
              J<PERSON><PERSON><PERSON>: "unknown",
              MetaTagAttributes: "Record<string, string>",
              UploadId: "string",
            },
          },
        },
      },
    },
  },
};

export default config;
