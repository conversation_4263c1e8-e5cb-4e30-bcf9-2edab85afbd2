import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/Admin";
import { SchoolAdmin } from "@/components/Admin/SchoolAdmin/SchoolAdmin";
import { AdminLayout } from "@/components/Layouts/AdminLayout";
import { getUser } from "@/server/admin/getUser";

export default async function SchoolAdminPage() {
  const user = await getUser();
  return (
    <AdminLayout
      header={<AdminHeader type="school" />}
      hero={
        <AdminHero
          heading="Account"
          description="Manage your credits, details and data"
        />
      }
    >
      <SchoolAdmin user={user} />
    </AdminLayout>
  );
}
