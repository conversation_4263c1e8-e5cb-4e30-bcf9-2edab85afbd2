import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/Admin";
import { AnalysisTable } from "@/components/Admin/AnalysisTable/AnalysisTable";
import { AdminLayout } from "@/components/Layouts/AdminLayout";

export default async function SchoolAnalysisPage() {
  return (
    <AdminLayout
      header={<AdminHeader type="school" />}
      hero={
        <AdminHero
          heading="Analysis"
          description={
            "Results from all your completed tests. Sort by any column or click on a name to view their full report."
          }
        />
      }
    >
      <AnalysisTable />
    </AdminLayout>
  );
}
