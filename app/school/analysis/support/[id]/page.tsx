import { Container } from "@chakra-ui/react";
import { draftMode } from "next/headers";

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/Admin/";
import { FlagNotification } from "@/components/Admin/AnalysisChart/FlagNotification";
import { PDFDownloadMenu } from "@/components/Admin/PDFMenu/PDFMenu";
import { RecommendationsTabs } from "@/components/Admin/Recommendations/RecommendationsTabs";
import { StudentReportTabs } from "@/components/Admin/StudentReportTabs/StudentReportTabs";
import { AdminLayout } from "@/components/Layouts/AdminLayout";
import { DATO_PAGING_LIMIT } from "@/constants/constants";
import { fetchStudentByCode } from "@/hooks/queries/useStudentByCodeQuery";
import { theme } from "@/styles/theme";
import {
  RecommendationCopyDocument,
  RecommendationTypeIconsDocument,
  ReportRecommendationsCountDocument,
  ReportRecommendationsDocument,
  ReportRecommendationsQuery,
} from "@/types/graphql/graphql";
import queryDatoCMS from "@/utils/cms/query-dato-cms";

export default async function Report({ params }: { params: { id: string } }) {
  const { isEnabled: isDraft } = draftMode();
  const student = await fetchStudentByCode({ studentCode: params.id });
  const recommendationsCount = await queryDatoCMS(
    ReportRecommendationsCountDocument,
    {},
    isDraft
  );
  const numRecommendations = recommendationsCount._allRecommendationsMeta.count;

  let allRecommendations: ReportRecommendationsQuery["allRecommendations"] = [];

  for (let skip = 0; skip < numRecommendations; skip += DATO_PAGING_LIMIT) {
    const batch = await queryDatoCMS(
      ReportRecommendationsDocument,
      { first: DATO_PAGING_LIMIT, skip },
      isDraft
    );
    allRecommendations = [...allRecommendations, ...batch.allRecommendations];
  }

  const recommendationIcons = await queryDatoCMS(
    RecommendationTypeIconsDocument,
    {},
    isDraft
  );

  const recommendationCopy = await queryDatoCMS(
    RecommendationCopyDocument,
    {},
    isDraft
  );

  return (
    <AdminLayout
      header={<AdminHeader type="school" />}
      hero={
        <AdminHero
          heading={`${student.surname}, ${student.first_names.substring(1, 0)}`}
          description={`Year ${student.year}`}
          belowDescription={<PDFDownloadMenu studentCode={params?.id} />}
          backLink="Back to Analysis"
          backLinkUrl="/school/analysis"
        />
      }
      tabs={<StudentReportTabs studentCode={params.id} currentTab="support" />}
    >
      <Container maxW={theme.max_admin_width_narrow}>
        <FlagNotification studentCode={params.id} />
        <RecommendationsTabs
          studentCode={params.id}
          recommendations={allRecommendations}
          recommendationIcons={recommendationIcons}
          recommendationCopy={recommendationCopy}
        />
      </Container>
    </AdminLayout>
  );
}
