import { Container } from "@chakra-ui/react";
import { draftMode } from "next/headers";
import { redirect } from "next/navigation";

import { AdminHeader, AdminHero } from "@/components/Admin/";
import { AnalysisWrapper } from "@/components/Admin/AnalysisChart/AnalysisWrapper";
import { FlagNotification } from "@/components/Admin/AnalysisChart/FlagNotification";
import { AreasOfNeed } from "@/components/Admin/AreasOfNeed/AreasOfNeed";
import { LevelOfNeed } from "@/components/Admin/LevelOfNeed/LevelOfNeed";
import { PDFDownloadMenu } from "@/components/Admin/PDFMenu/PDFMenu";
import { StudentReportTabs } from "@/components/Admin/StudentReportTabs/StudentReportTabs";
import { AdminLayout } from "@/components/Layouts/AdminLayout";
import { fetchAnalysisById } from "@/hooks/queries/useAnalysisByIdQuery";
import { fetchStudentByCode } from "@/hooks/queries/useStudentByCodeQuery";
import { theme } from "@/styles/theme";
import {
  AreasOfNeedDocument,
  AreasOfNeedFallbackDocument,
  GeneralResultsDocument,
  RiskLevelsDocument,
} from "@/types/graphql/graphql";
import queryDatoCMS from "@/utils/cms/query-dato-cms";

export default async function Report({ params }: { params: { id: string } }) {
  const { isEnabled: isDraft } = draftMode();
  const student = await fetchStudentByCode({ studentCode: params.id });
  const riskLevels = await queryDatoCMS(RiskLevelsDocument, undefined, isDraft);
  const reportData = await fetchAnalysisById({ studentCode: params.id });
  const scoringData = reportData?.result_data.cognitiveProfile || null;
  const isReportHidden = reportData?.hide_report;
  const aonFallback = await queryDatoCMS(
    AreasOfNeedFallbackDocument,
    undefined,
    isDraft
  );
  const areasOfNeed = await queryDatoCMS(
    AreasOfNeedDocument,
    undefined,
    isDraft
  );
  const generalResults = await queryDatoCMS(
    GeneralResultsDocument,
    undefined,
    isDraft
  );

  if (student.deactivated) {
    redirect("/school");
  }
  return (
    <AdminLayout
      header={<AdminHeader type="school" />}
      hero={
        <AdminHero
          heading={`${student.surname}, ${student.first_names.substring(1, 0)}`}
          description={`Year ${student.year}`}
          belowDescription={<PDFDownloadMenu studentCode={params?.id} />}
          backLink="Back to Analysis"
          backLinkUrl="/school/analysis"
        />
      }
      tabs={<StudentReportTabs studentCode={params.id} currentTab="results" />}
    >
      <Container maxW={theme.max_admin_width_narrow}>
        <FlagNotification studentCode={params.id} />
        <AnalysisWrapper studentCode={params.id} />
        {!isReportHidden ? (
          <>
            <LevelOfNeed
              studentCode={params.id}
              generalResults={generalResults}
              riskLevels={riskLevels}
            />
            <AreasOfNeed
              scoringData={scoringData}
              areasOfNeed={areasOfNeed}
              areasOfNeedFallback={aonFallback}
              student={student}
            />
          </>
        ) : null}
      </Container>
    </AdminLayout>
  );
}
