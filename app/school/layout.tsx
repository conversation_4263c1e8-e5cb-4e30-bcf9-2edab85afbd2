import { createServerComponentClient } from "@supabase/auth-helpers-nextjs";
import type { Metadata } from "next";
import { cookies } from "next/headers";
import { redirect } from "next/navigation";

import { SchoolUserProvider } from "@/context/school-context";
import { AdminRole } from "@/types/admin";

export const metadata: Metadata = {
  title: "Talamo admin",
};

export default async function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const cookieStore = cookies();
  const supabase = createServerComponentClient({ cookies: () => cookieStore });

  //Check user is logged in, otherwise redirect
  const { data: userData, error: userError } = await supabase.auth.getUser();

  if (userError || !userData?.user) {
    redirect("/login");
  }

  const orgId = userData.user.user_metadata?.orgId;

  //Check logged in user is a schools admin, otherwise redirect
  const user = userData.user;

  const { data: adminData, error: adminError } = await supabase
    .from("admins")
    .select("*")
    .eq("user_id", user.id)
    .single();

  const userRole = adminData?.permissions;

  if (adminError || !adminData || userRole !== AdminRole.SchoolAdmin) {
    redirect("/login");
  }

  return (
    <SchoolUserProvider
      initialUser={{
        id: userData.user.id,
        email: userData.user.email || "",
        orgId,
      }}
    >
      {children}
    </SchoolUserProvider>
  );
}
