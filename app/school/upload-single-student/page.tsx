import Link from "next/link";

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/Admin";
import { UploadSingleStudent } from "@/components/Admin/UploadStudents/UploadSingleStudent";
import { AdminLayout } from "@/components/Layouts/AdminLayout";
import { getUser } from "@/server/admin/getUser";
import { theme } from "@/styles/theme";

export default async function UploadSingleStudentPage() {
  const user = await getUser();
  return (
    <AdminLayout
      header={<AdminHeader type="school" />}
      hero={
        <AdminHero
          heading="Add student"
          description={
            <>
              If you want to add more than 1 student at a time, use our{" "}
              <Link
                href="/school/bulk-upload-students"
                target="_blank"
                style={{ color: theme.colors.tertiary.yellow_02.hex }}
              >
                import tool
              </Link>{" "}
              to add as many as you need
            </>
          }
        />
      }
    >
      <UploadSingleStudent user={user} />
    </AdminLayout>
  );
}
