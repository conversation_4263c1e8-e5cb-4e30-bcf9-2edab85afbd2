import { Container } from "@chakra-ui/react";
import { redirect } from "next/navigation";

import { Admin<PERSON>eader, <PERSON>min<PERSON><PERSON> } from "@/components/Admin/";
import { PDFDownloadMenu } from "@/components/Admin/PDFMenu/PDFMenu";
import { StudentAccessCode } from "@/components/Admin/StudentAccessCode/StudentAccessCode";
import { StudentDeletion } from "@/components/Admin/StudentDeletion/StudentDeletion";
import { StudentDetail } from "@/components/Admin/StudentDetail/StudentDetail";
import { StudentReportTabs } from "@/components/Admin/StudentReportTabs/StudentReportTabs";
import { AdminLayout } from "@/components/Layouts/AdminLayout";
import { fetchStudentByCode } from "@/hooks/queries/useStudentByCodeQuery";
import { theme } from "@/styles/theme";

export default async function StudentDetailPage({
  params,
}: {
  params: { id: string };
}) {
  const student = await fetchStudentByCode({ studentCode: params.id });

  if (student.deactivated) {
    redirect("/school");
  }

  return (
    <AdminLayout
      header={<AdminHeader type="school" />}
      hero={
        <AdminHero
          heading={`${student.surname}, ${student.first_names.substring(1, 0)}`}
          variant="test"
          description={`Year ${student.year}`}
          belowDescription={<PDFDownloadMenu studentCode={params?.id} />}
          backLink="Back to Analysis"
          backLinkUrl="/school/analysis"
        />
      }
      tabs={<StudentReportTabs studentCode={params.id} currentTab="about" />}
    >
      <Container maxW={theme.max_admin_width_narrow}>
        <StudentAccessCode studentCode={params.id} />
        <StudentDetail studentCode={params.id} />
        <StudentDeletion studentCode={params.id} />
      </Container>
    </AdminLayout>
  );
}
