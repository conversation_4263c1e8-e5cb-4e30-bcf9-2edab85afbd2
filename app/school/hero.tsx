"use client";

import { AdminHeader, Admin<PERSON>ero } from "@/components/Admin";
import { AdminLayout } from "@/components/Layouts/AdminLayout";
import { useSchoolUser } from "@/context/school-context";
import { useCreditsBySchoolQuery } from "@/hooks/queries/useCreditsBySchoolId";
import { useStudentsBySchoolQuery } from "@/hooks/queries/useStudentsBySchoolQuery";

export const TestHero = ({ children }: { children: React.ReactNode }) => {
  const { user } = useSchoolUser();
  const schoolId = user?.orgId || 0;
  const { data: creditData, isLoading: creditDataLoading } =
    useCreditsBySchoolQuery({ schoolId });
  const { data: studentData } = useStudentsBySchoolQuery({ schoolId });

  const numStudents = studentData?.data.length || 0;

  if (creditDataLoading || !creditData) {
    return null;
  }

  const unassignedCredits = creditData.filter(
    (credit) => credit.status === "Unassigned"
  );

  return (
    <AdminLayout
      header={<AdminHeader type="school" />}
      hero={
        <AdminHero
          heading="Test"
          description={`${unassignedCredits.length || 0} credits remaining`}
          variant="test"
          numStudents={numStudents}
        />
      }
    >
      {children}
    </AdminLayout>
  );
};
