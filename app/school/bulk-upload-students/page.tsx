import Link from "next/link";

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/Admin";
import { UploadStudents } from "@/components/Admin/UploadStudents/UploadStudents";
import { AdminLayout } from "@/components/Layouts/AdminLayout";
import { getUser } from "@/server/admin/getUser";
import { theme } from "@/styles/theme";

export default async function SchoolAdmin() {
  const user = await getUser();
  return (
    <>
      <AdminLayout
        header={<AdminHeader type="school" />}
        hero={
          <AdminHero
            heading="Import students"
            description={
              <>
                Don’t forget to{" "}
                <Link
                  href="https://www.talamo.co.uk/wiki/using-talamo/adding-students"
                  target="_blank"
                  style={{ color: theme.colors.tertiary.yellow_02.hex }}
                >
                  download our template
                </Link>{" "}
                to do this!
              </>
            }
            backLink="Back to Test"
            backLinkUrl="/school"
          />
        }
      >
        <UploadStudents user={user} />
      </AdminLayout>
    </>
  );
}
