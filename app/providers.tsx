"use client";

import { ChakraProvider } from "@chakra-ui/react";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";

import { Student, StudentProvider } from "@/context/student-context";
import { objectivity } from "@/fonts/fonts";
import { PostHogProvider } from "@/providers/posthog";
import { extendedTheme } from "@/styles/extendedTheme";

const queryClient = new QueryClient();

interface IProviderProps {
  children: React.ReactNode;
  initialStudent: Student;
}

export const Providers = ({ initialStudent, children }: IProviderProps) => {
  return (
    <>
      <style jsx global>
        {`
          :root {
            --font-objectivity: ${objectivity.style.fontFamily};
          }
        `}
      </style>
      <QueryClientProvider client={queryClient}>
        <PostHogProvider>
          <StudentProvider initialStudent={initialStudent}>
            <ChakraProvider theme={extendedTheme}>{children}</ChakraProvider>
            <ReactQueryDevtools initialIsOpen={false} />
          </StudentProvider>
        </PostHogProvider>
      </QueryClientProvider>
    </>
  );
};
