import "../styles/globals-tw.css";

import { Metadata } from "next";

import { objectivity } from "@/fonts/fonts";
import { getStudentUser } from "@/lib/get-student-user";

import { Providers } from "./providers";

export const metadata: Metadata = {
  title: "Talamo",
};

export default async function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const student = await getStudentUser();
  return (
    <html lang="en" className={objectivity.className} suppressHydrationWarning>
      <body className="bg-secondary-purple_01 text-white h-full overscroll-none">
        <Providers initialStudent={student}>{children}</Providers>
      </body>
    </html>
  );
}
