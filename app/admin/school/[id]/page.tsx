import { Box } from "@chakra-ui/react";
import { CheckCircleIcon, ClockIcon } from "@heroicons/react/24/solid";

import { <PERSON><PERSON><PERSON>eader, AdminHero, AdminPanel } from "@/components/Admin/";
import { CreditsPanel } from "@/components/Admin/CreditsPanel/CreditsPanel";
import { StudentsPanel } from "@/components/Admin/StudentsPanel/StudentsPanel";
import { AdminLayout } from "@/components/Layouts/AdminLayout";
import { Text } from "@/components/v2/Text/Text";
import { fetchSchoolById } from "@/hooks/queries/useSchoolsByIdQuery";
import { theme } from "@/styles/theme";
import { InviteStatus } from "@/types/admin";

export default async function School({ params }: { params: { id: number } }) {
  const school = await fetchSchoolById({ schoolId: params.id });

  return (
    <AdminLayout
      header={<AdminHeader type="admin" />}
      hero={
        <AdminHero
          heading={school.school_name}
          backLink="Back to Schools"
          backLinkUrl="/admin"
        />
      }
    >
      <Box>
        <Box mb={theme.spacing.md.px}>
          <AdminPanel>
            <Text element="h4" variant="md" mb={theme.spacing.sm.px}>
              School details
            </Text>
            <Box display="flex" gap={theme.spacing.sm.px} alignItems="center">
              <Text element="h4" variant="sm">
                Contact:
              </Text>
              {school.invite_status === InviteStatus.Pending ? (
                <ClockIcon
                  width={16}
                  height={16}
                  color={theme.colors.ui.alert_orange_01.hex}
                />
              ) : (
                <CheckCircleIcon
                  width={16}
                  height={16}
                  color={theme.colors.ui.alert_green_01.hex}
                />
              )}
              <Text variant="md">{school.admin_email}</Text>
            </Box>
            <Box display="flex" gap={theme.spacing.sm.px} alignItems="center">
              <Text element="h4" variant="sm">
                Students:
              </Text>
              <Text variant="md">{school.student_count}</Text>
            </Box>
            <Box display="flex" gap={theme.spacing.sm.px} alignItems="center">
              <Text element="h4" variant="sm">
                Type:
              </Text>
              <Text variant="md">{school.school_type}</Text>
            </Box>
            <Box display="flex" gap={theme.spacing.sm.px} alignItems="center">
              <Text element="h4" variant="sm">
                Location:
              </Text>
              <Text variant="md">{school.location}</Text>
            </Box>
            {school.org_name && (
              <Box display="flex" gap={theme.spacing.sm.px} alignItems="center">
                <Text element="h4" variant="sm">
                  Organisation:
                </Text>
                <Text variant="md">{school.org_name}</Text>
              </Box>
            )}
          </AdminPanel>
        </Box>
        <CreditsPanel schoolId={school.school_id} />
        <StudentsPanel schoolId={school.school_id} />
      </Box>
    </AdminLayout>
  );
}
