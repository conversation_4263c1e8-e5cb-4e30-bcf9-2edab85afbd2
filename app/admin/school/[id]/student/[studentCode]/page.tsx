import { Container } from "@chakra-ui/react";

import { Ad<PERSON><PERSON>eader, <PERSON>minH<PERSON> } from "@/components/Admin/";
import { PDFDownloadMenu } from "@/components/Admin/PDFMenu/PDFMenu";
import { StudentAccessCode } from "@/components/Admin/StudentAccessCode/StudentAccessCode";
import { StudentDeletion } from "@/components/Admin/StudentDeletion/StudentDeletion";
import { StudentDetail } from "@/components/Admin/StudentDetail/StudentDetail";
import { StudentReportTabs } from "@/components/Admin/StudentReportTabs/StudentReportTabs";
import { AdminLayout } from "@/components/Layouts/AdminLayout";
import { fetchStudentByCode } from "@/hooks/queries/useStudentByCodeQuery";
import { theme } from "@/styles/theme";

export default async function Report({
  params,
}: {
  params: { studentCode: string; id: string };
}) {
  const student = await fetchStudentByCode({ studentCode: params.studentCode });

  return (
    <AdminLayout
      header={<AdminHeader type="admin" />}
      hero={
        <AdminHero
          heading={`${student.surname}, ${student.first_names.substring(1, 0)}`}
          description={`Year ${student.year}`}
          belowDescription={
            <PDFDownloadMenu isAdminView studentCode={params?.id} />
          }
          backLink="Back to School"
          backLinkUrl={`/admin/school/${params.id}`}
        />
      }
      tabs={
        <StudentReportTabs
          studentCode={params.studentCode}
          currentTab="about"
          isAdminView
          schoolId={params.id}
        />
      }
    >
      <Container maxW={theme.max_admin_width_narrow}>
        <StudentAccessCode studentCode={params.studentCode} />
        <StudentDetail studentCode={params.studentCode} />
        <StudentDeletion studentCode={params.studentCode} />
      </Container>
    </AdminLayout>
  );
}
