import { Container } from "@chakra-ui/react";
import { draftMode } from "next/headers";

import { <PERSON><PERSON><PERSON>ead<PERSON>, <PERSON><PERSON>H<PERSON> } from "@/components/Admin/";
import { AnalysisWrapper } from "@/components/Admin/AnalysisChart/AnalysisWrapper";
import { FlagNotification } from "@/components/Admin/AnalysisChart/FlagNotification";
import { AreasOfNeed } from "@/components/Admin/AreasOfNeed/AreasOfNeed";
import { LevelOfNeed } from "@/components/Admin/LevelOfNeed/LevelOfNeed";
import { PDFDownloadMenu } from "@/components/Admin/PDFMenu/PDFMenu";
import { StudentReportTabs } from "@/components/Admin/StudentReportTabs/StudentReportTabs";
import { AdminLayout } from "@/components/Layouts/AdminLayout";
import { fetchAnalysisById } from "@/hooks/queries/useAnalysisByIdQuery";
import { fetchStudentByCode } from "@/hooks/queries/useStudentByCodeQuery";
import { theme } from "@/styles/theme";
import {
  AreasOfNeedDocument,
  AreasOfNeedFallbackDocument,
  GeneralResultsDocument,
  RiskLevelsDocument,
} from "@/types/graphql/graphql";
import queryDatoCMS from "@/utils/cms/query-dato-cms";

export default async function Report({
  params,
}: {
  params: { studentCode: string; id: string };
}) {
  const { isEnabled: isDraft } = draftMode();
  const student = await fetchStudentByCode({ studentCode: params.studentCode });
  const riskLevels = await queryDatoCMS(RiskLevelsDocument, undefined, isDraft);
  const reportData = await fetchAnalysisById({
    studentCode: params.studentCode,
  });
  const scoringData = reportData?.result_data.cognitiveProfile || null;
  const aonFallback = await queryDatoCMS(
    AreasOfNeedFallbackDocument,
    undefined,
    isDraft
  );
  const areasOfNeed = await queryDatoCMS(
    AreasOfNeedDocument,
    undefined,
    isDraft
  );
  const generalResults = await queryDatoCMS(
    GeneralResultsDocument,
    undefined,
    isDraft
  );
  return (
    <AdminLayout
      header={<AdminHeader type="admin" />}
      hero={
        <AdminHero
          heading={`${student.surname}, ${student.first_names.substring(1, 0)}`}
          description={`Year ${student.year}`}
          belowDescription={
            <PDFDownloadMenu isAdminView studentCode={params?.studentCode} />
          }
          backLink="Back to School"
          backLinkUrl={`/admin/school/${params.id}`}
        />
      }
      tabs={
        <StudentReportTabs
          studentCode={params.studentCode}
          currentTab="results"
          isAdminView
          schoolId={params.id}
        />
      }
    >
      <Container maxW={theme.max_admin_width_narrow}>
        <FlagNotification studentCode={params.studentCode} isAdmin />
        <AnalysisWrapper studentCode={params.studentCode} isAdmin />
        <LevelOfNeed
          generalResults={generalResults}
          studentCode={params.studentCode}
          riskLevels={riskLevels}
          isAdmin
        />
        <AreasOfNeed
          scoringData={scoringData}
          areasOfNeed={areasOfNeed}
          areasOfNeedFallback={aonFallback}
          student={student}
        />
      </Container>
    </AdminLayout>
  );
}
