import { Box } from "@chakra-ui/react";

import { Ad<PERSON><PERSON>eader, Admin<PERSON><PERSON> } from "@/components/Admin/";
import { Schools } from "@/components/Admin/AddSchool/Schools";
import ExportButton from "@/components/Admin/DownloadCreditsCSV/DownloadCreditsCSV";
import { AdminLayout } from "@/components/Layouts/AdminLayout";
import { fetchSchools } from "@/hooks/queries/useSchoolsQuery";
import { theme } from "@/styles/theme";

export default async function Admin() {
  const schools = await fetchSchools();
  const numSchools = schools?.length || 0;

  return (
    <AdminLayout
      header={<AdminHeader type="admin" />}
      hero={
        <AdminHero
          heading="Schools"
          variant={numSchools > 0 ? "addSchool" : undefined}
        />
      }
    >
      <Box mb={theme.spacing.lg.px} display="flex" justifyContent="flex-end">
        <ExportButton />
      </Box>

      <Schools numRows={numSchools} />
    </AdminLayout>
  );
}
