import "@/styles/globals.css";

import { createServerComponentClient } from "@supabase/auth-helpers-nextjs";
import type { Metadata } from "next";
import { cookies } from "next/headers";
import { redirect } from "next/navigation";

import { AdminRole } from "@/types/admin";

export const metadata: Metadata = {
  title: "Talamo admin",
};

export default async function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const cookieStore = cookies();
  const supabase = createServerComponentClient({ cookies: () => cookieStore });

  //Check user is logged in, otherwise redirect
  const { data, error } = await supabase.auth.getUser();
  if (error || !data?.user) {
    redirect("/login");
  }

  //Check logged in user is an administrator, otherwise redirect
  const user = data.user;

  const { data: adminData, error: adminError } = await supabase
    .from("admins")
    .select("*")
    .eq("user_id", user.id)
    .single();

  const userRole = adminData?.permissions;

  if (adminError || !adminData || userRole !== AdminRole.Administrator) {
    redirect("/login");
  }

  return <>{children}</>;
}
