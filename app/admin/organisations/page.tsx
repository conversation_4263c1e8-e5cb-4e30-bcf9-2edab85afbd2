import { Box } from "@chakra-ui/react";

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/Admin/";
import { Orgs } from "@/components/Admin/AddOrg/Orgs";
import { AdminLayout } from "@/components/Layouts/AdminLayout";
import { fetchOrgs } from "@/hooks/queries/useOrgsQuery";

export default async function Admin() {
  const orgs = await fetchOrgs();

  const numOrgs = orgs?.length || 0;
  return (
    <AdminLayout
      header={<AdminHeader type="admin" />}
      hero={
        <AdminHero
          heading="Organisations"
          variant={numOrgs > 0 ? "addOrg" : undefined}
        />
      }
    >
      <Box>
        <Orgs numRows={numOrgs} />
      </Box>
    </AdminLayout>
  );
}
