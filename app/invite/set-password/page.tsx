import { Box, Card, Container, VStack } from "@chakra-ui/react";
import { createServerComponentClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";
import Image from "next/image";
import { redirect } from "next/navigation";

import { InputField } from "@/components/InputField/InputField";
import { StarsBg } from "@/components/StarsBg/StarsBg";
import { Button } from "@/components/v2/Button/Button";
import { Logo } from "@/components/v2/Logo/Logo";
import { Text } from "@/components/v2/Text/Text";
import { theme } from "@/styles/theme";

export default async function SetPassword({
  searchParams,
}: {
  searchParams: { message: string };
}) {
  const errorMessage = searchParams.message;
  const cookieStore = cookies();
  const supabase = createServerComponentClient({ cookies: () => cookieStore });
  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    redirect("/login");
  }

  const setPassword = async (formData: FormData) => {
    "use server";
    const password = formData.get("password") as string;
    const cookieStore = cookies();
    const supabase = createServerComponentClient({
      cookies: () => cookieStore,
    });

    const { data, error } = await supabase.auth.updateUser({
      password,
    });

    if (error) {
      return redirect(`/invite/set-password?message=${error.message}`);
    }

    const userType = data.user.user_metadata.orgType;

    return redirect(`/${userType}`);
  };

  return (
    <>
      <Box top="20px" position="absolute" w="100%" left={0}>
        <Box
          data-testid="primary-navigation"
          py={theme.spacing.sm.px}
          zIndex={500}
          width="100%"
          pos="relative"
        >
          <Container width={"100%"} px={30} maxW={"none"}>
            <Logo color={theme.colors.primary.white.hex} />
          </Container>
        </Box>
      </Box>
      <VStack
        justifyContent={"center"}
        alignContent={"center"}
        w="100vw"
        h="100vh"
        pos="relative"
        zIndex={10}
        gap={0}
      >
        <Box>
          <Card
            w={{ sm: "100%", md: "464px" }}
            border="none"
            borderRadius={theme.border.radius.md.px}
            bg={theme.colors.primary.white.hex}
            flex={1}
            overflow="hidden"
            variant="outline"
            p={"32px"}
            css={{
              boxShadow: theme.shadow.box,
            }}
          >
            <Text
              variant="2xl"
              element="h2"
              mb={theme.spacing.sm.px}
              textAlign="center"
            >
              Welcome!
            </Text>
            <Text
              variant="lg"
              element="p"
              mb={theme.spacing.sm.px}
              textAlign="center"
            >
              Before you get started, let’s setup your own password.
            </Text>
            <form action={setPassword}>
              <Text
                element="h3"
                variant="xs"
                textAlign="left"
                alignSelf="flex-start"
                m={0}
                mt={theme.spacing.sm.px}
              >
                Password
              </Text>
              <InputField name="password" type="password" variant="outline" />
              {errorMessage && (
                <Text
                  element="p"
                  variant="sm"
                  textAlign="left"
                  alignSelf="flex-start"
                  mt="8px"
                  color={theme.colors.ui.alert_red_01.hex}
                >
                  {errorMessage}
                </Text>
              )}
              <Box display="flex" justifyContent="center">
                <Button type="submit" size="md" my={theme.spacing.md.px}>
                  Get Started
                </Button>
              </Box>
            </form>
          </Card>
        </Box>
      </VStack>
      <StarsBg zIndex={-1} />
      <Box
        position="absolute"
        bottom={0}
        right={0}
        display={{ base: "none", md: "block" }}
      >
        <Image
          src="/images/auth/sign-up.svg"
          width={400}
          height={400}
          priority
          alt=""
        />
      </Box>
    </>
  );
}
