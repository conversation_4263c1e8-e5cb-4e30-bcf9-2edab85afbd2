import { cookies } from "next/headers";
import Link from "next/link";

import { StartScreen } from "@/components/nova/dashboard/start";
import { Button, NightSkyBg } from "@/components/ui";
import { getNovaAssessment } from "@/lib/get-nova-assessment-data";
import { getShuffledAssessment } from "@/lib/get-shuffled-assessment";

import { StudentHeader } from "./components/StudentHeader";

export default async function AssessmentDashboardPage({ params }) {
  const cookieStore = cookies();
  const sessionId = cookieStore.get("sessionId")?.value;

  const rawAssessment = await getNovaAssessment();
  const assessmentData = await getShuffledAssessment(
    rawAssessment?.novaAssessment,
    sessionId!
  );

  if (!assessmentData) return null;

  return (
    <>
      <div className="mt-4 pr-12 z-50 relative">
        <StudentHeader showLogo showLogout={false} />
      </div>
      <div className="flex flex-col items-center justify-center fixed w-full h-full top-0 left-0 z-10">
        <StartScreen assessmentData={assessmentData} />
      </div>
      <NightSkyBg />
    </>
  );
}
