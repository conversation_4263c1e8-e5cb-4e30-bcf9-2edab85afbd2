"use client";

import { useContext } from "react";

import { Button } from "@/components/ui";
import { Logo } from "@/components/v2/Logo/Logo";
import { StudentContext } from "@/context/student-context";

export const StudentHeader = ({
  showLogo = false,
  showLogout = true,
}: {
  showLogo?: boolean;
  showLogout?: boolean;
}) => {
  const { logout } = useContext(StudentContext);

  return (
    <div className="my-14 flex justify-between px-6 items-center">
      <div>{showLogo && <Logo color="white" />}</div>
      {showLogout && (
        <Button size="sm" onClick={logout}>
          Logout
        </Button>
      )}
    </div>
  );
};
