"use client";

import { useContext, useEffect, useRef } from "react";

import { StudentContext } from "@/context/student-context";
import { useAudioStore } from "@/stores/use-audio-store";

export default function Layout({ children }: { children: React.ReactNode }) {
  const { student } = useContext(StudentContext);

  const audioRef = useRef<HTMLAudioElement>(null);
  const { setRef, setPlaying } = useAudioStore();

  useEffect(() => {
    if (audioRef.current) {
      setRef(audioRef.current);
    }
  }, [setRef]);

  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const handlePlay = () => setPlaying(true);
    const handlePause = () => setPlaying(false);
    const handleEnded = () => setPlaying(false);

    audio.addEventListener("play", handlePlay);
    audio.addEventListener("pause", handlePause);
    audio.addEventListener("ended", handleEnded);

    return () => {
      audio.removeEventListener("play", handlePlay);
      audio.removeEventListener("pause", handlePause);
      audio.removeEventListener("ended", handleEnded);
    };
  }, [setPlaying]);

  return (
    <>
      {children}
      <audio ref={audioRef} />
      <div className="fixed bottom-3 right-3 z-[1000] text-[14px] font-semibold opacity-50">
        {student?.code}
      </div>
    </>
  );
}
