import { cookies } from "next/headers";

import { Scale } from "@/components/nova/scale/scale";
import { getNovaAssessment } from "@/lib/get-nova-assessment-data";
import { getShuffledAssessment } from "@/lib/get-shuffled-assessment";

export default async function Page({
  params,
}: {
  params: { assessmentId: string; scaleId: string };
}) {
  const { assessmentId, scaleId } = params;

  const rawData = await getNovaAssessment();
  const cookieStore = cookies();
  const sessionId = cookieStore.get("sessionId")?.value;

  const shuffledAssessment = await getShuffledAssessment(
    rawData?.novaAssessment,
    sessionId ?? ""
  );

  if (
    !shuffledAssessment ||
    shuffledAssessment.__typename !== "NovaAssessmentRecord"
  ) {
    return <p>Assessment not found</p>;
  }

  const scale = shuffledAssessment.assessmentContent.find(
    (
      content
    ): content is Extract<typeof content, { __typename: "NovaScaleRecord" }> =>
      content.__typename === "NovaScaleRecord" && content.id === scaleId
  );

  if (!scale) {
    return null;
  }

  const scaleItemIds =
    scale.scaleContent
      .filter((item) => item.__typename === "NovaScaleItemRecord")
      .map((item) => item.id) ?? [];

  return (
    <Scale
      assessmentData={shuffledAssessment}
      assessmentId={assessmentId}
      scaleId={scaleId}
      scaleItemIds={scaleItemIds}
    />
  );
}
