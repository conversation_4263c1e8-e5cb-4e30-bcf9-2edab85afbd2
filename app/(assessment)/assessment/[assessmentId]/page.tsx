import { cookies } from "next/headers";

import { AssessmentDashboard } from "@/components/nova/assessment-dashboard/assessment-dashboard";
import { getNovaAssessment } from "@/lib/get-nova-assessment-data";
import { getShuffledAssessment } from "@/lib/get-shuffled-assessment";

export default async function AssessmentDashboardPage({ params }) {
  const { assessmentId } = params;

  const novaAssessment = await getNovaAssessment();
  const cookieStore = cookies();
  const sessionId = cookieStore.get("sessionId")?.value;

  const shuffledAssessment = await getShuffledAssessment(
    novaAssessment?.novaAssessment,
    sessionId ?? ""
  );

  if (!shuffledAssessment) return null;

  return (
    <AssessmentDashboard
      assessmentId={assessmentId}
      assessmentData={shuffledAssessment}
    />
  );
}
