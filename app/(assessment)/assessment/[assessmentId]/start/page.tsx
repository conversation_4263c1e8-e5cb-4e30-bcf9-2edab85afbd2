import { cookies } from "next/headers";

import { AssessmentStart } from "@/components/nova/assessment-start/assessment-start";
import { getNovaAssessment } from "@/lib/get-nova-assessment-data";
import { getShuffledAssessment } from "@/lib/get-shuffled-assessment";

export default async function AssessmentStartPage({ params }) {
  const { assessmentId } = params;

  const novaAssessment = await getNovaAssessment();
  const cookieStore = cookies();
  const sessionId = cookieStore.get("sessionId")?.value;

  const shuffledAssessment = await getShuffledAssessment(
    novaAssessment?.novaAssessment,
    sessionId ?? ""
  );

  if (!shuffledAssessment) return null;

  return (
    <AssessmentStart
      assessmentId={assessmentId}
      assessmentData={shuffledAssessment}
    />
  );
}
