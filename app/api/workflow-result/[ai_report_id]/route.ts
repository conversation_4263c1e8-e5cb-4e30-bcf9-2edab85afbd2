import { supabaseConfig } from "@/data/constants";
import { createClient } from "@supabase/supabase-js";
import { NextRequest, NextResponse } from "next/server";

const supabase = createClient(
  supabaseConfig.supabaseUrl,
  supabaseConfig.supabaseKey
);

// Type definitions for the workflow output
interface Risk {
  dyslexiaRisk: number;
  dyslexiaRationale: string;
  levelOfNeed: number;
  levelOfNeedRationale: string;
}

interface RecommendationLink {
  title?: string;
  url?: string;
}

interface Recommendation {
  id: string;
  testArea: string;
  type: string;
  duration: string;
  description: string;
  links: RecommendationLink[];
}

interface Recommendations {
  inTheClassroom: Recommendation[];
  extraSupport: Recommendation[];
  atHome: Recommendation[];
}

interface WorkflowOutput {
  dyslexia_risk: number;
  recommendations: Recommendations;
  dylexia_rationale: string;
  level_of_need_description: string;
  level_of_need: number;

  // Additional metadata from the workflow
  workflow_metadata?: any;
}

export async function POST(
  request: NextRequest,
  { params }: { params: { ai_report_id: string } }
) {
  try {
    const { ai_report_id: rawAiReportId }: { ai_report_id: string } = params;

    // Trim whitespace from ai_report_id parameter (common issue from n8n)
    const ai_report_id = rawAiReportId?.trim();

    // Validate ai_report_id parameter
    if (!ai_report_id) {
      return NextResponse.json(
        { error: "AI Report ID is required in the URL path" },
        { status: 400 }
      );
    }

    // Get the JSON body from the request
    const body: WorkflowOutput = await request.json();

    // Validate and update the existing ai_report record
    let updateResult = false;
    try {
      // First, check if the record exists
      const { data: existingRecord, error: findError } = await supabase
        .from("ai_report")
        .select("id, status, report_id")
        .eq("id", ai_report_id)
        .single();

      if (findError) {
        // Standard not found error
        return NextResponse.json(
          {
            error: "AI Report record not found",
            details: `No record found with ID: ${ai_report_id}`
          },
          { status: 404 }
        );
      }

      // Update the existing record with workflow results
      const { error: updateError } = await supabase
        .from("ai_report")
        .update({
          recommendation_list: body.recommendations,
          risk_analysis: {
            dyslexia_risk: body.dyslexia_risk,
            dyslexia_rationale: body.dylexia_rationale,
            level_of_need: body.level_of_need,
            level_of_need_description: body.level_of_need_description
          },
          status: 'COMPLETED',
          workflow_metadata: {
            ...body.workflow_metadata,
            completed_at: new Date().toISOString(),
            workflow_processed: true
          },
          updated_at: new Date().toISOString()
        })
        .eq("id", ai_report_id);
      if (updateError) {
        return NextResponse.json(
          {
            error: "Failed to update AI Report record",
            details: updateError.message
          },
          { status: 500 }
        );
      }

      updateResult = true;

    } catch (dbError) {
      return NextResponse.json(
        {
          error: "Database operation failed",
          details: dbError instanceof Error ? dbError.message : "Unknown error"
        },
        { status: 500 }
      );
    }

    // Return success response
    return NextResponse.json({
      success: true,
      message: "Workflow result processed and AI report updated successfully",
      receivedAt: new Date().toISOString(),
      aiReportId: ai_report_id,
      recordUpdated: updateResult,
      summary: {
        dyslexiaRisk: body.dyslexia_risk,
        levelOfNeed: body.level_of_need,
        totalRecommendations: {
          inTheClassroom: body.recommendations?.inTheClassroom?.length || 0,
          extraSupport: body.recommendations?.extraSupport?.length || 0,
          atHome: body.recommendations?.atHome?.length || 0
        }
      }
    });

  } catch (error) {
    return NextResponse.json(
      {
        error: "Failed to process workflow result",
        details: error instanceof Error ? error.message : "Unknown error"
      },
      { status: 500 }
    );
  }
} 