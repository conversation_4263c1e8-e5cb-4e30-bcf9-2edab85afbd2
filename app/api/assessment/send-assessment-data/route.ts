import { createClient } from "@supabase/supabase-js";
import { NextRequest, NextResponse } from "next/server";

import { supabaseConfig } from "@/data/constants";
import { AssessmentResponse } from "@/stores/use-assessment-responses";

interface Student {
  id: string;
  code: string;
  age: number;
  school_id: number;
}

export async function POST(request: NextRequest) {
  try {
    const {
      assessment,
      student,
      completedAt,
      assessmentResultId,
    }: {
      assessment: AssessmentResponse;
      student: Student;
      completedAt?: string;
      assessmentResultId?: string;
    } = await request.json();

    if (!assessment || !student) {
      return NextResponse.json(
        { error: "Assessment and student data are required" },
        { status: 400 }
      );
    }

    const {
      name,
      assessmentType,
      assessmentAudience,
      ...cleanedAssessmentData
    } = assessment;

    const payload = {
      user_id: student.id,
      student_code: student.code,
      age: student.age,
      env: process.env.NODE_ENV === "production" ? "prod" : "dev",
      school_id: student.school_id,
      date_taken: completedAt || new Date().toISOString(),
      assessment_id: assessment.id,
      assessment_name: name,
      assessment_type: assessmentType,
      assessment_audience: assessmentAudience,
      assessment_data_id: assessmentResultId,
      vercel_branch_url: request.headers.get("host") || "",
      assessment_data: cleanedAssessmentData,
      studentAge: student.age,
      studentCode: student.code,
      userID: student.id,
    };

    try {
      const response = await fetch(process.env.SCHOOL_DATA_ANALYSIS_ENDPOINT!, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "x-api-key": process.env.SCHOOL_API_KEY_DATA_EXPORT_API!,
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        console.error(
          "Failed to send to analysis endpoint:",
          response.statusText
        );
        return NextResponse.json(
          {
            success: false,
            message: "Failed to send to analysis endpoint",
            error: response.statusText,
          },
          { status: 500 }
        );
      }

      return NextResponse.json({
        success: true,
        message: "Assessment data sent successfully",
        data: payload,
      });
    } catch (error) {
      return NextResponse.json(
        {
          success: false,
          message: "Error sending to analysis endpoint",
          error: error instanceof Error ? error.message : "Unknown error",
        },
        { status: 500 }
      );
    }
  } catch (error) {
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
