import { createClient } from "@supabase/supabase-js";
import { NextRequest, NextResponse } from "next/server";

import { supabaseConfig } from "@/data/constants";

export async function POST(request: NextRequest) {
  const supabase = createClient(
    supabaseConfig.supabaseUrl,
    supabaseConfig.supabaseKey
  );

  try {
    const bodyBuffer = await request.arrayBuffer();
    const bodyText = new TextDecoder().decode(bodyBuffer);
    const { assessment_id, student_id, student_code, assessment_response } =
      JSON.parse(bodyText);

    if (!assessment_id || !student_id || !assessment_response?.scales) {
      return NextResponse.json(
        { message: "Missing required fields" },
        { status: 400 }
      );
    }

    const totalTimeMs = assessment_response.totalTimeMs || 0;
    const isComplete = assessment_response.isComplete || false;

    const { error: assessmentError } = await supabase
      .from("nova_assessments")
      .upsert(
        {
          student_id,
          student_code,
          assessment_id,
          assessment_name: assessment_response.name,
          assessment_type: assessment_response.assessmentType,
          assessment_audience: assessment_response.assessmentAudience,
          total_time_ms: totalTimeMs,
          is_complete: isComplete,
          completed_at: assessment_response.dateCompleted ?? null,
        },
        { onConflict: "student_id, assessment_id" }
      );

    if (assessmentError) {
      return NextResponse.json(
        { message: "assessment insert error", error: assessmentError },
        { status: 500 }
      );
    }

    const { data: assessmentRow, error: fetchAssessmentError } = await supabase
      .from("nova_assessments")
      .select("id")
      .eq("student_id", student_id)
      .eq("assessment_id", assessment_id)
      .maybeSingle();

    if (fetchAssessmentError || !assessmentRow) {
      return NextResponse.json(
        { message: "assessment lookup error", error: fetchAssessmentError },
        { status: 500 }
      );
    }

    const assessmentResultId = assessmentRow.id;

    const scaleRecords = assessment_response.scales.map((scale) => ({
      assessment_id: assessmentResultId,
      student_code,
      scale_id: scale.id,
      scale_name: scale.name,
      total_time_ms: scale.totalTimeMs || 0,
      reason_for_exit: scale.reasonForExit || null,
    }));

    const { error: scaleBatchError } = await supabase
      .from("nova_scales")
      .upsert(scaleRecords, { onConflict: "assessment_id, scale_id" });

    if (scaleBatchError) {
      return NextResponse.json(
        { message: "scale batch insert error", error: scaleBatchError },
        { status: 500 }
      );
    }

    const { data: insertedScales, error: lookupScalesError } = await supabase
      .from("nova_scales")
      .select("id, scale_id")
      .eq("assessment_id", assessmentResultId);

    if (lookupScalesError || !insertedScales) {
      return NextResponse.json(
        { message: "scale lookup error", error: lookupScalesError },
        { status: 500 }
      );
    }

    const scaleIdMap = new Map(insertedScales.map((s) => [s.scale_id, s.id]));

    const itemRecords = assessment_response.scales.flatMap((scale) => {
      const scaleResultId = scaleIdMap.get(scale.id);
      if (!scaleResultId) return [];

      return (scale.items || []).map((item) => ({
        scale_id: scaleResultId,
        student_code,
        item_id: item.id,
        name: item.name,
        seen: item.seen,
        score: item.score,
        answer: item.answer,
        skipped: item.skipped,
        practice: item.practice,
        time_taken_ms: item.timeTakenMs,
      }));
    });

    if (itemRecords.length > 0) {
      const { error: itemBatchError } = await supabase
        .from("nova_scale_items")
        .upsert(itemRecords, { onConflict: "scale_id, item_id" });

      if (itemBatchError) {
        return NextResponse.json(
          { message: "item batch insert error", error: itemBatchError },
          { status: 500 }
        );
      }
    }

    return NextResponse.json(
      {
        message: "success",
        assessmentResultId: assessmentResultId,
      },
      { status: 200 }
    );
  } catch (error) {
    return NextResponse.json({ message: "Error", error }, { status: 500 });
  }
}
