import { createClient } from "@supabase/supabase-js";
import { NextRequest, NextResponse } from "next/server";

import { supabaseConfig } from "@/data/constants";

export async function POST(request: NextRequest) {
  const supabase = createClient(
    supabaseConfig.supabaseUrl,
    supabaseConfig.supabaseKey
  );

  try {
    const bodyBuffer = await request.arrayBuffer();
    const bodyText = new TextDecoder().decode(bodyBuffer);
    const { students } = JSON.parse(bodyText);

    const { data, error } = await supabase
      .from("student")
      .upsert(students, {
        onConflict: "first_names, surname, date_of_birth, school_id",
        ignoreDuplicates: true,
      })
      .select("student_id, student_code, school_id");

    if (error) {
      return NextResponse.json({ message: "error", error }, { status: 500 });
    }

    return NextResponse.json({ message: "success", data }, { status: 200 });
  } catch (error) {
    console.error(error);
    return NextResponse.json(
      { message: "error", error: error },
      { status: 500 }
    );
  }
}
