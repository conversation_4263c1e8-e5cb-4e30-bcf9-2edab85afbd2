import { createClient } from "@supabase/supabase-js";
import { NextResponse } from "next/server";

import { supabaseConfig } from "@/data/constants";

const supabase = createClient(
  supabaseConfig.supabaseUrl,
  supabaseConfig.supabaseKey
);

export async function POST(request: Request) {
  const { email, loginCode } = await request.json();

  const { data, error } = await supabase
    .from("admins")
    .select("*")
    .eq("email", email)
    .single();

  if (error || !data) {
    return NextResponse.json(
      { message: "Please enter a valid email address" },
      { status: 400 }
    );
  }

  if (data.verification_code !== loginCode) {
    return NextResponse.json(
      { message: "Please enter a valid code" },
      { status: 400 }
    );
  }

  return NextResponse.json({ success: true, userId: data.user_id });
}
