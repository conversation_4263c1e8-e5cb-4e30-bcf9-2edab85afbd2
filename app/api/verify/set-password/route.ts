import { createClient } from "@supabase/supabase-js";
import { NextResponse } from "next/server";

import { supabaseConfig } from "@/data/constants";

const supabase = createClient(
  supabaseConfig.supabaseUrl,
  supabaseConfig.supabaseKey
);

export async function POST(request: Request) {
  const { email, password } = await request.json();

  const { data, error } = await supabase
    .from("admins")
    .select("user_id")
    .eq("email", email)
    .single();

  if (error || !data) {
    return NextResponse.json(
      { message: "User not found or invalid email." },
      { status: 400 }
    );
  }

  const userId = data.user_id;

  const { error: updateError, data: userData } =
    await supabase.auth.admin.updateUserById(userId, {
      password,
    });

  if (updateError) {
    return NextResponse.json({ message: updateError.message }, { status: 400 });
  }

  return NextResponse.json({ success: true, ...userData });
}
