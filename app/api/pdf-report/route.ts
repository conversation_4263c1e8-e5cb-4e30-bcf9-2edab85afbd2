import { draftMode } from "next/headers";
import { NextResponse } from "next/server";

import { DATO_PAGING_LIMIT } from "@/constants/constants";
import {
  AppendixPdfResourcesDocument,
  AreasOfNeedDocument,
  AreasOfNeedFallbackDocument,
  ContentsPdfResourcesDocument,
  CoverPdfResourcesDocument,
  RecommendationCopyDocument,
  RecommendationTypeIconsDocument,
  ReportRecommendationsCountDocument,
  ReportRecommendationsDocument,
  ReportRecommendationsQuery,
  ResultsPdfResourcesDocument,
  RiskLevelsDocument,
  SupportPdfResourcesDocument,
} from "@/types/graphql/graphql";
import queryDatoCMS from "@/utils/cms/query-dato-cms";

export async function GET() {
  try {
    const { isEnabled: isDraft } = draftMode();

    const recommendationsCount = await queryDatoCMS(
      ReportRecommendationsCountDocument,
      {},
      false
    );
    const numRecommendations =
      recommendationsCount._allRecommendationsMeta.count;

    let allRecommendations: ReportRecommendationsQuery["allRecommendations"] =
      [];

    for (let skip = 0; skip < numRecommendations; skip += DATO_PAGING_LIMIT) {
      const batch = await queryDatoCMS(
        ReportRecommendationsDocument,
        { first: DATO_PAGING_LIMIT, skip },
        false
      );
      allRecommendations = [...allRecommendations, ...batch.allRecommendations];
    }

    const [
      coverResources,
      supportResources,
      resultsResources,
      appendixResources,
      contentsResources,
      riskLevelResources,
      recommendationCopy,
      areasOfNeedFallback,
      areasOfNeed,
    ] = await Promise.all([
      queryDatoCMS(CoverPdfResourcesDocument, undefined, isDraft),
      queryDatoCMS(SupportPdfResourcesDocument, undefined, isDraft),
      queryDatoCMS(ResultsPdfResourcesDocument, undefined, isDraft),
      queryDatoCMS(AppendixPdfResourcesDocument, undefined, isDraft),
      queryDatoCMS(ContentsPdfResourcesDocument, undefined, isDraft),
      queryDatoCMS(RiskLevelsDocument, undefined, isDraft),
      queryDatoCMS(RecommendationCopyDocument, undefined, isDraft),
      queryDatoCMS(AreasOfNeedFallbackDocument, undefined, isDraft),
      queryDatoCMS(AreasOfNeedDocument, undefined, isDraft),
    ]);

    const recommendationIcons = await queryDatoCMS(
      RecommendationTypeIconsDocument,
      {},
      isDraft
    );

    const pdfResources = [
      ...coverResources.allPdfResourceSets[0].resources,
      ...supportResources.allPdfResourceSets[0].resources,
      ...resultsResources.allPdfResourceSets[0].resources,
      ...appendixResources.allPdfResourceSets[0].resources,
      ...contentsResources.allPdfResourceSets[0].resources,
    ];

    return NextResponse.json({
      pdfResources,
      riskLevelResources,
      recommendationCopy,
      allRecommendations,
      recommendationIcons,
      areasOfNeed,
      areasOfNeedFallback,
    });
  } catch (error) {
    console.error("Error fetching resources:", error);
    return NextResponse.json(
      { error: "Failed to fetch resources" },
      { status: 500 }
    );
  }
}
