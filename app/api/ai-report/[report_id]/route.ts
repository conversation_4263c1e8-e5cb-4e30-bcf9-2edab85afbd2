import { AIReportRequest, AIReportService } from "@/services/ai-report.service";
import { NextRequest, NextResponse } from "next/server";

export async function POST(
  request: NextRequest,
  { params }: { params: { report_id: string } }
) {
  const { report_id } = params;

  // Create AI report service instance
  const aiReportService = new AIReportService();

  // Extract auth token if provided (currently disabled for testing)
  // const authHeader = request.headers.get("authorization");
  // const authToken = authHeader?.startsWith("Bearer ") ? authHeader.split(" ")[1] : undefined;

  // Create request object
  const aiReportRequest: AIReportRequest = {
    reportId: report_id,
    // authToken
  };

  // Generate the AI report
  const result = await aiReportService.generateReport(aiReportRequest);

  // Return appropriate response based on success/failure
  if (result.success) {
    return NextResponse.json({
      success: result.success,
      message: result.message,
      webhookResponse: result.webhookResponse,
      recommendations: result.recommendations
    });
  } else {
    // Determine status code based on error message
    let statusCode = 500;
    if (result.error?.includes("required")) statusCode = 400;
    if (result.error?.includes("not found")) statusCode = 404;
    if (result.error?.includes("authentication") || result.error?.includes("authorization")) statusCode = 401;

    return NextResponse.json(
      {
        success: result.success,
        error: result.error,
        message: result.message,
        details: result.details
      },
      { status: statusCode }
    );
  }
} 