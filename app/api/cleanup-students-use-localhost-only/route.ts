import { createClient, User } from "@supabase/supabase-js";

import { supabaseConfig } from "@/data/constants";

export async function POST() {
  const supabase = createClient(
    supabaseConfig.supabaseUrl,
    supabaseConfig.supabaseKey
  );

  const startTime = new Date("2099-01-15T12:00:00Z");
  const endTime = new Date();

  try {
    let allUsers: User[] = [];
    let currentPage = 1;

    while (true) {
      const { data, error } = await supabase.auth.admin.listUsers({
        page: currentPage,
      });

      if (error) {
        console.error(`Error fetching users on page ${currentPage}:`, error);
        return new Response(`Error fetching users: ${error.message}`, {
          status: 500,
        });
      }

      if (data?.users) {
        const users: User[] = data.users as User[];
        allUsers = [...allUsers, ...users];
      }

      if (!data?.nextPage || currentPage >= data.lastPage) {
        break;
      }

      currentPage++;
    }

    const usersToDelete = allUsers.filter((user) => {
      const createdAt = new Date(user.created_at);
      return createdAt >= startTime && createdAt <= endTime;
    });

    console.log(`Users to delete: ${usersToDelete.length}`);

    for (const user of usersToDelete) {
      console.log(`Deleting user: ${user.id} (${user.email})`);

      const { error: studentDeleteError } = await supabase
        .from("student")
        .delete()
        .eq("student_id", user.user_metadata.student_id);

      if (studentDeleteError) {
        console.error(
          `Error deleting related student data for user ${user.id}:`,
          studentDeleteError
        );
      }

      const { error: studentDataDeleteError } = await supabase
        .from("studentData")
        .delete()
        .eq("user_id", user.id);

      if (studentDataDeleteError) {
        console.error(
          `Error deleting related studentData for user ${user.id}:`,
          studentDataDeleteError
        );
      }

      const { error: deleteUserError } = await supabase.auth.admin.deleteUser(
        user.id
      );

      if (deleteUserError) {
        console.error(`Error deleting user ${user.id}:`, deleteUserError);
      }
    }

    console.log(`Deletion process complete.`);
    return new Response(`Successfully deleted ${usersToDelete.length} users.`, {
      status: 200,
    });
  } catch (error) {
    console.error("Error during cleanup:", error);
    return new Response(`Error during cleanup: ${error}`, {
      status: 500,
    });
  }
}
