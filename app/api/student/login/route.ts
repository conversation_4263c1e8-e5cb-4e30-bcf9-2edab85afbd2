import { createClient } from "@supabase/supabase-js";
import { cookies } from "next/headers";
import { NextResponse } from "next/server";

import { supabaseConfig } from "@/data/constants";
import { createToken } from "@/lib/jwt";

const supabase = createClient(
  supabaseConfig.supabaseUrl,
  supabaseConfig.supabaseKey
);

export async function POST(req: Request) {
  const { studentCode } = await req.json();

  const { data, error } = await supabase
    .from("student")
    .select("student_id, first_names, surname, date_of_birth, school_id")
    .eq("student_code", studentCode)
    .single();

  if (error || !data) {
    return NextResponse.json(
      { error: "Invalid student code" },
      { status: 401 }
    );
  }

  const token = await createToken({
    studentId: data.student_id,
    first_names: data.first_names,
    surname: data.surname,
    date_of_birth: data.date_of_birth,
    code: studentCode,
    school_id: data.school_id,
  });

  cookies().set("student_session", token, {
    httpOnly: true,
    secure: process.env.NODE_ENV === "production",
    sameSite: "lax",
    path: "/",
    maxAge: 3600,
  });

  return NextResponse.json({
    success: true,
    student: {
      id: data.student_id,
      first_names: data.first_names,
      surname: data.surname,
      date_of_birth: data.date_of_birth,
      code: studentCode,
    },
  });
}
