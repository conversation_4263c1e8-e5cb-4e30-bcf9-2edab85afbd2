import { createClient } from "@supabase/supabase-js";
import { NextRequest, NextResponse } from "next/server";

import { supabaseConfig } from "@/data/constants";
import { CognitiveProfile } from "@/types/analysis";

interface AssessmentMeta {
  assessmentId: string;
  assessmentName: string;
  dateTaken: string;
  schoolId: number;
  userId: string;
}

interface Analysis {
  dyslexiaRisk: number;
  dyslexiaScore: number;
  levelOfNeedRisk: number;
  levelOfNeedScore: number;
}

interface SaveReportRequest {
  studentAge: number;
  studentCode: string;
  assessmentMeta: AssessmentMeta;
  resultsData: CognitiveProfile;
  analysis: Analysis;
  assessmentType: string;
  assessmentAudience: string;
  assessmentDataId: string;
}

export async function POST(request: NextRequest) {
  try {
    const bearerToken = request.headers
      .get("authorization")
      ?.split("Bearer ")[1];
    const data: SaveReportRequest = await request.json();

    const supabase = createClient(
      supabaseConfig.supabaseUrl,
      supabaseConfig.supabaseKey
    );

    const requiredFields = [
      "studentAge",
      "studentCode",
      "assessmentMeta",
      "resultsData",
      "analysis",
      "assessmentType",
      "assessmentAudience",
      "assessmentDataId",
    ];

    const missingFields = requiredFields.filter((field) => !data[field]);

    if (missingFields.length > 0) {
      return NextResponse.json(
        { message: "Missing required fields", missingFields },
        { status: 400 }
      );
    }

    if (typeof data.assessmentMeta.schoolId !== "number") {
      return NextResponse.json(
        { message: "Invalid school ID" },
        { status: 400 }
      );
    }

    if (!bearerToken || bearerToken !== process.env.API_TOKEN) {
      return NextResponse.json(
        { message: "Please provide a valid bearer token" },
        { status: 401 }
      );
    }

    const {
      studentCode,
      assessmentMeta,
      resultsData,
      analysis,
      assessmentType,
      assessmentAudience,
      assessmentDataId,
    } = data;

    const { error: supabaseError } = await supabase
      .from("studentReports")
      .upsert(
        {
          student_code: studentCode,
          assessment_id: assessmentMeta.assessmentId,
          assessment_data_id: assessmentDataId,
          assessment_meta: assessmentMeta,
          result_data: resultsData,
          analysis: analysis,
          assessment_type: assessmentType,
          assessment_audience: assessmentAudience,
          school_id: assessmentMeta.schoolId,
        },
        {
          onConflict: "student_code,assessment_id",
          ignoreDuplicates: false,
        }
      );

    if (supabaseError) {
      console.error("Supabase upsert error:", {
        error: supabaseError,
        studentCode,
        assessmentId: assessmentMeta.assessmentId,
      });
      return NextResponse.json(
        {
          message: "Failed to save report",
          error: supabaseError.message,
          code: supabaseError.code,
        },
        { status: 500 }
      );
    }

    console.log("Successfully saved report:", {
      studentCode,
      assessmentId: assessmentMeta.assessmentId,
      assessmentType,
    });

    return NextResponse.json({
      message: "Successfully saved to reports",
      studentCode,
      assessmentId: assessmentMeta.assessmentId,
    });
  } catch (error) {
    console.error("Unexpected error in save-report:", {
      error: error instanceof Error ? error.message : error,
      stack: error instanceof Error ? error.stack : undefined,
    });
    return NextResponse.json(
      {
        message: "Internal server error",
        error: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
