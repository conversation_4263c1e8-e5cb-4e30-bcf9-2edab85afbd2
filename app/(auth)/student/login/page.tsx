"use client";

import { OTPInput } from "input-otp";
import { useRouter } from "next/navigation";
import { useContext, useEffect, useRef, useState } from "react";

import {
  Button,
  KeyboardInput,
  KeyboardInputGroup,
  KeyboardInputSlot,
  NightSkyBg,
} from "@/components/ui";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>cter } from "@/components/ui/feedback-character/feedback-character";
import { StudentContext } from "@/context/student-context";
import { getNovaAssessment } from "@/lib/get-nova-assessment-data";
import { useAssessmentResponsesStore } from "@/stores/use-assessment-responses";

import { StudentLoginHeader } from "./components/student-login-header";

export default function LoginPage() {
  const [studentCode, setStudentCode] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [hasError, setHasError] = useState(false);

  const { refreshSession } = useContext(StudentContext);
  const router = useRouter();
  const otpRef = useRef<React.ElementRef<typeof OTPInput>>(null);

  useEffect(() => {
    if (studentCode.length === 0) {
      otpRef.current?.focus();
    }
  }, [studentCode]);

  const handleLogin = async () => {
    setIsLoading(true);
    setHasError(false);

    const loginRes = await fetch("/api/student/login", {
      method: "POST",
      body: JSON.stringify({ studentCode }),
      headers: { "Content-Type": "application/json" },
    });

    if (!loginRes.ok) {
      setIsLoading(false);
      setHasError(true);
      return;
    }

    await refreshSession();

    const novaAssessment = await getNovaAssessment();
    const assessmentId = novaAssessment?.novaAssessment?.id;
    const assessmentName = novaAssessment?.novaAssessment?.displayName;

    if (assessmentId && assessmentName) {
      const fetchRes = await fetch(
        `/api/assessment/fetch-assessment-responses?student_code=${studentCode}&assessment_id=${assessmentId}`
      );

      if (fetchRes.ok) {
        const assessment = await fetchRes.json();
        if (Object.keys(assessment).length === 0) {
          useAssessmentResponsesStore
            .getState()
            .initAssessment({ id: assessmentId, name: assessmentName });
        } else {
          useAssessmentResponsesStore
            .getState()
            .loadAssessmentFromDB(assessment);
        }
      } else if (fetchRes.status === 404) {
        useAssessmentResponsesStore
          .getState()
          .initAssessment({ id: assessmentId, name: assessmentName });
      }
    }

    router.push("/dashboard");
  };

  const handleInputChange = async (value: string) => {
    setHasError(false);
    setStudentCode(value);
  };

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === "Enter" && studentCode.length === 8 && !isLoading) {
        otpRef.current?.blur();
        handleLogin();
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, [studentCode, isLoading]);

  return (
    <>
      <StudentLoginHeader />
      <div className="flex flex-col items-center justify-center w-full h-screen">
        <div>
          <h2 className="text-base font-semibold mb-6">Enter your code</h2>
          <KeyboardInput
            ref={otpRef}
            maxLength={8}
            value={studentCode}
            hasError={hasError}
            onChange={handleInputChange}
          >
            <KeyboardInputGroup spellCheck={false}>
              <KeyboardInputSlot index={0} />
              <KeyboardInputSlot index={1} />
              <KeyboardInputSlot index={2} />
              <KeyboardInputSlot index={3} />
              <KeyboardInputSlot index={4} />
              <KeyboardInputSlot index={5} />
              <KeyboardInputSlot index={6} />
              <KeyboardInputSlot index={7} />
            </KeyboardInputGroup>
          </KeyboardInput>
        </div>
        <Button
          className="mt-10"
          loading={isLoading}
          onClick={handleLogin}
          disabled={studentCode.length < 8}
        >
          Continue
        </Button>
      </div>
      <FeedbackCharacter
        message="Code not found. Try again!"
        variant="error"
        isActive={hasError && !isLoading}
      />
      <NightSkyBg />
    </>
  );
}
