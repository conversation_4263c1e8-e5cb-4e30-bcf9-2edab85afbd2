{"extends": ["next/core-web-vitals", "next/typescript", "plugin:storybook/recommended", "plugin:prettier/recommended"], "plugins": ["simple-import-sort", "import"], "rules": {"simple-import-sort/imports": "error", "simple-import-sort/exports": "error", "import/first": "error", "import/newline-after-import": "error", "import/no-duplicates": "error", "@typescript-eslint/no-explicit-any": "off", "no-unused-vars": "off", "@typescript-eslint/no-unused-vars": "off"}}