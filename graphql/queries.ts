import * as Partial from "./partials";

export const GET_ASSESSMENT_ANSWERS = ({
  assessmentID,
}: {
  assessmentID: string | number;
}) => `
query GetAllAnswersForAssessment {
  allAssessments(filter: {id: {eq: "${assessmentID}"}}) {
    assessmentScales {
      id
      scaleTitle
      scaleItemGroups {
        scaleItemGroupTitle
        id 
        scaleItemGroupItems {
          id
          scaleItemIsMetadata
          scaleItemMetadataFieldName
          scaleItemResponseType
          scaleItemIsMarked
          scaleItemAnswer 
        }
      }
    }
  }
}`;

export const GET_ALL_ASSESSMENTS_SUMMARY = () => `
query GetAllAssessmentsSummary {
    allAssessments {
      ${Partial.assessmentPartial}
    }
  }
`;

export const GET_SCALE_ITEM_ANSWER = (id: number | string) => `
query GetScaleItemAnswer {
  allScaleItems(filter: {id: {eq: "${id}"}}) {
    id
    scaleItemAnswer
  }
}
`;

export const GET_SCALE_BY_ID = (id: number | string) => `
query GetScaleById {
  scale(filter: {id: {eq: "${id}"}}) {
   ${Partial.scalePartial}
  }
}
`;

export const GET_ASSESSMENT_SCALES_BY_ASSESSMENT_ID = (id: number | string) => `
query GetAssessmentById {
  allAssessments(filter: {id: {eq: "${id}"}}) {
    id
    assessmentImage {
      id
      alt
      url
    }
    assessmentTitle
    assessmentDescription
    showOnboardingVideo
    onboardingVideoFile {
      url
      video {
        mp4Url
        streamingUrl
        thumbnailUrl
        width
        height
      }
    }
    videoPosterImage {
      id
      url
      title
    }
    assessmentScales {
      id
      scaleTitle
      scaleDisplayName
      scaleStart {
        id
        url
      }
      scaleComplete {
        url
        id
        alt
      }
    }
  }
}`;

export const GET_ASSESSMENT_BY_ID = (id: number | string) => `
query GetAssessmentById {
  allAssessments(filter: {id: {eq: "${id}"}}) {
    id
    assessmentImage {
      id
      alt
      url
    }
    assessmentTitle
    assessmentDescription
    showOnboardingVideo
    onboardingVideoFile {
      url
      video {
        mp4Url
        streamingUrl
        thumbnailUrl
        width
        height
      }
    }
    videoPosterImage {
      id
      url
      title
    }
    assessmentScales {
      ${Partial.scalePartial}
    }
  }
}`;
