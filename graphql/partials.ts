export const scaleItemPartial = `
  id
  scaleItemIsMarked
  scaleItemAnswer
  maxAnswersPickable
  scaleItemInstructionText
  scaleItemIsMetadata
  scaleItemMetadataFieldName
  scaleItemInstructionAudio {
    id
    title
    url
  }
  scaleItemStimulusType
  scaleItemVoiceTarget
  scaleItemCheckPhonemes
  scaleItemVoicePhonetics
  scaleItemTextStimulus
  scaleItemMediaStimulus {
    id
    url
    title
  }
  scaleItemNumberOfReplays
  scaleItemCorrectText
  scaleItemResponseType
  scaleItemResponseMedia {
    id
    url
    title
    responsiveImage {
      alt
      aspectRatio
      base64
      bgColor
      height
      sizes
      src
      srcSet
      title
      webpSrcSet
      width
    }
  }
  scaleItemResponseValues
  scaleItemCorrectAudio {
    id
    url
  }
  scaleItemIncorrectState {
    id
    incorrectAnswerAction
    incorrectAnswerAudio {
      id
      url
    }
    incorrectAnswerText
  }
  scaleItemNudgeAudio {
    id
    url
  }
  scaleItemNudgeText
  scaleItemNudgeTime          
  scaleItemTimeoutAction
  scaleItemTimeoutAudio {
    id
    url
  }
  scaleItemTimeoutText
  scaleItemTimeoutTime
  scaleItemTitle
`;

export const assessmentPartial = `
  id 
  assessmentImage {
    id
    url
  }
  assessmentTitle 
  assessmentDescription
`;
export const scaleItemGroupPartial = `
  id
  scaleItemGroupMaxAge
  scaleItemGroupTimeLimit
  scaleItemGroupMaxIncorrect
  scaleItemGroupAboveAverage
  scaleItemGroupIsSample
  scaleItemGroupMaxAge
  scaleItemGroupMaxIncorrect
  scaleItemGroupShowTimer
  scaleItemGroupTitle
  scaleItemGroupOnboardingContent {
    id
    onboardingText
    onboardingAudio {
      id
      url
    }
    onboardingImage {
      id
      url
      title
    }
    onboardingImagev2 {
      id
      url
      title
    }
    onboardingVideo {
      url
      video {
        mp4Url
        streamingUrl
        thumbnailUrl
        width
        height
      }
    }
  }
  scaleItemGroupItems {
    ${scaleItemPartial}
  }
`;
export const scalePartial = `
  id
  scaleTitle
  scaleSpellCheckOn
  scaleStart {
    id
    url
  }
  scaleComplete {
    url
    id
    alt
  }
  scaleItemGroups {
    ${scaleItemGroupPartial}
  }
`;
