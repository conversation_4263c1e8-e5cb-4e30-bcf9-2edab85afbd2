query ReportRecommendationsCount {
  _allRecommendationsMeta {
    count
  }
}
query ReportRecommendations($first: IntType!, $skip: IntType!) {
  allRecommendations(first: $first, skip: $skip) {
    id
    title
    description(markdown: true)
    category
    recommendationType
    testArea
    scoringType
    relevantAges
    minimumScore
    maximumScore
    primaryLink {
      title
      url
      description(markdown: true)
    }
    secondaryContent {
      id
      title
      url
      description(markdown: true)
    }
    recommendationType
    duration
  }
}

query RecommendationTypeIcons {
  general {
    recommendationTypeIcon {
      title
      url
    }
  }
}

query RecommendationCopy {
  general {
    highPriorityCopy(markdown: true)
    highPriorityFallbackCopy(markdown: true)
    extraSupportCopy(markdown: true)
    classroomCopy(markdown: true)
    atHomeCopy(markdown: true)
    recommendationsIntroductionCopy(markdown: true)
  }
}
