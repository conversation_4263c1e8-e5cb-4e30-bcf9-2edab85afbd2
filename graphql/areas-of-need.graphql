query AreasOfNeed {
  allAreaOfNeeds(first: "500") {
    id
    title
    maximumScore
    minimumScore
    testArea
    linkUrl
    linkTitle
    flagColour
    descriptionTeacher(markdown: true)
    descriptionParent(markdown: true)
    description(markdown: true)
    icon {
      url
    }
  }
}

query AreasOfNeedFallback {
  general {
    aonFallbackTitle
    aonFallbackDescription(markdown: true)
    aonFallbackIcon {
      url
    }
  }
}
