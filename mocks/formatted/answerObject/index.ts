export const mockAnswerObject = {
  "172605956": {
    _unsafe__answer: "1,3",
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      values: "$2a$04$oAMswcDEJSWk5psz6LjamONtO42HzCN2KtGchf0sAkTlzPxzK8nBi",
    },
  },
  "172798627": {
    _unsafe__answer: "5",
    responseType: "number series",
    answer: {
      markAs: "series",
      values: "$2a$04$MlLhdnUTVMBCMvdctcq6CuqxvyU2lYF5Uvcxn7UMCwwfIfDY4nYkC",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "173698657": {
    _unsafe__answer: "True",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$Y1y0JBrC47qk4fQby.0zsuf9qCp05BXRo4yZQYQacttEgXA9D6pLe",
    },
  },
  "173902291": {
    _unsafe__answer: "(0,2),(0,3) - (0,6),(0,7)",
    responseType: "pair cancellation",
    answer: {
      markAs: "score_pair_cancellation",
      values: [
        "$2a$04$s8Hvy.1F7yTAc1.VgfFHb.deK.r2Q3fSQ8Fpamv3awUk1T9djr1ii",
        "$2a$04$882mLb9QGWMQbAHKU7t81ug4UuvTtCGxhG06zeD4FJSAeB7lazd2a",
      ],
      description: {
        code: 7,
        description:
          "Receives answer value as arrays of coordinates [ ['1,2', '1,3'], ['4,4', '4,5'] ]",
      },
    },
  },
  "177155911": {
    _unsafe__answer: "2",
    responseType: "number series",
    answer: {
      markAs: "series",
      values: "$2a$04$YBaS/QI/7827OZHkoXlpIu7hWZjXx2bQsSBSTOyIaImIC/d6xAMMu",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177155932": {
    _unsafe__answer: "3",
    responseType: "number series",
    answer: {
      markAs: "series",
      values: "$2a$04$xwwva1Y5OOyoqLm/tIeeCOLUK7o0n7RjITLMjobDfUQwGJOq3Tguy",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177155980": {
    _unsafe__answer: "9",
    responseType: "number series",
    answer: {
      markAs: "series",
      values: "$2a$04$4FzRPIhYfuZJNJEhv6oaR.p25ftQi55eEPjKpf5QNeMfSMMQj2jg2",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177155988": {
    _unsafe__answer: "4",
    responseType: "number series",
    answer: {
      markAs: "series",
      values: "$2a$04$tSW8bE6vUowW/RnCxuu9ruYu1OHPPEoHDEM0BOCHWhxjBlpZjSC76",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177155993": {
    _unsafe__answer: "18",
    responseType: "number series",
    answer: {
      markAs: "series",
      values: "$2a$04$zMskPdKi9//veRxJbWwc1uKya5WBgOclEjfRbBI2MFkaYopfYpgt.",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177155995": {
    _unsafe__answer: "13",
    responseType: "number series",
    answer: {
      markAs: "series",
      values: "$2a$04$gMiuVwJoV1XKejFkDDfzX.9Cux4ETzpbFNcS90KSyNmIHZ2CMrL5O",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177155999": {
    _unsafe__answer: "19",
    responseType: "number series",
    answer: {
      markAs: "series",
      values: "$2a$04$.LdCFlhMeVrYo7qAvcmNhuvfeLOcMUAjMTcIBBQhn6fW0kz4puCzS",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177156004": {
    _unsafe__answer: "13",
    responseType: "number series",
    answer: {
      markAs: "series",
      values: "$2a$04$/qc6bG6ZfFMcuCULl3s7pehiAizZWpSu2.wvhgBsbhI.QDqEfs2fq",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177156013": {
    _unsafe__answer: "13",
    responseType: "number series",
    answer: {
      markAs: "series",
      values: "$2a$04$4nMFIN6c9cAJeV/.yYJArO0laZXoJzBZ95P1zqDPItaVGr6jTLOOG",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177156024": {
    _unsafe__answer: "21",
    responseType: "number series",
    answer: {
      markAs: "series",
      values: "$2a$04$Zem37054e3vM7itmX3uhveF3Rro12RACR7LQW5RkCn3nZWbuIalUy",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177156069": {
    _unsafe__answer: "1",
    responseType: "number series",
    answer: {
      markAs: "series",
      values: "$2a$04$6DIqkBdKK7wwNTkBOaAHfeUI.1E7n3yvkM/qoe0c1gxRsN9SuQTpu",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177158768": {
    _unsafe__answer: "6",
    responseType: "number series",
    answer: {
      markAs: "series",
      values: "$2a$04$kyEQ7QkKez4vdJeSvkT.YuC39Tc75U4GcWqmr2WShjBqnI7p.4bjO",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177158782": {
    _unsafe__answer: "20",
    responseType: "number series",
    answer: {
      markAs: "series",
      values: "$2a$04$eIiBbHwKqMCQI1pjEDFxcOcdhZg7CtVdJXKt1T.j56ons0AKi8Upm",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177158783": {
    _unsafe__answer: "4",
    responseType: "number series",
    answer: {
      markAs: "series",
      values: "$2a$04$eC7VKy4FOnmWbqLSeqE32OYlbdoHw8104SF7b/3UTicr1zxhGYxVy",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177158784": {
    _unsafe__answer: "8",
    responseType: "number series",
    answer: {
      markAs: "series",
      values: "$2a$04$fdLhINOp5kChu3552jn7xOg.9InoerTFkZGYXf64I8sg1IsBmTAke",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177158789": {
    _unsafe__answer: "60",
    responseType: "number series",
    answer: {
      markAs: "series",
      values: "$2a$04$BiXrCll1HEh54DGbwZTDlepHuSYU942P3ZayGcdOWcsKvdjrkt10C",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177158790": {
    _unsafe__answer: "2",
    responseType: "number series",
    answer: {
      markAs: "series",
      values: "$2a$04$JM4nkg0N0tnVX7.IALEV7eXT497Aqax1efvBGsQlS23N91bNakgBm",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177158791": {
    _unsafe__answer: "15",
    responseType: "number series",
    answer: {
      markAs: "series",
      values: "$2a$04$Kp3QRuLrYOtKkDk5HNsADuDL.HOmh29KxTxlnDIfRKLin/MhTikP.",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177158800": {
    _unsafe__answer: "14",
    responseType: "number series",
    answer: {
      markAs: "series",
      values: "$2a$04$S46r6Dst7lNIfj4K1jgozOVuUmJhlweIEgdSkZluJ/gekZN7eyQyK",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177158808": {
    _unsafe__answer: "13",
    responseType: "number series",
    answer: {
      markAs: "series",
      values: "$2a$04$X1HlJQU30fIm7bu2bd4eJOPUJ97u8CVTt45JN.dMgeyjTc9as9hQi",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177158815": {
    _unsafe__answer: "16",
    responseType: "number series",
    answer: {
      markAs: "series",
      values: "$2a$04$KXnaN7XAAluMxztIy5i2h.zsWtBaS3b5wJkCboza0NL7BhDczXHEm",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177158825": {
    _unsafe__answer: "36",
    responseType: "number series",
    answer: {
      markAs: "series",
      values: "$2a$04$33lQqwFHH7TJan1NgjIgMuNtjGaxl.rWeSWiwBnCrtC.28pDltEh6",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177158827": {
    _unsafe__answer: "1",
    responseType: "number series",
    answer: {
      markAs: "series",
      values: "$2a$04$NpvtmYBxOb02ukRiV7xnWuUVwfN8dyK3uvgb0oJ7/xYV8VoEAnFSW",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177158828": {
    _unsafe__answer: "1",
    responseType: "number series",
    answer: {
      markAs: "series",
      values: "$2a$04$k3r8DJtNxzFgzwZWx.s5tO5Hod8zPYAPF5LS.PWmZte/HmfK3tro2",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177158837": {
    _unsafe__answer: "45",
    responseType: "number series",
    answer: {
      markAs: "series",
      values: "$2a$04$J9GlTHT9rxi1e0MsJpRyh.uD/uvMsJUPbMf0GU7NBJ1hM9MaaWSqW",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177158840": {
    _unsafe__answer: "1",
    responseType: "number series",
    answer: {
      markAs: "series",
      values: "$2a$04$CWQMU7LbrBmLb1A3VaVDTePoll9x3ri9c4Dn59EmgEDnD0FndINuS",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177158845": {
    _unsafe__answer: "5",
    responseType: "number series",
    answer: {
      markAs: "series",
      values: "$2a$04$bieEcsx/pajj95CSOcsq6eMjbUgymdfxkrkEQkLKE8TkWT8.9j2Py",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177158858": {
    _unsafe__answer: "3",
    responseType: "number series",
    answer: {
      markAs: "series",
      values: "$2a$04$2MJUKDEIPgu9WKLf8NuiKuUR8xjA2nRN5H0GXJ/PrrPnOFMr0mC0u",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177158859": {
    _unsafe__answer: "14",
    responseType: "number series",
    answer: {
      markAs: "series",
      values: "$2a$04$kXyhRjWJ3qcsZxtvZQiyzONGkjVhT3gh8obfrtXtp8ZMUB.DREd7W",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177158862": {
    _unsafe__answer: "88",
    responseType: "number series",
    answer: {
      markAs: "series",
      values: "$2a$04$v4uGrt0QF.NcvdCXI9abCO3TCnLRkO5tcR1aMFU4XibEcxmG1FTU6",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177158867": {
    _unsafe__answer: "(22) or (35) or (25) or (26)",
    responseType: "number series",
    answer: {
      markAs: "series",
      values: [
        "$2a$04$c3fbA7nEaiGV3f8AgasLs.CdSOEGe8tNaDrD5aTRQEhKrz3601aFW",
        "$2a$04$8vKKaRMgCS//oQkoo3PFYuuxN9y8pgHzlowII/nw1IRx/lTl/BL3u",
        "$2a$04$yFWxQ3IIyZCdnrYrvYoes.lVZIK48cYXWSyvJC/T/dfwuvsEpX38.",
        "$2a$04$aA0zu0HDCS0sC2iNCzZaR.wm8AwvY8w03EwrUMeW09f.mlgpTwUla",
      ],
      guide: {
        code: 5,
        description:
          'Receives answer value as string of numbers concat togerther "21,86,42"',
      },
    },
  },
  "177158873": {
    _unsafe__answer: "4",
    responseType: "number series",
    answer: {
      markAs: "series",
      values: "$2a$04$B.fNEWkBvhkIeUOHuKeqRuc1j8Jkf7senKixfRqS2rHDtBL1DFwAi",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177158874": {
    _unsafe__answer: "12",
    responseType: "number series",
    answer: {
      markAs: "series",
      values: "$2a$04$TnY4PBjq91GFRt6Atlbsse7yOPNJ15FeSjbQSAALAnqdfR59wNNzC",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177158881": {
    _unsafe__answer: "(3) or (5) or (9)",
    responseType: "number series",
    answer: {
      markAs: "series",
      values: [
        "$2a$04$3fpgmvlwkVRLjaApe6Ux1ueGmKJsCZ6QTsV6QfcgjK8TJPVGoz3qq",
        "$2a$04$8KX2COpz8NfHz6Owp87u5eQEyMR5NLChNlE/iBmzM.7v3cvZ2xSoG",
        "$2a$04$u0Geo6ec95Y2Yreeh6AewuWpbBAq6WP06MkN50xORsKdPmJKFLH1G",
      ],
      guide: {
        code: 5,
        description:
          'Receives answer value as string of numbers concat togerther "21,86,42"',
      },
    },
  },
  "177158889": {
    _unsafe__answer: "(16,18)",
    responseType: "number series",
    answer: {
      markAs: "series",
      values: ["$2a$04$tYp.K4tyf.YZ4NVyOaznxumUAeEYhAak5Pdnr99XB2Cw5Lh7imTdO"],
      guide: {
        code: 5,
        description:
          'Receives answer value as string of numbers concat togerther "21,86,42"',
      },
    },
  },
  "177158897": {
    _unsafe__answer: "81",
    responseType: "number series",
    answer: {
      markAs: "series",
      values: "$2a$04$fGAa2DhmBUodfh9IgicEpOrN2CcPpqGstofGGgmjgstR4KOzxVRs.",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177158903": {
    _unsafe__answer: "20",
    responseType: "number series",
    answer: {
      markAs: "series",
      values: "$2a$04$kR08nYDzbMlIxVS8QmX8sueWxa6aJEW6a/.XaSdC/sqMYws7ZIXzu",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177158911": {
    _unsafe__answer: "(42) or (45)",
    responseType: "number series",
    answer: {
      markAs: "series",
      values: [
        "$2a$04$1XgvyD0sLTRdBg46WTNnquEbdom0Jhpk9mhsYB/ZJQz8zfHGoJqe.",
        "$2a$04$TU3qPq/D9CjFwIjfGm0Wp.J007urHXyjIzNWqE0zG85r1NFJ4WzhO",
      ],
      guide: {
        code: 5,
        description:
          'Receives answer value as string of numbers concat togerther "21,86,42"',
      },
    },
  },
  "177158913": {
    _unsafe__answer: "(30) or (26)",
    responseType: "number series",
    answer: {
      markAs: "series",
      values: [
        "$2a$04$AfMXXHa.aYP3aKsRv8ErJO0G.eBEA9OLvYmJiYJK1I59OtvR5wNem",
        "$2a$04$PAkJY9w73P.OcGPQzYJX7ueW45TkcoT58V8Konv/CCqwDMkU60Bg2",
      ],
      guide: {
        code: 5,
        description:
          'Receives answer value as string of numbers concat togerther "21,86,42"',
      },
    },
  },
  "177158914": {
    _unsafe__answer: "(8) or (11)",
    responseType: "number series",
    answer: {
      markAs: "series",
      values: [
        "$2a$04$p06Ve6y.PqK9WJMrvKu18OPVB9CI5Tx.K.kAKB6PP74Mm9f8lnv3.",
        "$2a$04$1OvwZQiddiZcOr3UzGAmyOXZ9FMXP0K3Buhafkne7OabFYyAqP6/K",
      ],
      guide: {
        code: 5,
        description:
          'Receives answer value as string of numbers concat togerther "21,86,42"',
      },
    },
  },
  "177158916": {
    _unsafe__answer: "2",
    responseType: "number series",
    answer: {
      markAs: "series",
      values: "$2a$04$a7iwkQPAF8XxUYer/qhvrO2oBxvhREVe2tqnzeGgiyY5tGwLKtB6a",
      guide: {
        code: 2,
        description:
          "Receives answer value as a string, there is only one correct answer",
      },
    },
  },
  "177158936": {
    _unsafe__answer: "(72,76) or (78,82)",
    responseType: "number series",
    answer: {
      markAs: "series",
      values: [
        "$2a$04$aOIzqf.XBp5Pq/1z2EfKP.QAF11qeR4phejdiOAIV00lkxtdheYPm",
        "$2a$04$365Qt81JkrDQXHhugkVkV.8EYxk0YvhNxXZmoW4erdTswGjgFZ/vC",
      ],
      guide: {
        code: 5,
        description:
          'Receives answer value as string of numbers concat togerther "21,86,42"',
      },
    },
  },
  "177158945": {
    _unsafe__answer: "(14) or (32) or (41)",
    responseType: "number series",
    answer: {
      markAs: "series",
      values: [
        "$2a$04$oCYX9m5q5pSRRucs0oZH4emh7.9fmwP1FlUxgJq1b6GcT0Kps8ak.",
        "$2a$04$36F2Cb0yhH35ca/TF9wgQ.Yy86qNUj7BRICzu/rJV1oMr/RZ4QFI2",
        "$2a$04$xql7Bt.3W9BMFEvk2I1/TumCS2Xd1VSrtReRYNuRlM8TLIVfMtR96",
      ],
      guide: {
        code: 5,
        description:
          'Receives answer value as string of numbers concat togerther "21,86,42"',
      },
    },
  },
  "177242447": {
    _unsafe__answer: "False",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$XtOETOdYlh5y.sPoOYc2oe7IxTLCg1sexWt6eeYu/iKLb9msbvugq",
    },
  },
  "177242485": {
    _unsafe__answer: "False",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$l28O2VwzG3OUzxuyurwteOeidlOsKZMIaAQzMFUcG0eGGILo7VoJ6",
    },
  },
  "177242486": {
    _unsafe__answer: "True",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$ShKp2N7vpk6SbdYxjdIYMe8NvlG3zonYnPeTPjwK28KOuzJqMhq4C",
    },
  },
  "177242490": {
    _unsafe__answer: "True",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$Yq.cLJI1vEjz1tr.qfkMYeJnA.kkGDHQk0/mYPFGliPN2D6FmiCJG",
    },
  },
  "177242497": {
    _unsafe__answer: "False",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$dD7u8EHHM1xwrd7SBMDHqOIsDK2nwrSheN59RyMZnAJ2h3wpzXRqC",
    },
  },
  "177242636": {
    _unsafe__answer: "True",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$8IXZdCP71y6J9dfvzkPYWeoQmXsjhQfKOpbRzHtAS19mvkGQmCoXW",
    },
  },
  "177242637": {
    _unsafe__answer: "True",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$Aqy8P4rfmrNa42ffD1kSz.8pCG/WaxjFiU9pUCupRQDgBkWQQkSs.",
    },
  },
  "177242640": {
    _unsafe__answer: "True",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$aoPSA/2b3KOIq.4Y8S/GU.erjphYQcppKpgw6AuL4iuajWLJJ.Vsy",
    },
  },
  "177242641": {
    _unsafe__answer: "False",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$kK.lE0fJlftCeHSTU4jT9.pIfyN7FoMaW7DUAzwyh1bEtPGlN41ZS",
    },
  },
  "177242679": {
    _unsafe__answer: "True",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$v8tmiIzSuMiBf/ZP5tckjO1H.mVKTF80CnXyqQB9Yb7lidO//eDXW",
    },
  },
  "177242681": {
    _unsafe__answer: "False",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$wwUJcpEikVxeC4UP/vupEerc9uiFTsGE7J6BzR8raMN97p8oJV372",
    },
  },
  "177242683": {
    _unsafe__answer: "True",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$zMvkY5ZOyUFUXxahBRRp1.Xdesk8h5vwtZzzbYlMMqgtGiAq5TTTe",
    },
  },
  "177242685": {
    _unsafe__answer: "True",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$/ggVq.XbZJlR.vXPZmfWLePNwPd37bZULyHEz8xyToA9M.Lm9xW.e",
    },
  },
  "177242686": {
    _unsafe__answer: "True",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$k/EIznLJUJ.mAkrg9jOVGOwnI052Trfu7OH/CF0xdNQFWtaojQCW2",
    },
  },
  "177242690": {
    _unsafe__answer: "True",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$4LI6KxC7HgR9Lj5qbDRSveyuIlKe4gLuHJUqhdeL3mtJaPkb2NJ82",
    },
  },
  "177242691": {
    _unsafe__answer: "False",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$kDJiKrz5/E.z.y1ep/WGLeZMRqX6c07ipreo3dFXIQAJsGAVPelre",
    },
  },
  "177242692": {
    _unsafe__answer: "True",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$vCJlMVo0XtIOy.NA/8/NKOCUgf1RZ0PMdk88/aojXfL.n31EMLw36",
    },
  },
  "177242693": {
    _unsafe__answer: "True",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$aecmQWw82cQSvHXMD0q7COeKT0G0J0GwgcPs./CvCxabug70GJsYC",
    },
  },
  "177242694": {
    _unsafe__answer: "False",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$iBGiqoprnm6uXp0dspOLTOb//fSe06MwhW.lS05eY2hgJjfhp2eoe",
    },
  },
  "177242732": {
    _unsafe__answer: "False",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$QV5pZtalgKh/MtPFrssUT.M.zswsvxwd8QDDQxyYEdxUb01y8qbCe",
    },
  },
  "177242733": {
    _unsafe__answer: "False",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$G5rvASCkukNcmquwHJno6.2a67UQAfQcwa5SfrisQImiUzCO7oQWu",
    },
  },
  "177242734": {
    _unsafe__answer: "False",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$XF35Xa89NV5qyLSn47049OhOB4ApI6ka7Wm.A2DUvFyDuhwGW/zCq",
    },
  },
  "177242735": {
    _unsafe__answer: "False",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$bpndSL/8F6KmySVxysc.teQYSsyU.cytpqUrUI.8wrm8VKZ7WQ0Qi",
    },
  },
  "177242736": {
    _unsafe__answer: "False",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$DrY4ErzCB2yP2dxBLGB4sOJubqbEIGVcJ86G8n5Mno8S2NAiPGteK",
    },
  },
  "177242737": {
    _unsafe__answer: "False",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$/R/AWjy.LYcvNaZLqYyWPOqo/PacbcXZ/yOG4FXzBK0HmMvGk9BCm",
    },
  },
  "177242738": {
    _unsafe__answer: "True",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$BPFa5AB64jbH.nS5Cbg...FMbtFmIv7VSDqCtRBFpbXspXESe86Pa",
    },
  },
  "177242740": {
    _unsafe__answer: "False",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$Cvu6ZPLhoBShOOvm3eN8tOChKWhtuT0vpPdCsOdDny1PqjeCfGa6G",
    },
  },
  "177242747": {
    _unsafe__answer: "True",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$4x9KOpBKCwmJVTo1cMc8vObfomzalCmDkOMSJtUyNRIYenTNJTE2a",
    },
  },
  "177242749": {
    _unsafe__answer: "False",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$/uCVG4tlUkqgkvcIRxYmd.kgac3LF5bmtW1Qxv1DOg8U/pCkwOErS",
    },
  },
  "177242750": {
    _unsafe__answer: "True",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$lPWfyXHv9IA7DPB3ILInO.4tsVmQQgPxtJGZg4QwJq73V5X0EyF2u",
    },
  },
  "177242920": {
    _unsafe__answer: "True",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$Jm5Z0wPwP7KbRVa2N1G7AOmn1D7hqQZJhknh4ONLL1n0.ZthipHzO",
    },
  },
  "177243065": {
    _unsafe__answer: "False",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$jEaMFFmkcqopDh5G4iMamuMcKrayswazI/qSnxi7ILFLmHp2EJLIy",
    },
  },
  "177243070": {
    _unsafe__answer: "True",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$N5jEE4hRz/ytR0TKNxnjB.ND21SD/mBLTYHJxpIarEcnucsdtK8gG",
    },
  },
  "177243071": {
    _unsafe__answer: "True",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$gojd/aoo0MKtt2Qm.JOib.ZcE/lT.qi2cLPOybddnO8V8G2/LiBfu",
    },
  },
  "177243072": {
    _unsafe__answer: "False",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$5OIMNh0i6atJAInjeMttaOf0itaRd2INEuiBdDeAQwT0bCBVz5DWe",
    },
  },
  "177243074": {
    _unsafe__answer: "False",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$1GPj81GXjWH4rztpx9qqyeDvpIg45nYWBc97YE99TpNEeGTpR6IDC",
    },
  },
  "177243075": {
    _unsafe__answer: "False",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$py1VX2nOdDZ0UZBR.XDsUO992sjpMTiBfVwbkZo4KMCmRn3CM/6Y2",
    },
  },
  "177243077": {
    _unsafe__answer: "True",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$.WvG18AIN1k2sbUKSCIJcuYQbEMFxr3RdYoD5meUqoBUkOXQl2qla",
    },
  },
  "177243078": {
    _unsafe__answer: "False",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$CY0/HHkObYfLXLktT.5qFu0wIG2LmRk6zUBJlWJThvmQ1JZJ6HBA2",
    },
  },
  "177243079": {
    _unsafe__answer: "True",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$U.Aah3Q9M/XpEPVhbFOMlehCEWx6kTPRbMLsxPkCeEwaU4LEalOKm",
    },
  },
  "177243080": {
    _unsafe__answer: "True",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$HNKamQjOETcfGlLXiIv8pOQMxTfaafiujvX49T/.hLvwEgO.VSn1a",
    },
  },
  "177243081": {
    _unsafe__answer: "False",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$akzBX2hGvgncvk7xbNCnbuAmq5Fg8DiFJ23SOQNnt0dibvxYG8BM.",
    },
  },
  "177243089": {
    _unsafe__answer: "False",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$8poqlDkDzkVvN66E0d4sp.BzV7uNDbQrsr56m60sN5ZbHofG6eDj6",
    },
  },
  "177243091": {
    _unsafe__answer: "False",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$NW51ImdJ/xMGMlR6nzMsKO3ivFSkpKd4NO2nd1WrNcByOeT86t5Vu",
    },
  },
  "177243092": {
    _unsafe__answer: "True",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$JDGt.7vFioG1VCuBHrbDIudGJoNroTEgb2HGDmbp/1u2AX6Jk3j7a",
    },
  },
  "177243093": {
    _unsafe__answer: "True",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$3mB8uGW.5Z9k60olGJqACeOh374zdAi2NquCFdEAPACl6HpL13JGu",
    },
  },
  "177243094": {
    _unsafe__answer: "False",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$phXfcttDxXr1789.rMa.YuhHhvbz8EZyyILrOZb741sXImg.Rfkqm",
    },
  },
  "177243095": {
    _unsafe__answer: "False",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$sCqm0i5g2YEl0EbORqmrku2G2eorXpiH462p8KCGk7HNK/al8vEfq",
    },
  },
  "177243096": {
    _unsafe__answer: "True",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$XrbUhi0G4Pnps00HIpKvtOd4hTbQhr0zOzJrh61L.MJ4HaA5wjb3O",
    },
  },
  "177243097": {
    _unsafe__answer: "True",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$tlYKTi4n7SeBC94/xqKKW.YZosdeiSE2Em9D74oPFj7Ti0oRhoFp2",
    },
  },
  "177243098": {
    _unsafe__answer: "True",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$8WFnPdk.UYuFdDgolEchU.jGnDNyNT4BMMN7NUg6nOk91pvXlBwIq",
    },
  },
  "177243100": {
    _unsafe__answer: "True",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$FOZ0diXyBkI73rjnNbe.rOC4tsqLkdjSUOTzji/DSQxkE14es.Ykq",
    },
  },
  "177243109": {
    _unsafe__answer: "True",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$zzpkDdEDUWYMJq5BWc/mJOgUxUnuopB/W3t4x6BQVUswM2keJ1wYG",
    },
  },
  "177243115": {
    _unsafe__answer: "False",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$HEsGpAb9WCXWyio3BIL4bejj6/D6cC7Dz3dA3XNTw9QPP8Me.4H1O",
    },
  },
  "177351316": {
    _unsafe__answer: "True",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$DWvNdrjt6kxttrZHXcmL2OTiRR477xev8pO.ODMEDPZQFm2WlQRO.",
    },
  },
  "177351317": {
    _unsafe__answer: "False",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$glxqJGiSXnrKCFWFJZaIQOdj0C2A/XgI8wh5ah6NQpayhMQtNOlsa",
    },
  },
  "177351318": {
    _unsafe__answer: "False",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$0IxnJra9rNM9BSC1jAdJuOrhJ5I3sPjJySQZCWAmbrGZKiw/fukqG",
    },
  },
  "177351319": {
    _unsafe__answer: "True",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$YQYSg4LkTuHB7RSBpXUKoOAOa7nCcCj5E/jwTxNZDMdacqcWTDvIK",
    },
  },
  "177351323": {
    _unsafe__answer: "False",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$6Jz4wRExoyFgXcBZjNkH7uRU7zCLi4JN1XquDlx9l9/psYcHRbU3S",
    },
  },
  "177351324": {
    _unsafe__answer: "False",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$2DLNGqLM85CjQHAwEZ4lxeSsm6ijfPEG6gbC9Mm4PT768ZGjL/UkS",
    },
  },
  "177351325": {
    _unsafe__answer: "True",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$2C7DdCkbQLn0vLaoXtSA2uvaYvWxxubKUFVzjhgU6JbR2iCDZHOP2",
    },
  },
  "177351327": {
    _unsafe__answer: "False",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$fpoXqSBL75BMHHQMuGsReeRUuPwA14NXXedW2DbHvDH.uuPS4pXdW",
    },
  },
  "177351330": {
    _unsafe__answer: "False",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$8MumPM8kPsewPTay68A8veph7UfM6dJpBW/Q7T7NG3XIjmrQ5UD4K",
    },
  },
  "177351334": {
    _unsafe__answer: "False",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$nRwG7tBVD45Ez4mwCAcYwuUIJsa3Y09weJsrugybcPhugPFRCSXcC",
    },
  },
  "177351336": {
    _unsafe__answer: "False",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$VNSSDez7KLCoWv8XkbM3/u/HG/nhnKif1LzuZsMmOVTfArb0sd8hK",
    },
  },
  "177351378": {
    _unsafe__answer: "False",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$QDnKNZXyUjPItVMAKc3dIuD.imkWLV4QDKuzKfpvgzZoJDd.pCCky",
    },
  },
  "177351379": {
    _unsafe__answer: "True",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$hfkP5mxKqjN295g1OLeXb.j6shn.3NKi.9gmOY.mNSDkXZKzYLXkK",
    },
  },
  "177351393": {
    _unsafe__answer: "True",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$m4l7.MglfNXNCWDxWYSK.ecb28d2o1g1l2r6qJkNx6UrUZPim5osK",
    },
  },
  "177351408": {
    _unsafe__answer: "False",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$bj0lB6tGQl5O3I98N9AuVOtauWVgwBmW4yOS6mGu3ez8Uy5zrJvAO",
    },
  },
  "177351410": {
    _unsafe__answer: "False",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$xPLK4jitd6usRs7PURhHNOjHgsR73yCs9vg35D118sY7mZL9GFpyq",
    },
  },
  "177351411": {
    _unsafe__answer: "False",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$dj8H/s6v80DbgPML2kLwIe467dPQqZL/C5fpKz1o4uCB6cKsFeoIK",
    },
  },
  "177351412": {
    _unsafe__answer: "False",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$9cm8xjvnVNXVhm2XXDdYzeSIpcM7evNO7.YdkBhdO9R4h.C9JiznC",
    },
  },
  "177351413": {
    _unsafe__answer: "True",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$msIG5nmJhGEIt8rQxUw5t.o7Jz0CLKdm9/eGu9R/yPl..ss0KbLJ.",
    },
  },
  "177351415": {
    _unsafe__answer: "False",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$Hqes60RpoCPFOXxtdRK.furtlt/PUbj8vwbUU910ObDxy4Fau2LyK",
    },
  },
  "177351474": {
    _unsafe__answer: "True",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$.i4/fRtBVVUPv.mjvsk5TeymvgjNqtIzpSCdb8tbK2EBaLO/cT4Ru",
    },
  },
  "177351481": {
    _unsafe__answer: "True",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$1J0eFyu5z6cJd1ci1g/LZuMvWovMPiyEdP3jelVnMJrwzPNn0nkMm",
    },
  },
  "177351482": {
    _unsafe__answer: "True",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$sgS1SK2btltm3BVivUJ/ju5O9JftUuaPd4z7O4JAe/GgmeSd4CjbG",
    },
  },
  "177351483": {
    _unsafe__answer: "False",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$Smdom4KOE/0a6pFmunBBsOfhEACzkXG78X5yzt75Cs8NgmUyMSuta",
    },
  },
  "177351484": {
    _unsafe__answer: "False",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$3ZLucUwd6.V/mgOmW8SIveBuBuMfWQZcrJVfNcW4TWC1csgy8nztW",
    },
  },
  "177351485": {
    _unsafe__answer: "True",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$RluKB3TVDMsNpy77NgfKWu9mTrBUVubwIGvCv6AqbC3Tdnn1cxvEG",
    },
  },
  "177351486": {
    _unsafe__answer: "True",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$LUurvlaAD188uw5UtDh1hOhtSkL1En6bqNZWntdxzhtE2fcqUFg9i",
    },
  },
  "177351517": {
    _unsafe__answer: "False",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$tDdhlJgTjLFYQZcnysayGeIrUwmhoYhIZu5VgMSDzelJQrdPSWbrG",
    },
  },
  "177351546": {
    _unsafe__answer: "True",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$s2kyGUZXAphPw4g4MOduFOXhnn04LhsEKYRFwAwWzsC9kAXtBO9Ni",
    },
  },
  "177351548": {
    _unsafe__answer: "True",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$0/Pjo6KuKVTYUsVwOYc5jets19VISWUiqbTPocTD5g/doYXksuJcO",
    },
  },
  "177351549": {
    _unsafe__answer: "True",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$T.EFUbc2s.m.fR1XYdyRT.RTC1jNPD6H.BJ9VxB7VAUbIGMXOZGw6",
    },
  },
  "177351552": {
    _unsafe__answer: "False",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$CIweJ4RUSXtmemr721E72.oLV.8G.kA3D0u6VUXO3Uq.SDxMCgL52",
    },
  },
  "177351553": {
    _unsafe__answer: "True",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$Zv337HQlBbF38VGYgTgxGebMTSR7x4w982mUiTP2qx5aIBJDMd6hC",
    },
  },
  "177351558": {
    _unsafe__answer: "True",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$z1WUcv6r4lqIUzx76IzrmuVfwBYpakWtloyIOvh1bMYFm/FG2S2Na",
    },
  },
  "177351559": {
    _unsafe__answer: "False",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$IInOqpEMQoHYKVuWtR7zCumOOW5JlumOhD8ZYeDUZKoxuF.W.ClMW",
    },
  },
  "177351562": {
    _unsafe__answer: "True",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$3ev4vORWrl8wkuqza2W4KOzTK8eX8Y/j6O1YO2ZnM7aAuC89hHJXS",
    },
  },
  "177351563": {
    _unsafe__answer: "False",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$lF15S22js6vQeJG.FJO/AelwpHmyqIWQw.wCB.wQ1cVe9fMtbCz1.",
    },
  },
  "*********": {
    _unsafe__answer: "False",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$LYMGcpmOPr1/NNn20XZ6SuoW4kQOMfERlh.I8TV1hgwtVHIhX53Ce",
    },
  },
  "*********": {
    _unsafe__answer: "True",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$wgkywueq44aRal.LJxZMCuNKfMrjo.4ikbwVVJtaXbSRWvESXImfi",
    },
  },
  "*********": {
    _unsafe__answer: "True",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$jeJIRViagOYE/sxPMMzsqONByK.iwNcFZbRuZY4dUQO1S2G3/bX7O",
    },
  },
  "*********": {
    _unsafe__answer: "False",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$IYIk6Mdh6G5civZrbrhycujCVL2MmrpvU9YyjFElIY5vVlheyJr8a",
    },
  },
  "*********": {
    _unsafe__answer: "False",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$TRMm/ZDLX8s0W46j31JbDOUjoqaSZHF0MbvpBy.F3Xqrn6DiE2pwu",
    },
  },
  "*********": {
    _unsafe__answer: "False",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$0K0oUv4PtfndtvaM8eUJZuurlSW.LFrwL8Ydi4ADjt5xfhLNGBJEC",
    },
  },
  "*********": {
    _unsafe__answer: "False",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$ZoaridvI4KJSqRVUsZRTseh18TfheMUANr55SATOHurZMv7DpsIXG",
    },
  },
  "*********": {
    _unsafe__answer: "False",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$mz7ZHy2ZIRvRzS9ACAp2F./n0ONG39jOc2w7B9JlOnFYcFs0PUEaG",
    },
  },
  "*********": {
    _unsafe__answer: "True",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$TW3u8Q5QUKDmhvmYM/nG4.4oSxrieRreb5zGzqmOnG3El6c3Sm4RC",
    },
  },
  "*********": {
    _unsafe__answer: "False",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$ejqhQxuVayvZ6pk/Ah/PbeHP9yteWopc53Q2RLBpZOOyFUfwS03aK",
    },
  },
  "*********": {
    _unsafe__answer: "False",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$Bk382xLFhfTIMsxUiAU/ZO3SYOrLYXkQ2XEInGUK.6s4LjTKt04bq",
    },
  },
  "177360953": {
    _unsafe__answer: "True",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$15wSiSmtyFwAywahyU.5IOkSapC38sa/rlskbUpodKqXVRApjeay6",
    },
  },
  "177360958": {
    _unsafe__answer: "True",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$sLIO/NvOlPx0LptoFk5Bc.mBvSwW6IB059yN8c9o1XfrzuAsTnv/C",
    },
  },
  "177360959": {
    _unsafe__answer: "True",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$HhgF9MeL81pumSqjOxClL.BN0DiDNhUkaI7UKiWofuIwtv83HmYW6",
    },
  },
  "177360963": {
    _unsafe__answer: "True",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$Ew1LHxO1R4dN8K0yxhR4.eHk.U0IJnLmSwUKm6iY7ddGQru1J5lUC",
    },
  },
  "177360968": {
    _unsafe__answer: "False",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$lFhxs119E5vEYakUxF5IxuWeKtzFi8AsZviFv9oqDNOfnrN0vqUBy",
    },
  },
  "177360970": {
    _unsafe__answer: "False",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$TRrFyk2g.0vfA6fLm5RQPOnv2fKPya29yB//qe6OGFsfV1e2N5Vvu",
    },
  },
  "177360973": {
    _unsafe__answer: "True",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$itYuKqVRN0wzKC/vMWRmweacwmEwKrKW4.Tj5.t/O48FCt9.bUB56",
    },
  },
  "177360978": {
    _unsafe__answer: "False",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$zplS5FICoywRRpdRK7PhbuaUwDd9RvvZeV9/90.zvN4QclWdLS4oG",
    },
  },
  "177361001": {
    _unsafe__answer: "False",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$183snVI5aLf9SoK2hzCVR.FAj3GVy5ShTeewpgvFW34wxPT8q2dD.",
    },
  },
  "177361004": {
    _unsafe__answer: "True",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$lt7x2v0atYusBqfeW3amYObg5xlqgwU05W6Cw7VNLmpX5oGWYWLD6",
    },
  },
  "177361005": {
    _unsafe__answer: "True",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$SixUFAzOgAFb1vtlbeaCvuioD13SnLfbkGhhZT9tTgImCPMazGIDG",
    },
  },
  "177361006": {
    _unsafe__answer: "True",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$YYtvzlPLqxIotTCcXJQ5leC9w/iAqKNyEPtzJKUMj/N/OYJcgIPoG",
    },
  },
  "177361007": {
    _unsafe__answer: "False",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$fWyEsnzif864sUdFs8ewsuY9fQHFDwsRmGgr0qViMqM3aN9K5lg6a",
    },
  },
  "177609694": {
    _unsafe__answer: "2,5",
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      values: "$2a$04$e08h7nXc2umF5sNNwawdU.4Zqfy/S9/9UoLWrqW8NA5gw7wlTiOrW",
    },
  },
  "177609849": {
    _unsafe__answer: "3,5",
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      values: "$2a$04$jCKHt9z6hp6aKxqyX42kSuaW5SutnthuW5Xo8G9bZ0MjSFdwd4LSy",
    },
  },
  "177609869": {
    _unsafe__answer: "2,4",
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      values: "$2a$04$U.loC1xeGYKc83GR7SlqSOE7QpaluehGHwND3nHqIOLLjKUqrOp12",
    },
  },
  "177609879": {
    _unsafe__answer: "1,4",
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      values: "$2a$04$Kq5/NTW0Hy200Y1G58Sd.eQdoBbsUSFelY0L/o9NNuGrakJLLkbYu",
    },
  },
  "177609883": {
    _unsafe__answer: "2,4",
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      values: "$2a$04$L0CXKu.ErJsBclTyCBb1HuoYgOHYczhd2nVDCg1lPkniUpvilt9my",
    },
  },
  "177609885": {
    _unsafe__answer: "1,2",
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      values: "$2a$04$CFQOYgrGZs8A7FUKKJpWROF3joEZGTiz//dMkSJEcp2hzLqC1F9V2",
    },
  },
  "177609888": {
    _unsafe__answer: "4,6",
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      values: "$2a$04$1GgQq/Q7jazmMfFiV1FiSeU5sy0aOf8jmyScpMs5dr6jRzqaKZcJK",
    },
  },
  "177609896": {
    _unsafe__answer: "3,5",
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      values: "$2a$04$aPkCnhZ3PGZP8Zy1kxeo6ewoD66pTdgIISibQs/nj/uhy.uEO5kSm",
    },
  },
  "177609901": {
    _unsafe__answer: "1,4",
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      values: "$2a$04$XAsQagTJFVEzXV5E046BjOGLFuuSuajl0UQRmHSD2Yj59khgt5u0e",
    },
  },
  "177609904": {
    _unsafe__answer: "1,3",
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      values: "$2a$04$eVFT.ZmQJei1FWjJ1E5nBeCe09Y3D.fu6kyYgGtjtr2GeZO7H4cNi",
    },
  },
  "177609906": {
    _unsafe__answer: "2,5",
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      values: "$2a$04$D3eQFbjjI8FpyxNpkXNAx.c67qtne5g4ppCGZGtRp/IFLj0XiJ3EK",
    },
  },
  "177610963": {
    _unsafe__answer: "1,4",
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      values: "$2a$04$rhftQPCNC7qjHV00xDSNHO/mb85O8/V2Ny86aUv4ShyRLKkVvxy6W",
    },
  },
  "177610969": {
    _unsafe__answer: "2,6",
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      values: "$2a$04$6s8G8cYWzPIaE7VLVdfJlOIFCD.iv3GGMEVnENDPRB1fKgGD9HTOK",
    },
  },
  "177610970": {
    _unsafe__answer: "1,3,5",
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      values: "$2a$04$63pIpZkAAe5.62jWBfUVaekFBVIzAvAqkBRXzfyuFgjSGBwJfpGSa",
    },
  },
  "177610971": {
    _unsafe__answer: "1,4,6",
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      values: "$2a$04$mbgYCFh6SU.L3Zbpp0bYp.6Ug9twZRWf73UYi8J8Yy6.1CtjNRgO.",
    },
  },
  "177610972": {
    _unsafe__answer: "2,4",
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      values: "$2a$04$PU4TD3TGeyUU7fvhtU8SFOSP.NTFoFaRNYsnjpm.4AtgH6kaf4hQK",
    },
  },
  "177610973": {
    _unsafe__answer: "4,6",
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      values: "$2a$04$D3snBbFDM6KUkT8kWRKO5OYY08kv/9M/WbBtIfXuf3FYWXcpxj4ry",
    },
  },
  "177610974": {
    _unsafe__answer: "4,6",
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      values: "$2a$04$e1z//QI3QVV53P/K9Stul.aYxrwcnRP82hFpHnaUC.pmbpSoSnjw.",
    },
  },
  "177610979": {
    _unsafe__answer: "4,5",
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      values: "$2a$04$fWLQixz.CssYrrhkXxb38.f4TNb0r9UFJkXf90kvChGNCaawwRYxG",
    },
  },
  "177610983": {
    _unsafe__answer: "3,5",
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      values: "$2a$04$oOtDtzVuViwEQvQ6hPjrj.Lt3veKO4lMKVQcJJQ0XunkgWHtSWXQS",
    },
  },
  "177610985": {
    _unsafe__answer: "2,5,6",
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      values: "$2a$04$LfNIJQpDaPfHPaKuXApbxuPnSNR.UqN8evl62iEHqiJf5Fm5fjpgi",
    },
  },
  "177610986": {
    _unsafe__answer: "3,5",
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      values: "$2a$04$KLE12VC4pY3IroRyVAVPkulJJRNGqUDFDBTkBRdFMg.yBvYSLJHfa",
    },
  },
  "177610991": {
    _unsafe__answer: "1,2,5",
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      values: "$2a$04$9W20CHLyGTfG4epqb5Qeju1gVyqWhnUvF0e8fyZv2T5yZDYwg4GMW",
    },
  },
  "177610992": {
    _unsafe__answer: "1,2,4",
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      values: "$2a$04$9CvDq9MZzTG1h5yg9b4tzO5.DNru.H9TvEQTnFmvWLnUwXu.Agn/O",
    },
  },
  "177610993": {
    _unsafe__answer: "1,4,5",
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      values: "$2a$04$W90uemF2JQW2E/Z51lKHzOEbRfayIsIs0G5Qnq8cZee6Bha/PlpPq",
    },
  },
  "177610995": {
    _unsafe__answer: "2,5,6",
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      values: "$2a$04$K5Qgr3/a5XPF8t.bwGW5reTRHQIjvsid81FDVxCjEzXeMy1eP.iqu",
    },
  },
  "177610997": {
    _unsafe__answer: "2,3,4",
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      values: "$2a$04$HZZLHXOCsYF8br1NHTDGEu3UDmdzD5IVEHvYzc34fpmF7dROEcXn2",
    },
  },
  "177732173": {
    _unsafe__answer: "1,3",
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      values: "$2a$04$yvVVmPdFqmfmVB6Arn9RhOcMuJjuDuEykw5cstZs4IHvD7zRRRYy.",
    },
  },
  "177734185": {
    _unsafe__answer: "1,4",
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      values: "$2a$04$w41CxCrvPnTR5WFEcMBrS.IepNUYnlnX./vICKWfR4TL5zv19xIoS",
    },
  },
  "177734189": {
    _unsafe__answer: "2,5",
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      values: "$2a$04$dKfSL.26uIAzeF6K9NAQp./7fhzFNWFmY79efr38eRKQdnQDHXZZu",
    },
  },
  "177734217": {
    _unsafe__answer: "2,4",
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      values: "$2a$04$Ns8MoUripaxa2ndGgMwWP.DqpPkPG/lo2aGuuhALcRJLNtb03cWl.",
    },
  },
  "177743573": {
    _unsafe__answer: "2,4",
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      values: "$2a$04$IL7msogL3lvGvA2hEfIAzuI0OWGdpwma9/VRBY2FKYDnIx0./sCCu",
    },
  },
  "177743614": {
    _unsafe__answer: "3,5",
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      values: "$2a$04$XbflEHdkA83MWGpwpNSozeslRh2zi01pG0JL/pSTwhpV9HKhXEeZ6",
    },
  },
  "177743637": {
    _unsafe__answer: "1,5",
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      values: "$2a$04$cOHlTOnAsA6E19C1Jida2ugvMzST2sE6pZzCNIlq9RzHyGKdW3oda",
    },
  },
  "177743642": {
    _unsafe__answer: "3,5",
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      values: "$2a$04$gx70/pPFJ44brdo8D5YBYevAvotdBDqDTEub6aomVNBlrn5ZTkcKu",
    },
  },
  "177743924": {
    _unsafe__answer: "2,4",
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      values: "$2a$04$C/bS/8QiQQYW8GxFKpdlVOk6yS8uycEFOwLja7zZQv5oiSiEIhgO6",
    },
  },
  "177743929": {
    _unsafe__answer: "4,5",
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      values: "$2a$04$/ApK.KmY9BKZakFq9gDRjehfXWxUnsHOZWfJyQEhpusGGjUU9X0zC",
    },
  },
  "177743949": {
    _unsafe__answer: "1,5",
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      values: "$2a$04$E6Lv0ZH3zo8YjEFqZqYrLu.XsdnOdtZbgfSAhRcP/r1iBMPxnXUrC",
    },
  },
  "177743982": {
    _unsafe__answer: "1,5",
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      values: "$2a$04$UD2nFOdIhpKkbhDfNEWYT.3RAZ/uEGyuPV6kyc8/r7Jsx3doF3Vn6",
    },
  },
  "177743983": {
    _unsafe__answer: "2,5",
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      values: "$2a$04$9GfvZW428dAkMvEZ.JnJoe2yDwx4ks.MaWyj/ywuyTa1kZRl11FRq",
    },
  },
  "177743987": {
    _unsafe__answer: "1,5",
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      values: "$2a$04$3wNsr9O.8FnnT7jGBMPa1upWLT6lVR5qBYTaC7f2D.eQ63GjXUo.C",
    },
  },
  "177744063": {
    _unsafe__answer: "1,5",
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      values: "$2a$04$SS02iOjHXlv.GNnttJmzFuFP1Ih4AvzIdlEU1qoEac8LyHOzsUXrm",
    },
  },
  "177744064": {
    _unsafe__answer: "2,3",
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      values: "$2a$04$xidHTCF2WANWbFHY0pGhpuduEBWPfxRLENtRod9pxWfFXGkIIxp3m",
    },
  },
  "177744094": {
    _unsafe__answer: "1,4",
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      values: "$2a$04$2LapvJvul8yVI4M9pyvfROvLnuSYplM4Q6Osdnro.ziEwJmXB8ICm",
    },
  },
  "177744655": {
    _unsafe__answer: "1,3",
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      values: "$2a$04$ob9GErjHh6QstiSXjjXIfO/zMc6aaSpDcofoHtaszESsdcc91FuPW",
    },
  },
  "177744813": {
    _unsafe__answer: "1,5",
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      values: "$2a$04$A7yywnO.rs9O.ouelVR6/.MQ45ZfdlWjRGCN5pEy47lkcn1X7WLve",
    },
  },
  "177744835": {
    _unsafe__answer: "3,5",
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      values: "$2a$04$Tmpt1ALSslhPwWczJ.TSl.Z8VnbVKEnBcTvlMgh755d4mARLeVnUa",
    },
  },
  "177744853": {
    _unsafe__answer: "3,4",
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      values: "$2a$04$jx1eC/XbtbkJIqYR1lZ/K.nAbTWeTmYpQYNJry9nJ6miS/t5KIOsm",
    },
  },
  "177744896": {
    _unsafe__answer: "1,4",
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      values: "$2a$04$0aKXffD5V2AgH74BKLdStOKz6Rwi8oDzPl9cJgnLemDTYAoSfXCQG",
    },
  },
  "177744927": {
    _unsafe__answer: "1,3",
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      values: "$2a$04$N2w9KByCOksZYb1F1dx1muFO7OV5PZwZoFgGtpz94tjemwA5e9CCy",
    },
  },
  "177744960": {
    _unsafe__answer: "2,4",
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      values: "$2a$04$OWjxUxeqqwoBcutefWFbAeLG/GxhOWCh.VjqSkKMK2e8GsQH8shDK",
    },
  },
  "177744989": {
    _unsafe__answer: "1,4",
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      values: "$2a$04$TwGhmcewjEr/M457LktXKu4DFUbuFyDfQpDBUeNfhOkexLAhdHUfe",
    },
  },
  "177745005": {
    _unsafe__answer: "2,3",
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      values: "$2a$04$v5zyRqgdgsk.WkHE9jD6k.9L/LzBhfcaB9AlaYYCNDLaDPRuPT8EO",
    },
  },
  "177745026": {
    _unsafe__answer: "1,2",
    responseType: "multiple choice image",
    answer: {
      markAs: "multiple_choice",
      values: "$2a$04$Cc1X.rXg4yae/zpzE2DL4u8jFIS3rV9Ve0NcZo4Gzwy.7PoWdDm5O",
    },
  },
  "178620232": {
    _unsafe__answer: "large, gigantic,huge",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$2pcBtrMpbeITOzSo/J41Z.Jw8jPHcIBJqHZ07XtQiQDZ7N6UwG0UC",
        "$2a$04$qo.8QbuJ4ouFy03aIQJJaezqQa6oa/ayp52xkuvSgHdbl8qhUCl.K",
        "$2a$04$uIUhFPBhEr2mQQU1hIBRTe3At7cgYyL1730y21jaTIQLcE9bJpkqu",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "*********": {
    _unsafe__answer: "sleep,rest,snooze",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$O9QnLiOxyQOXojdHPMrLuu/FpcacnQj29Enw2CvXk.XjG/JhlRHWS",
        "$2a$04$lvncm.1rMuEaPcMJ7A1Y5uLNUQf.fMTBp4uCHt4/AQLDNpxPyPuEq",
        "$2a$04$HBsPh912Guhhk0kJBw1aOOm4TdztBzFgu25YgMntyXq47QJd8.J/a",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "*********": {
    _unsafe__answer: "mad,enraged,furious,infuriated,irate",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$4SJ4o1DZycog84nfb72JB.h30Dw5ymXvtnRE0OAVqB/8veLgEQFI.",
        "$2a$04$K44qHUCBzIAmQNXdCJ.1.ueLmq4zFlPzM6lY0fi9.qbKxbmGUvU.i",
        "$2a$04$t0rfNHK8BW7RdT3lY1t2K.odXWZnNQ3q.PnljiHA1dDzJLIGzWss.",
        "$2a$04$LEv0eXvcc5.6e.xEWbqs/OskRHGrIQEaj0FILeaVMDlbr60tTAySu",
        "$2a$04$L7rppg/o.gKBfoZ9wBrAteduzqIT.uiHpM0RCkoUFEQ7KXt.lGLv6",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178620461": {
    _unsafe__answer: "big,large,smaller,tall",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$yHuTLictRwtIDrImz90c2uZgHzlDfhQk4L2Q25VXhfMpRNJWju06O",
        "$2a$04$7ykyBN5K56gkwHJNX0mpKuu3A82jKyglJkHD19YVsxSAdI2ugOqwm",
        "$2a$04$sFeEdetBdAJdijYdqKE1VOvUAnoBiN5NJL.UIVnmVJ0y368ltgeMS",
        "$2a$04$owOvNYooxoYLkeuIcieuPeN6r00um63CsOZvop3bBWRWhOfJq8itO",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178622900": {
    _unsafe__answer: "mummy,mommy,ma,mama,momma,mother,parent",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$5foPh/QoeofsffzPyHqiAey4SnspB8GUiSuQXeJiJIF6IckZu4qfS",
        "$2a$04$CKBSR8.z0QYObyUZyRI7f.0ft6xjyl7s8D48jBO2JWpbP1pjv35lm",
        "$2a$04$meGL1o4GxSDM9plnVn0GVOArFlRKSNEyIL77qgbL1JDJ3iZJeEi0W",
        "$2a$04$7ZNOoVqrhZpxjKKHWm9xO.zrS/FDGPEBgWHeUh2N2.C2xs6mm84qO",
        "$2a$04$OzUSFYVHgYaE/bWQIdMbF.p5mO8Qb5M9zLu5xYKX4z9qH0ah5asTe",
        "$2a$04$ntYXbAMg7po4tDjvsJsmNexqSQ8uPs79jifaWu6ZsKxglVuuxMJi2",
        "$2a$04$pg3BEYV/usUO.c9WCqvXaOb9vZfmLnGNjre4nfcYIV8XoV.9vG.pW",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178622905": {
    _unsafe__answer: "laugh,chuckle,snicker,titter",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$lA9tiqm6rTWogJtt0ha4Xucr1GTdAPu.bjIc1MwSo0ANmwrN3.KY6",
        "$2a$04$xhADmTqRT3L/3l1FujSzcu4lZrYZFJKIMKYAN4CTl8S/vhNM4yi.2",
        "$2a$04$G1YhOlnUqQ5cjk0O6ASx9O4vna6enZjwSOUJq63mK1wZI1MfkAMwK",
        "$2a$04$7na3J6wRtTqzJInuEgT6kudz.RqDjqtHaeHLCMqvQv0noV2kztmCe",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178622911": {
    _unsafe__answer: "glance,gaze,observe,peek,peer,see,stare,view, watch",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$ddB9ZLaXnsIc.fmax9bBwObSS2AmRN33WUTOxF3ajNrTiwhDuy0Qq",
        "$2a$04$PSzKZbQA1zCL16Uo9kUrc.fcGXBlDQAoneO5ruw6gdnkvQLAOfALy",
        "$2a$04$PHZlReYVXT.spZdPed2xVuse53NEhDb1mFBtVPBCS/O9cEG2Cfw9C",
        "$2a$04$Jn7cGlgj23gvqxu/pDpfvOr6QNR2w5ok8m3nUUb7oF71YYSCSWReq",
        "$2a$04$Ujq3HHUyuSsN7UwXmh9ek.gkg7OvWfglIyIkM4XqeEKivnyMcLnn.",
        "$2a$04$OamAe4lHOn629JXIkpdmSeuGd7e0tlbcx7.iyG2usys2IZNKh39Aq",
        "$2a$04$IMcL8/VVS3Jgf/DqTpPY8uQko8Rvyxo98DdheWE8TNT2Zlx8A3l7a",
        "$2a$04$DcerbuVWy.HlYk1t0afPB.MrbXRrT4Vv5Ej2YJTzTMLflFva76tbq",
        "$2a$04$RTpgwZ4n7V085Rtz9jva..WgY6h5JaGAbuf1VQIxGEHwbfyfGH9oi",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178622931": {
    _unsafe__answer: "cook,baker",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$Vh10SfgQmJd5QX0IlfKhEuP3AzeF1zKVBYOw1ejsaHttcjNfYzEVy",
        "$2a$04$b0pj2IppJTZMhHWNgJg9P.FASEgIqOMZu755FwwKnvSG/VNw/kP4e",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178622933": {
    _unsafe__answer: "gorgeous,attractive,elegant,handsome,lovely,pretty",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$RMXpuzzJVPupakZMv.1kPeK3rKmkOi/mWC/1OeilJBOmJLl7p9UT.",
        "$2a$04$HJ6xim/L1spo5L.yXFbpIuCa6SSphouoNE2B2XaSZzEGmM/x7WT9y",
        "$2a$04$wrh/ukdi.WI5TorDGvXEuewX.K3cOCn41T3CGPtKAkD6Cq2jYNHrS",
        "$2a$04$vh/UQKXclXsFWxPsljYbUeFjk2MDsdMJKZMB8YrGa06Ya8lSBHFUi",
        "$2a$04$CJ97ua.1oc.RMsGrhcaI6.5SfLEWKJ9FIeEN7zyPB.IqcQeDp54aa",
        "$2a$04$egegPwlvmt.17Sumhi58Tu.7Wrl5ffe/5vQySbI.iW8o8EbC4bvFm",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178622934": {
    _unsafe__answer: "yard,field,garden,grass,green,sod",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$3Nji.8CpaSF350JyZ6MAbe7o69sS6ToUWySD2axM/P/S6LEdsYeYO",
        "$2a$04$YtqELHUog8Lx7VuWr3n3I.9QolA/E3pg1qd7F3lJcR0Ds32pjkuwG",
        "$2a$04$CxZOpZ6yHp/OYjMAntKniuK4BJENxb7NdtsT//oK11J3daZJE4Dqu",
        "$2a$04$Gn9OyE6eKtCsB5y0rP7J3O2lWwulbimwvY4Dv5t8Tu/CkL7IFAWBG",
        "$2a$04$V2FDC3u5WmV0ZUlhLIoHnuDSEDNrCcNgi6vpXkDP/7Rr7MxVcQ/Ze",
        "$2a$04$5p/MUqufLc69u1NldJs45e7dZSIwN/cmrBluXA1B9QcgexOdst7Ei",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178622955": {
    _unsafe__answer: "vehicle,auto,automobile,van",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$091vg17r8QPu.w7haUJJZe.NIS4Fsl70ArYdxXMQ.nsL8aQhrvuTO",
        "$2a$04$iMWecMMRD6WqoljNBKZPzOHeQEVGxmuK2KXZRBKecmVuiz1zAPGvi",
        "$2a$04$WxFAhnXT/qVPziRMrXQOZudaeQ4/4NBqawZZB35Fx4jdpl4rJa6i.",
        "$2a$04$RqDAh1UeCNhhdAr06HXrX.5zLh.WyFw84BZQlmvQe8j4WtFC6c9tS",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178622987": {
    _unsafe__answer: "road,avenue,boulevard,drive,highway,lane,thoroughfare",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$U9T4g50hqPzyhrqrZulBRe6QeSwxE2HOhzr.IJyV1qm3dMZt0LGTK",
        "$2a$04$EdEf5qQ/rI/HAG6SCc9gj.CySe2RaqyuTzRRCto3XDkRBaIoUGxiq",
        "$2a$04$zVZrhfr1JGwcU0cW1QeqV.6KB1SxUv4BUi7JUJxkYPfTCdkMiWziu",
        "$2a$04$n9lf32LneUoBDs4xOG.xq.U./srtGzCAjrwZv1PcnEETlgEeT9wdO",
        "$2a$04$QB324GV5kEgQCR23DKp.yO.PcwKO9rYauheXKE4Enbn1d6B7ZVcvK",
        "$2a$04$xr2IM.3QJSMEvHiChADrke.HJ4TYM18njKto2yMG/jDnVpKT7dfaa",
        "$2a$04$SiVwmN.xdHzb.nEgDMPo9utMFMdWDyj0HSSz81LWCgK8sqUtd5jjy",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178622989": {
    _unsafe__answer: "try,endeavor",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$lFRuavtT0a6Hz38gMjvWfexp6YzktSxlxx20gqilNnuit/DZJe3cq",
        "$2a$04$D/FkjMC5QX4LALQf9pLc9OQKN16AvYe4MNEOssicOulutAPzhHy6.",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178622994": {
    _unsafe__answer:
      "insincere,affected,contrived,counterfeit,fake,false,fictitious,manmade,synthetic,\nunnatural",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$OmarshVeyLwU.zS11SbDYe6CrJ8yS9VqW4qHcZGkyatl0VlF0.MC.",
        "$2a$04$dbjJsEll86IFsPJE6a8lQuU8g47phtnu91khQ54BkVTACTyOwSqIC",
        "$2a$04$M3XfcoOY8T5DEtSZNnUdyOB7xFxwaUAjREATp0kfpWuYFsKgndo4m",
        "$2a$04$.xEOfQpINCRT/ec3C0BYDu0FqUHCzY1DAB9/sgv7GzDU344LAKuWy",
        "$2a$04$Sw8VzpCW0dD9oKoNDTZbFesIK5sRWA/5Kc/XNNLd7MRIzQOIJVRuG",
        "$2a$04$kSC1X9e1Ky/04a8pJ36C4eYbUHRZQfVWNDcnEPZH24xF86H7/TXHC",
        "$2a$04$HbzXbQbpOQblyvKw3WZAY.ZW02.BtRtt5JLscQgp2BLfWnneNPxkK",
        "$2a$04$UwA.HEB/4qiwZ6O/w5J/D.cCXMSi8R6A74kGkvtuBlFuE.j0Bl3bq",
        "$2a$04$a5AdyzUyuPB44En0Gp.Axu/2pDJom6Y0ihsAv7cR59hhht.f2Hie6",
        "$2a$04$KWv4Akg5x4mArVUzxkoXN.I7.YQiQIUJu3PygAMmQF0SIINGSr8F6",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178622996": {
    _unsafe__answer:
      "consume,destroy,eat,engulf,feast,gobble,gorge,\ngulp, inhale, swallow, wolf",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$aF9YhXcFZ/YmliSrzccXT.KvD.og7wP7mGqK/L4/jRaPfAXr5Yt1S",
        "$2a$04$WW/zIIKcBLxEEB1r7ZJyZOTRNfM5hSdlN5eRSfXT0Ftoas3X4TupK",
        "$2a$04$TN25TfPiXflmRN/xL3xLTu9vnFAmmu0SnRt68ncpEGZ2L1/0q6QNe",
        "$2a$04$yJSXd2g8VO9q17TPbzvkHujWcJ6Ky5yyUBvTQFiljlO6Dj67JDq3i",
        "$2a$04$Dtq6ygUWQEbrtGrCQZlj1uET6DRkPq87Cp.FRTWa0lo/QtQ2mX8Zy",
        "$2a$04$xkrBeCMdoZ8rtRZMZTUuRumKib6/2q1K7.jBC9Q67rnG/hVPWFTiK",
        "$2a$04$heFGAIY6THN2kujste/iHuTPJ6tpGur1dq5/kwDAPKcg2YmTiXKve",
        "$2a$04$33cJ/aQQDZ.nHMOxSWgc4ekyjA6Z8FM488FFU.Ux0dK3eYeqbMfFK",
        "$2a$04$L8i2z0uQQkhgyDUkGFOOLe3a9yjVR6Mj5slW4qD.D/AmF50YMFMyW",
        "$2a$04$PBGP.lFWBVXjwY87oRbIkuxTIBL0jm5ipnqoh6LoTikHi8/tFVZnu",
        "$2a$04$J7AfALAVnk1tuKf/HfARF.qY0Yb0mgUlmAFb0KJBOU/gsu1FbR/Xm",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178623000": {
    _unsafe__answer:
      "unruly,feral,ferocious,savage,uncontrollable,undomesticated,untrained,wild",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$YHbTp6dZVJTBCTRnP31QP.1sWPv9mU1Vm.jAzx5xPoo5OgFGjDIN6",
        "$2a$04$S0H69YL40NcrePVERo080.brSvqGQyX.RRWz1UG1jAcO7oYbP9u4m",
        "$2a$04$q9zUYuBxuIaeFvIHeBepvexh0C55fDCZgF6C9bYcmOo0pAJMRevjS",
        "$2a$04$ZOcNYkn4GY0JX5PCxtb7uODtdbYPo4/2.ZeIRNJUp88Zn/jgPtipu",
        "$2a$04$5tTUIslh8o2h9ixY1YTDMu7P.qF3dU8c7A0aRnoU54jlPPGJvu7NW",
        "$2a$04$63qkekIkHKZqVVpNPa/rEu5ePBWSnX0G8EB.7gAa06ki80Jie6Ch.",
        "$2a$04$ke1ousNTy5JyjZwHZ.09ie6yh0X65jOrvy0Z2LG7rFco.aKL470yO",
        "$2a$04$yCvSQXbdVMVCk.2I4ZZFfu/MRty5HnLwXr0BxesOqaco7W5iYrlt.",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178623003": {
    _unsafe__answer: "cover,camouflage,cloak,enclose,hide,mask,secrete,shroud",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$jRWNc6FtWIxg/UY/.oyQ4uIeY2FetBtbz0UjXqlOPz26RS4T1r5mW",
        "$2a$04$ToqWgNjmEwIyaIKb5MmIMumdmCQpJDUk/ehG4bmS10rwpj6J2lb9G",
        "$2a$04$z6/0/o1a9YYr4S.w00MYAeWWbLQjBISXrph1C0zdrNjg3ttddtZKG",
        "$2a$04$C9Ii/lUyZJT5X1oE92feD.CIvCweR4BoV2FRTrajxzgcolHhk0EbG",
        "$2a$04$9ZQRzzzkHEmw.ZAeD3.t8OnxhcwYyqebNEt0xmnSuh7seqFQQc/CK",
        "$2a$04$dD66f3vTV3QED3vVVNJtKO685pO10J1AmbWE74KPsIil/cW0mTBNe",
        "$2a$04$8I7YhvKxlClkzQAMT5dK/OaVcxTIbxgwEXAcDt4L.llL.tGxkcbhy",
        "$2a$04$DxRWjg/PFvXYB/FlMOhGMOJVZdxAUpML7T0bnZTyPtZVW2x8zmhZe",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178623004": {
    _unsafe__answer:
      "piece,bit,fraction,helping,part,ration,sample,section,segment,serving,share,slice,\nsome",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$RPY3MK9dbrI3yPbPrh4/C.BNrO9.9OiurnW1eqxzfdiXLsO/eyeCi",
        "$2a$04$pGjdtb2CUpyIP8WuhZaDIeTy7ViLalXQnYSXVbO0QEvXflix/cNEC",
        "$2a$04$mgM10iD5wFs4dzGBge2sAO5jGwT5KiGGvE7coEpD6AoRSnXpCbtka",
        "$2a$04$UCDaG2T3koGuoROrcithceF3o1LQbYGUE2wCy.T4L0iD6hxmYK2yi",
        "$2a$04$0hZb2JeyKp4dvhQ9U7bbTe4OqGKOgesy5ncEAypcC3JVderCkuj5C",
        "$2a$04$ereUQAWlu6uQtoIkyHFoC.f5qQ80RkI/I6xT0KW8BsYpEZmE6pTqe",
        "$2a$04$nTo.6hvpj4qqAGxr4qeanuRR0ZXAo.Ydi1OMUsLe2xZkAtPZ.0iM.",
        "$2a$04$kzxnUsz8yNfY8IOhNOA6PuHdNqCzMDZuPNNs597/T3yytBbqpH/ai",
        "$2a$04$OKWDo0wINVxkrtgW2cRvm.q6DvcSKfJYGYUbw9nMQZMWufkNLLu7u",
        "$2a$04$F7L337wFE4p.y41IihXpJO3NI50i/VYHc/Hiog3w.LZLkJci3T8Z6",
        "$2a$04$K7mJaPLiGO1JuzHJZO5a/.6aFg2eoLf2TgM7aekiIaSCotb/3IcTy",
        "$2a$04$2YY3PfsjpHWKRmB0Ty6VeOi3nI9hCykF5iM2NiJIhFwjPnTh6i//y",
        "$2a$04$cmRlwoTqjFh.Fm21vYiRaOlGTCf4tmK9hwd5VgYNH4o2Q5QcnOK6a",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178623007": {
    _unsafe__answer:
      "shiny,bright,brilliant,clear,glowing,lighted,\nphosphorescent, radiant, shining",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$BhSCrhMHH4S15B4Duj3sn.8ybBkS3OhNNqe/9oX1f1mgM4BsZr4xm",
        "$2a$04$fJG/iH1C8SYK8VMZ19HdxeZuzebSKFagHWml11aqxAVrtkXuqyIjC",
        "$2a$04$Tdj3GvVbs0CmAWlenelaFeJXVwN9oomfqgNU64veLv6n2wWp0UogG",
        "$2a$04$TWwcTVH7AasX0oFIkUQ53eOclmu0XfCgXWkEibgNCIljmzjh5Vn/i",
        "$2a$04$vPYQyuct8BwYvaK8QbWJwe.usr1MP94ZHH9eydn987iR.tVuSBOJ2",
        "$2a$04$iFbG30.UBm3c./Brzgs46uuk/u0HPAsclNWyDSgfMUVtjRLFMEbVe",
        "$2a$04$oNL1n6uA1k5uR/5BGCUa7.aq078eDEr9AW/G.UGIV3bT7w2pWcSqa",
        "$2a$04$.w1wwFMe1gyXT97gEa3XCOQ7CYBYU9VQj2SAcAeiDME6PupxN7GTy",
        "$2a$04$tSTKsDxJWAKq7WB5mEsuHu.Z3UYZ7hX1ziMfNJ5V3.34DE9Ox57Za",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178623008": {
    _unsafe__answer: "quiet,calm,frozen,immobile,stationary,unmoving",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$Kh1iHeW4yW1HD4gR83vf6O7k.eFIpwGiIT21xF4jAdS8wWGuLpXWS",
        "$2a$04$Ljq6nlq1WlTnAnRPZboEHOgywQ2lNm.EDw2fxGZ9CXuHxeuNZTUbG",
        "$2a$04$.GbVrj2BdtIlfOzjHO4X7uKndoeuX0qhlBIYJ1emthIIeQh1y0Hhu",
        "$2a$04$XkkpP.DkZtr18XCDYsZd0eRKlM0HHNkqvlv8gdVx9BPQIFZEX1Gfq",
        "$2a$04$ADqgC8GPv0H7304E7SqHc.WuC4Gr2BeG8LeGJg.ukF5/mNhQgH3hK",
        "$2a$04$V0VaASZZ1B8ry2UG1yZ7zu64xeGXcQnu1wvPkC/O4PiC2NqW8tYF.",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178623011": {
    _unsafe__answer: "prove,assure,persuade,sway",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$uSEjDEDpr0Bjd8b/HRvjM.Ff1g4/hhVxnFGHUHhYnmlA2inT9nyOa",
        "$2a$04$zwZD6yB3YJuk/z/RmEpRGOxc4ur4ehki/jvjNncHdHJp/B1nXKiXm",
        "$2a$04$HlpVoSAmYzfFTzJQ2oc2gO/k9pvTtSkC16kGHUhPRh8tQsQXJIedm",
        "$2a$04$SpkLcPmSbVkAlkOlQ5hUreJP1pthl9KymB.5/qpfGbxWp2Jqp.Uam",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178623013": {
    _unsafe__answer:
      "surrender,accept,acquiesce,bear,bow,capitulate,comply,concede,crop,defer,earnings,\nfruit, gain, give, output, product, relent, relinquish, resign, return(s), submit, succumb, waive",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$kPHNZCOK39tNcDTyTGd6Q.k996HEgJiOwAtR/5XaJYHWvP37/i2cS",
        "$2a$04$PyW8q1IW9fTQKzNXGimGmueTsugToBds1TIaE7XpYiBkBP4hSfOxW",
        "$2a$04$YIfM4pbx7haClGz0e690B.rEdPAasawYMMP1OyWFQp1MGALumU9Ua",
        "$2a$04$fMvgdqLOcQXMzDNon3ZWyO9Gig.NPU5J5hgQ.AARcr/xFglQDTFa2",
        "$2a$04$9D3ZhsxsIwdfIuHXjj6WKOReqHdmwa8eoUA4T5yCLYmzBlFnf0uhu",
        "$2a$04$aKXQmnwM6b7S5B5lt3Q2OeAnAjkyGcoyOspWQj./G72wmfC0YmCfe",
        "$2a$04$abfcnbzUzbWvYdTE56vdi.pZ7Y6CmKlZDdnLlJNmzBaYH2UFy3Buq",
        "$2a$04$NfDDChW32l8q2gL78f95b.kwpkColB6OzhBsdGRbUUya88NsnSoXu",
        "$2a$04$POGC2y.Fq20aS9hQ9lk5TOU2KLpsMtg/MCIrHtuMPoUA2KVqv9Obm",
        "$2a$04$b0sqRdJzc1oi6Dal8AOvX.UVf.odXCde.aY9x9J1StdpbjTWCIrhe",
        "$2a$04$xcYACvrxk/3ZgxQBGrhO0.UGWANmY7iejqIP3KwBbTHqbt/WhyTmu",
        "$2a$04$Pag79YAW88exnITaiDo9r.LLAUaUOzU0zvFB.oX7Mfk9UvluH4w/K",
        "$2a$04$D1RWr470m8qsuBWT.FOcFuwESoQSVVFNhgvWNcLgumX90Y8cKwvAO",
        "$2a$04$sely1AxnSg9pXLB2umrLvuq3hoTQrw2/WpwAjbWKRpeYlzvnQZauK",
        "$2a$04$.lvTK2rgDm56a56n3GtTJubiUsrrN/SCBznKuq6xsxSE7h9LpDD/a",
        "$2a$04$RbEEap79S2PKGw4GCg0YgOYCu4vSAnCl1gFwyK05pLDi45KMc2zgm",
        "$2a$04$ZnW6jY/P4zsUamGOSQulfeVaOoeOqP62sWBtDNcSV/sGcFW1uFEEK",
        "$2a$04$juUVcMlhHXOs7qPhp6XA/.p/sc5D2HmgJ2PCEV54y5IdRsKr4W5I.",
        "$2a$04$dCQ34bK/s19V4VCdRNTTheceN.GiobmkT8QehXC536KDBWoTdinrK",
        "$2a$04$4Wm8HT2GN5DBqefcICI34OLuNcgMOV9ak7mr7wf2gstmX3pNA29eq",
        "$2a$04$Rd1iwueUnTy5AqUbtNgPiuo9KDNfoPiwJ0aQWzfU6zBdqjB9rhmZq",
        "$2a$04$1d96pk/2M1Mr4yNb0K2Uh.4OyjFbZphmq7ZkjvhVPEFCSUX4SvEGS",
        "$2a$04$fz51RqRIlz/70TbVPQnj7.7u5T3bCfG9eLwzyzIIWrq5fC/GRZSEe",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178623015": {
    _unsafe__answer: "scant,handful,occasional,scarce",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$OzFHZs/sGE7KHKQ9vu.bSeYxXxJdXMfJ6LW./23TKDaFSh39fTRbC",
        "$2a$04$oT.Bqp5pXNv/U2O7xS5FHOrytYoEPT05YsK92unXcapeTlfU8Kgs2",
        "$2a$04$hwbdsnMxntHtpDSB1N/5Lu1JMtCuFK0nFm0vq.VSQ3DVm1l7ve8IK",
        "$2a$04$Fmiru0gamkPwyvlPL9hxx.6pH.oF93bAHMwwIbhce83rYvDZ4KPfK",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178623022": {
    _unsafe__answer:
      "smart,bright,clever,discerning,intelligent,keen,knowing,knowledgeable,perceptive,\nsagacious, sharp, shrewd, wise",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$kpYwv2FhZderKhDCdMyr/OHjKwm4Qc62eQxmEXaimuDxEuPdno/2O",
        "$2a$04$sAsBKCsuCjL5fjpMSYLGbeCeQaoz2pU.a8KSVNPX6BwwVSDfzKHw2",
        "$2a$04$1H3pEYd2nLmg.DdSuErNJ.Tbp1vhNdERGCbNWueKrolo8b3I.nac.",
        "$2a$04$Zu4aiSKTd4lsFxFwHtGH7eVA8FdJNTQJ0nbUNJNVQwdw/7TvFvPSe",
        "$2a$04$KtDvFokUwm0Ep3BeZ/I6U.deQFWSCDehM3cxDIwbo8V.mtM2oq5Vm",
        "$2a$04$AQh0kPXiQ4Eln/bS3itEy.oAoQZvDAd9R5cAFiw4JXFgSM15FU0Wu",
        "$2a$04$UmohlU5rQk3AYAheV.Q.T.Ecj/CRyecaunbOwQ8Um8myPWsGb94Pm",
        "$2a$04$KCDp5chdZiJ6Dhx9rNYZu.LDK/gUqyawQ4wQS8OrqYPDv/1X1ym82",
        "$2a$04$uneS6kXSSTxhF.iN9PMOxO1wBNKPPLZSEgMpqremCWMv4h2GYDQu6",
        "$2a$04$b9l0DCdM58mOKG2GCQH.Se3TD2vnBIQs5wL8WpNBgWoRvk0lWe.7i",
        "$2a$04$O52W8Iu84MiITfBBlVhSLed728bcJWja4z2uycTkVE3Duk8vj8fvC",
        "$2a$04$zwOkGzaTQ8YTQ0nEv5SZCOM6y4qPki7uZsQkmS9MQuB3TPuO0q3i.",
        "$2a$04$FEuNv1ohHciMmCo6yIBzHe.nvgG0T4bSjXR2qR8.iftHKJtD4vKFi",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178623033": {
    _unsafe__answer:
      "unclear,confusing,doubtful,hazy,indefinite,indistinct,obscure,questionable,uncertain,vague",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$qbX./r.xpfMed0fQ8hn9Ru1vFesQavXi/LrAJNJL/RoAAD8OGTxzu",
        "$2a$04$HWHBHkG3hsu94q.uX5p1Du6nSeYgN.xKN5yofwrgYtuXih703wra6",
        "$2a$04$Sw35PrBTVWFjnPfpYA72/.Qvi4pCJ9oeeCUD9pS5ER.wgN33Ulbuq",
        "$2a$04$1nio8fkzNujW7ICGV8YE4.FrCICKQYKfTRhioaThOfgZiC55EuAI6",
        "$2a$04$aI2ZGgPh857aKr0TXiN68OT0tB247phT5OqWvNJfxB9cQe0f7yQFC",
        "$2a$04$3deTFeIP3fskLEhQmFzk.OuaQeiXclBVXQUIFyCSlMcjbodVcb.pu",
        "$2a$04$wQ6E4.ZUA54XYodvRxCWrubSg3PPq.3GihK8MAmB4ITco0Qu8kZiu",
        "$2a$04$ezkrt2zKwZhAOJv7sqM6l.PoFFk1mFMYGC8y4vYljf1WsGUKLbuwO",
        "$2a$04$Gtp7eas5VVB8OapmalS5yutVj0RllqJyxP2QCR6gJCxY3rcTeKfkm",
        "$2a$04$Ih5ZJYAsuF5n8zDMrwjRSun0wear5WON/QuZJuf8HMGiQf2LIj5S2",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178623043": {
    _unsafe__answer:
      "renowned,august,celebrated,distinguished,esteemed,famed,famous,honorable,\nillustrious, outstanding",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$8/5zlehf808JrRfxWjPqGO/NzcuR8a/mImpNQ7A1yFC9ATclLI786",
        "$2a$04$2PTDTpDIRv5bZ94/smpDbeWHmuzapqdHlf5pXw12WJzKjh7NUGNcO",
        "$2a$04$TtAF811Ta6XOAdyO5lH.9.SeLT03WTUH6wuzNKUb4pZJDu1.HpLfi",
        "$2a$04$eMO7rJCKfj.MeKR3.wVTNeiuGdPNkAI4KExh8Bv5zaMstLWXXkDRe",
        "$2a$04$PeTopXrPCmbntpZKkzmbkePemTuUJ6HToAmGjhuO40uAGNmy1Sh9u",
        "$2a$04$eqX56wMUxHDTWow.xFBjKeC5jKfvXWPBsO8o541mgsr72zCuZ3Tfq",
        "$2a$04$7ETLjPUDUQvdcTwhXIQLQuetlPN5ObFi5XttfZlqwUMKTfh65k3Te",
        "$2a$04$tljBTTGz1q64AaSPIrSWn.Hinl4HYVRDSOe4aowmAu14tw1v.2Ola",
        "$2a$04$AEWtjqc79GbzeVCIAMavEuPjY8MWyn1cZ9tXUJ.PBok.U4CXK9shO",
        "$2a$04$tXDhMRNKY6t9rOxl8eWqNOcvm5jD06bjKbfnxHMLW2.TP2EidoPH.",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178623047": {
    _unsafe__answer: "chastise,berate,criticize,rebuke,reprimand,reprove,scold",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$0Wi0EqrWBgUR/U2mrAqFI.JrPl9Y78swBS//iGK7tNaBiE5IOyuuy",
        "$2a$04$utt7EY4JlK7/pBv3y6.JD.Kq.00cmw/6IPKsKIli2RFFr4g6HeWTS",
        "$2a$04$IKXxbuyNqpqUHAkAueaxA.hmtHiwGMWYiChJ.wGsgKVv0.QaAB9FS",
        "$2a$04$K2QTRYRk8ar414msyBqvheCz5/5qs9RbxAIxYSs/XjhOvChboWfei",
        "$2a$04$gShlrcCXG78RUFFz.lNqteyo/9Bfi1PIHWb/E40vjHzlIExefePM6",
        "$2a$04$DW3okA/ZKDdsGi/4QsYDM.b/stTuak8YdZZPNyUTnMhVQmBWj2f9S",
        "$2a$04$FduGgck4JqHzA0.vxd7o6.O06.jPToEwvk9UWr3nyL9HKYm3LDraO",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178623049": {
    _unsafe__answer: "bleak,stern,severe,somber",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$z3.CgUyfb4TyalQgM7jOEOeVMQcQeVMAuQdMj2KKgvuoxBgSWSpoS",
        "$2a$04$1Pvobvg7zmKnJ0izrXmEIufij8eUP4cM5fslwQQ92BMaeixoPCMU.",
        "$2a$04$HkOUOnBn8yMIfIpHJO8mPOSnnVT6ef3IWwFZ3pZnBj4NpmX8elrxK",
        "$2a$04$4PPoy2/A5W1qdRorkZgNJeqnTJGPI.1H.HzhFj/O/p56wU3xEsWQm",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178623050": {
    _unsafe__answer:
      "bright,brilliant,dazzling,glorious,glowing,magnificent,radiant,shining,splendid",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$FB/Bzn9D61t77Wb2qyJMsO4h3VNjHczaDpI5lMg8lkQ2Z9oqxOPMC",
        "$2a$04$.nCT/dO/68RMIFF36WnwwOuDLzuYV4Ep8qGmqH/wE1lqwpn.0wflC",
        "$2a$04$TsgJepa/sNmqO0iZo1G7.uNAMxUNrqyg98nKrCI6zKCtPeDZ8qkKy",
        "$2a$04$S2OAu8fuG/OmRUI79CYRMuNiK0bZjoq2Y/YBoT5yR633/BV2ROhum",
        "$2a$04$xb0ZzMw5NWCTC33hxKWF4.eWd18zF.uI4vg8NoDLmFx/QKWl9Mnzm",
        "$2a$04$xipAMeklzIYN./KGjSkY..RjoU5xx5LjRvpBj..WbTsbc4Nq.dE0G",
        "$2a$04$SAnvo6eay73jLkLa5t/3FerwY56QtnIUUIWCDeMLV/jas1Na/NaNO",
        "$2a$04$zuV8L785Eiq6M1yldxfvYeEyTrH.iISUmm3FJwNaivUJjeQDMs9Yq",
        "$2a$04$7Qmf3VjDWI5cob.Cj8KDd.7Kk94sXIl5rE7tdQc9sDQAnHmDsSxce",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178623052": {
    _unsafe__answer: "deny,confute,contradict,disprove,negate,rebut",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$wMa8oSvF4hUNOMZVgc/OAep94cgirsBb7TN0IX3T2aHYAaMErgEKK",
        "$2a$04$t6QPmx2HSlDDkZbnlQB24OSRUQ9kl0U/ki3g9FeyqVEh45SJ6fh6a",
        "$2a$04$Wy7xqLS.jwKff69OESmIU.auxEVTYa9axgGbauQnHasbxYcjEEC8u",
        "$2a$04$Ecfiuw0yjueVPjQm2g.vVuAw9aPpuutUAbLWorOYvpq7OyoiCM.ZS",
        "$2a$04$pkd8lqosxNlo0on10l2iueEP45kWUE2K3EoKGl/TGYs8jB7Paqz22",
        "$2a$04$bur2UUg3ac4CXKs4HhC78etDicBS8D2KJP89qSQgCkvxoAOekkBOy",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178623053": {
    _unsafe__answer: "bother,beset,besiege,harass,pester,surround,trouble",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$95Sm2awim9aKit49U5Y1bOPjzsmuXsqcIA125bIBkAIS/XQz1TLY6",
        "$2a$04$K4bg4Z9myjus5odtUcioAuROXw1wTUkvviaFOUXdR1v4N9fabelsq",
        "$2a$04$tqs0Q2PLfSmjBnIBAq/OQ.T/OcwrTHoZT43uvUsuhe7eoarT38u3i",
        "$2a$04$MXY/2r08n7d3QFcfvGXywuRoemcDZiMoexhdkEK7NsthdNn8eVptm",
        "$2a$04$8Te3bSVTYkwc9Zc36lwxEOQNFetNgB/KanXnTdvrJl3bVdlFGt3Qu",
        "$2a$04$kYUtZoUt5lLclO10kbrsQuNSN2gLee693VP4ZGnLGeFsZAb2PGhWC",
        "$2a$04$WTg2S6zlAC0I66YVRKgTYu.gbC8e5RfCeviGrn2rfllKq87pvT6/S",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178623056": {
    _unsafe__answer:
      "generous,bountiful,charitable,giving,lavish,liberal,magnanimous,philanthropic",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$SSimxxgkHSmuasru9pZKGeUPIKklibnuIvmuFNdKkgx7kgFhSp8XS",
        "$2a$04$yOQjrw6i7M/KgnpxLDB6XOFdg50AuxV5b9kEOimfXMeemh1blvNDq",
        "$2a$04$qYJqT4kqS3rS2B1c0cNl5.FHW1yWycWVlHeoETV7lq6uCcPgfRt7C",
        "$2a$04$9FDnfaa6WjeiDra7KrWbPuQixtx.fZA5fJ.7UQR8HAiHw1XY2.0P6",
        "$2a$04$P9zr.uw85jAu51Uxzj894.oGUkHrlwSLV8R7yriP3LodjQrKwWEM6",
        "$2a$04$rsJPSQIPApSEw5ykEvI9yu/7SD8BMaoIV22l4VLof.GbfXhVv/tH.",
        "$2a$04$Fpo0LJDiUC8ark/C6S.hYOZYrATe7e.CE5JieZWrMffqrm4aps6Cm",
        "$2a$04$LLUu/Yak0aprcKOitIDY1u0uvtsIbjYIrM7zUjdN9znT8.Gakdr3q",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178623061": {
    _unsafe__answer: "brightness,brilliance,radiance",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$lHfSvbP3nGg/AUG.XVo3RudmLRpfhMPWtZhbKaCmeLNO8jXl3abfC",
        "$2a$04$23uD4dxB1N8aa8C9ANTpC.zUteBdYtzHsz7kP7qBfC556tSrpTm.C",
        "$2a$04$AxXaAORQWabk/FBDjB9bn.rIHuPejiS2DgNaLoxQWwvwS6..r5Kny",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178634773": {
    _unsafe__answer: "no",
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$t32eFFTy41AdXKy4q9fEbeQY52.6CUhiWFynGgupkfH.Y92DqcAui",
    },
  },
  "178634776": {
    _unsafe__answer: "down",
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$m5RvWLAjBcX552Bg2Dmlze4gRp.4PEl/sNne7thUXZIDm5PiKVddK",
    },
  },
  "178634779": {
    _unsafe__answer: "bad,evil,horrible",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$TpvfBx4Udzhh9vIWeOtszusCM958hNp.P4yq575AE/iOja/nkkKpy",
        "$2a$04$JVHVlN9aB.rYlwSn69Qr2.QqcOeSJOjGQptGEnelZUbSyFxcdSdca",
        "$2a$04$GGNyOPR0VfwvJvoqaNfFX.WjfGnLIKkP4qwTHAU1x.E5dcdqrO30a",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178634781": {
    _unsafe__answer: "stand,rise",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$NpG4CIaGYerO3JVYyUvL8un1oDPdyhITxsJ/mkqwqja7E6ZaHCm8q",
        "$2a$04$j5rKnyj6xwbRfxCMBaxaX.8jWclOnjbBGOr8Hj/rJMtGSp4y.17qa",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178634782": {
    _unsafe__answer: "backward,back,bashful,halt,impede,reverse,shy,stop,timid",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$WzYnFXxQfBo7FpIhHsCyDuLwZF.NVQSOirAhpG3yrTk8XwZFQIseu",
        "$2a$04$.UQpst4LDZ.b5W9y4ykwwO9ACY0yLxkY1a.0TJ7GAgKKZAJKCYCH.",
        "$2a$04$Im2JS.OH/4iEfjYFjAOCiOe7kRW0CK7hjjrz6GTYkeQtNr2mt8ryK",
        "$2a$04$5gp8KBQyX2NKuBjqsSY.IO8Krsrr3winLBOh3rRB8.3kAFAyTwnpO",
        "$2a$04$4kz0XDzqj1uJmM93oKKVuO7Ifzo5w/ZelXypiMtzFCzDzS62DHdPC",
        "$2a$04$1OLnU6yNSn6EOKlgZTikB.JIpPboiKJsnaKFtEYWfApdEq1C6UbpW",
        "$2a$04$fjK6RJfHzIyOiHwECjjCHOplEGGBUHS/Iq2VXiSj8v/WkDc1pGLwW",
        "$2a$04$fNzGrZUWuIo9U4YarhuVDe1LFtiGrYGcFtN1rfhh0QhaDJICIfnDq",
        "$2a$04$SaoP07Kpwk3nDbjb2G.nSujPTNJngpRCwcBL3/TCh1FMVN8xBOGay",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178634785": {
    _unsafe__answer: "young,contemporary,current,modern,new,recent",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$W4VAQa29mUcuAcpoPkR2IeEx0EJhWXNHKe8torROj7bl4n42OKOpS",
        "$2a$04$ctdM1jqMkvrIKMG1GSlOhOsKg71rOKiKCx1HL9uEwpmYelF77qu92",
        "$2a$04$HCgsZJhFyZk8ttUExRqUgufNfaOuaoQFsufopzJrusmpiEuATcq2.",
        "$2a$04$ff2LtYCDS5nw.Ddv/JxJse9hNHD29cLpLYIzYyTMd7kRfI2hpSlTK",
        "$2a$04$YqLZbGUGOgFNUtMWROH1d.Y8s2O2GhOebtDUsnNUcLKMOfUux0ISW",
        "$2a$04$5lAyR/T8lHnl6bWbQc4FD.sZ3e5R8xItLP7z.7Q3ugeZq5AhFbNUu",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178634786": {
    _unsafe__answer: "small,little,miniscule,minute,tiny",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$C.W8IIyohNS2KjF3qjhjEObXyBEv7z6y300ezaKgqkcNdAxbbthvC",
        "$2a$04$4SZiYcQeFuBnIWYT0gpVuucdw0lhSEPhhaexmlM78/M/lPD5xDF2S",
        "$2a$04$J2z/AFzg8meD7b7nfQ6gnOUKwA6BaKANUuVOPCd5hErnUHysSHD2m",
        "$2a$04$Dcv.GUys8mUObiuVVAx4VeBAgMFWBwO7JC0vuAHeiXUx6WfWMOO4S",
        "$2a$04$2Q0U8.mvwrblGLjo4sy1E.V5KRCNCAnJom/C460G0iDh4nIxRVFEC",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178634787": {
    _unsafe__answer: "sister, sis",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$XtSQ/S/Wyj1.nrOvy/pDXOmQuh6ddWi8BWfq4G3GmSLLI3b0YRHvi",
        "$2a$04$RhDoXHohc0G298pr9ni8XOToiXzWS7Wd1lR9sNOBI1TN0nC.YR4oa",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178634790": {
    _unsafe__answer: "cold,cool,freezing",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$Vg0EsOTPlaKx5H/Ar/Ia7.PzoSyIHr1H5hqOZ7pwGBKKtVxFmTE5C",
        "$2a$04$.peQXwbHcZaMAZxBBieHje19dCQPoFlltsfQvFoauoq.3UF.piK9O",
        "$2a$04$ReNkEfQrWzGORtKcBDrc3eapOdTBPRz2uQgGWaFFM7pAGifxxnlPO",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178634791": {
    _unsafe__answer: "girl",
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$0Lj9JfDnqLg1AvvO8Q1lk.LHOpQ2/IzEJpPSfrSnMFf3kHg52b976",
    },
  },
  "178634792": {
    _unsafe__answer:
      "minus,decrease,deduct,diminish,lessen,reduce, remove, subtract, take",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$mBDzALBqjs69XgQxmadZJ.HuEhswNLjc3bJ27ZUN1spED2./WRsRa",
        "$2a$04$bu/D1HWXIohKBmz7VZH7U.zScaqlOXNl0GMTlm72gfqKeDvSdBNBi",
        "$2a$04$KoVo4FTbXenNbbNLdX3PAuLeDO7JoS0JzhgqetPIY5hBlZXv0Wpqi",
        "$2a$04$nbyRBGZwDXokjC.V26YL9uR3wN2UTjSnh8AND9WMjfPFrOlEyQScS",
        "$2a$04$pQpRYhKjUBSUkVUMkY6PqunG/17d4j/UFCvxv4Mmi3zj.fYB1gZki",
        "$2a$04$nu7vEM9fJ458hSaqwZPcsOdEa.wydVLprqRHOd3KdO68SeNnqxH7K",
        "$2a$04$i3qcouEwh.YaiDQVLhMjxOgcmCsSVKsAjQVfJ9ZZno0rF2RfNDhNq",
        "$2a$04$WcQxQIw.M9kT..kUE23YNeaAstXInNI5SO0nDo.aByLOcPCgLOCPa",
        "$2a$04$yFhoAz3/jRH17u9tHp2x4.cu7lqtKjmxp.z2i.xOIQp5TxsJ0rxGW",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178634794": {
    _unsafe__answer: "answer,accept,reply,respond,response,statement",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$37//dsrOf5c/wRSrDov4vurl09k9I.Os8icG7TqDPjAi4ujT1PQZW",
        "$2a$04$Z7hktgwMsEWbylCKmNxkHuxwhx7h5GI0TYMiJtqPsU1p9VMSOK11q",
        "$2a$04$bBWSvGd..ll8NboOoJ.G/upP6rfGG/cBsiT1D86ed0vPVVm1wHtby",
        "$2a$04$GRigSNNVICNik/FrS1XVgutLC3ndR8ciOma5KIKW1EQ7owTIUglrC",
        "$2a$04$RDBLUmHIyf9FWuXwaWVcyex0OTSSQ5/aEvYFJkW.5znjpzPSaKwNy",
        "$2a$04$5zKLd9jY/tQaw9l1vTjt7.dEGbDUQBxS5GGdkXc5bVamEDpJk5VPC",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178634795": {
    _unsafe__answer:
      "rough,abrasive,aggravate,bumpy,coarse,irritate,rugged,uneven,wrinkled",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$fC36JwbStFdAMnUq2sVDbe5M0pzaB1QpAkC0UHxHruP2GqB1X9A6y",
        "$2a$04$h4RFmSmgpKM8H/zhJ8049.O52Q6w1n75G3zCq738JEdUSWckdCXYm",
        "$2a$04$CWr4wWorV5y5LbgyTQKSbOjVf0YbrX/76lEtcvWMLsMiVx7..xJ5e",
        "$2a$04$qnfMEV/438xst.dhj9VZ5esMOxaZo0VGJmOK0UJZvmKVGZrzsePDK",
        "$2a$04$/HtGBlBcgqbPOdnriEDu4.M3TPpFIKSUGJbAvIFeiGD4rIeNOIxnu",
        "$2a$04$d7ZOLRvYTAjrnJ1acLzamO6/P64SLv28ZJkisEC78JRLhIcyjXTQ.",
        "$2a$04$m/yt2k6R8.te2aDVPR4J6elXl.RK8kBipJA8mKy.wQ7KfQf79LgEC",
        "$2a$04$hxAbXdMWf6OC2aX6i9WK9urGevgRtFLgfmNIwQnZ4YQyfaS6r5NY2",
        "$2a$04$M.0U8dNZ3imRwkojs7n5Oe5FuB2re4/DshJ0BWdEQAmIXi3LI4RGi",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "*********": {
    _unsafe__answer:
      "foe,adversary,antagonist,enemy,nemesis,opponent,rival,stranger",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$nKWh8n4qwTeUjTxHLG32bueKtAG89ZydPCt97O4T6AkTFU2sehWI.",
        "$2a$04$7zpo2ijXempirN2AC6uno.Su.JYR50FBuSQ5p7Svk6vqCYfp1.KcG",
        "$2a$04$2pxfqJa0ACNHJrgzfXsui.dyctP0OHYb8qRgWXvmgIRtF7NFbBxNe",
        "$2a$04$zHuVGRIYsQg3KBI0ieA.WOYeLBCro7RHcW4KQLGIAdEN9c7.vKLwy",
        "$2a$04$XcOkA6JCp4YOnajx1fBlrOVfzdK2uYnR/WZSAVJicp/z4hheINRmm",
        "$2a$04$w7FOC1P/47eziv4i7LIaFeb2UzNGfP3IGbpXcJ0QR0gfksCLkan0y",
        "$2a$04$frNqipJJQKXVCPbiSI3cM.QZJA43eFu/gebAFpJzteEw8I6ZO9q/i",
        "$2a$04$tyMjyY54e3/ufdnhfnOoK.J9G.RZ529DdqT2tBvTjdKPEMtJG3L82",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "*********": {
    _unsafe__answer: "blunt,bland,dull,dumb,flat,slow,stupid,unintelligent",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$AROC4ojBm72iPRUv4v8XGeXy/kLQ4fVo9du.9.XfOdg8dBfmM2nVW",
        "$2a$04$6tCA2d7YUzEG0CcJx1H6JOq6x6Q0pp4YCjzuelAKFmXJAXRWPbucm",
        "$2a$04$ZLmwTPGhhgGd.dauxtSpk.cQI83vex0JMTy8HZy3OV25czxf5Z7fy",
        "$2a$04$Ld9SVXVtU/LkMXrrvzpfuuIZI3Gi.QtAKmREsviowUHrnCfVpbwBm",
        "$2a$04$BCEuCsa4dg9eW9FVfibDTOevBY33Xtd9YegAT8OOMqSJ.GRCGJEGq",
        "$2a$04$MMk.uDikxvWYcBuww.f7dua3zkzlQwpsmxm/KnYVZYT3egul95Dgu",
        "$2a$04$.wWPwfG9HyJNj9SLuobAruuruP9sSucqo2Opl5sXcJPHgsA9B1yJG",
        "$2a$04$M3Iq5s6hiO3EnSWgaUxpk.N6cf2djamJgH9lBMkQkCUjjSg7qsfjq",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178634799": {
    _unsafe__answer: "laugh,smile,whisper",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$iKbTQlV61wGwWkySlZWwdu2ayT59xMhQEgFVA15g6VaEq6tY03UAa",
        "$2a$04$NdzC0lajMmwpEM7qlFGYFOKGbUYrv/3MokGsWle3E4y49/BzIXME.",
        "$2a$04$W7x0HAp1QgqnEeCSmA.lCO3elb1QJ3CcuKIYkV0Lsq4lV4FUkiGb6",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "*********": {
    _unsafe__answer: "wide,broad,fat,liberal,open,thick,tolerant",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$oZhMwlt302EFGVecn24zPewT7hcp.4fh1owAfCEouQTTWuo2jX7i2",
        "$2a$04$bRvNeXK329CmBJYQ3Y2VU.3L5rXrVu./vj4fS9RTfNNh3xJxLs1OS",
        "$2a$04$/0ssy69Bmhpr0a1XY5Y8C.JduXZ/0AVb3NF1CY8N3bvySHNDTVpkC",
        "$2a$04$sVu.0lFBoF3AzSCdthl5jOHmG2zVUwuIktm5C2tkTsAeBftd7Oanu",
        "$2a$04$8oyu019qEQQdWyvfLK7A3OFadFTa.fxoWJussxzh7uYCurGAUtPhi",
        "$2a$04$cn/33IURlAdQUVjCUlHgZO.DhU8EcnzbbVGm1gVAWohdaihnbkOc.",
        "$2a$04$9jUo1xlPUgKWRdtyxN47Z.CQFAmcagHRvGKsUH0/jr3KfYPoHacwm",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "*********": {
    _unsafe__answer: "single,one,singular",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$OD8L4wSwHpgw3uJm/6gowuuxUtlyFpF4bUKR3uYdoczXrz.Wig04e",
        "$2a$04$Xuc7IaBjgDHHuCUglmJ6WepT4LlZj9gi7GmXevetcphhaRyF5PzPC",
        "$2a$04$znXknFRE6kKHcLuUsrtOcOfhlI4CtxAt9hWsjUdO9R6zC2PxZPriS",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178634802": {
    _unsafe__answer:
      "bold,audacious,confident,extroverted,forward,gregarious,obtrusive,outgoing,outspoken",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$CsuPRoEQQuHwEh89Qurvku8fEgIjQpu8ygICJYxOdj0hvUJaglKPW",
        "$2a$04$sDitQjgouZL7SHxlHb0ZQuQyOkr55vPP7uPPBGOKjDL.vUMk9QwBC",
        "$2a$04$CRs15KrlNwFdjS2h9UehY.DtdEUGpEcRuODu7eCd0E1kzVkB8/rv6",
        "$2a$04$BZE6DSTv66OuDnMFxX334uX1d6lq24ip1PB6yTclv86JbotVSDj3G",
        "$2a$04$Zthys9eSMh9nOUruQZ1fs.4FRyHeg1iBkQLX7M6ashGIbDa0NF7Vy",
        "$2a$04$kWzXrwktgAbS/Xg4jXVOv.SuzMaoFx6imnr2kWCO.4sv5sHZn1Q.q",
        "$2a$04$vc6HRPfIhOj05vlDLT9z5uuht9Pq9U8JAq7H6CRGIlSWOdQfXsyz2",
        "$2a$04$uBpp1w8pDPN1cwLTYBi6QeTcXZ4J8k1SOMQUnr5iYjw38VuYSsZry",
        "$2a$04$80so909yWJbqGlIu20ZLLO7nFa9hZlSSAwjik9SKUymuFSskeyjRq",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178634803": {
    _unsafe__answer: "masculine,manly,unwomanly,virile",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$JUHjhktKncYrtOmVCgOhUe22mI8hODXDIEvCBAInaYfI5gjPjBzTe",
        "$2a$04$MHo.kYSpk2rrKUVgCUE67uM15b8Ig4Z0xRbUJVj8YiWNcxOgx/UqG",
        "$2a$04$cIeWmPWj5wTWb2tngfOMHuSf0om515bS4KjGaVrrlJdUFxxPsZO4q",
        "$2a$04$5.Uoo0kE80NGE1zkHezKKeVT.eJoFNZc9nJO72AET.lNVsREInzmi",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178634804": {
    _unsafe__answer:
      "active,diligent,energetic,hard-working,industrious,lively",
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$dgkd/zPGIN/vQMddKux7wuWK6v4QgCW5uisbZmKd6Ixji37kiwJsK",
    },
  },
  "178634805": {
    _unsafe__answer:
      "specific,definite,exact,individual,particular,precise,rare,singular,uncommon,unique,unusual",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$4Rt.Huq7/qBeN5K.asIbKOzrCZU9r.Co5JXf7lnBbnVyyJY8I9EDa",
        "$2a$04$Sj6NyTdNyw77hvEQ4SUgQ.B9/BUc.v0pqCeXgfhMqe/YyGb.Jyl1m",
        "$2a$04$ie8oK8BEq1rf1eTSNGU1HufHjPwja9ceBbceyalcT4Aok0oGEk/uq",
        "$2a$04$FdN82FG2O0.6a9TRPu8N5.aD3fbFyBK8ft48NmZyXZSi7qWRD.S3u",
        "$2a$04$ZmhCsJTRt3OerDA.Q9qocuL5QaXWK8tCtBRBTiGOWd6iKHNjqArC6",
        "$2a$04$M4scH2yyUptLtlM68d589eRwSIBzzVcaUmD267muGf4Lr9zeHFh3W",
        "$2a$04$RR4cqkersKnMLbdbC7gnmOQhWVd9t2S6vFa8vc4120ssYv5cNZlY2",
        "$2a$04$YW4GLcM.3BNt.pyxGpfYouJ/cGms0/TqEZ9JfzGkKRkxdhss0GZou",
        "$2a$04$kh1KgFHryExPn2H2ce8ro.jMfKwRlfIxHWHV9deJwdEezea/lE4jG",
        "$2a$04$ErKX7Kp1TpIsEsA156xBEOkkLXcgNuu6WleMnUAMf85ATCwxtU4HC",
        "$2a$04$Rh0AvLN810zE84HweAJXFuiK0ORIvNKI1vjiw3jIj6RrZEkt9PBOa",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178634806": {
    _unsafe__answer:
      "lose,decrease,deplete,diminish,disperse,dispose, dissipate, distribute, dwindle, lessen, purge, rid, scatter, spend, squander, subtract, waste",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$rHVZfkiKDmmiBCu8K2w5E.MEva114ROKjvnu2BQfDcuL9Gpn0fWSK",
        "$2a$04$Vw/baE./xp5Be9oXqemsB..5srcrSZAHBbRML3lSJB9Y3cySaO3E2",
        "$2a$04$PYRGfQUQdWomGMue6fPFDuF8nyeDaHnSU1pr8P5XMlIQCJrJ9Rd1.",
        "$2a$04$32mXVNsYAuZoDk6UN3OpU.cBmk1nYu5pRVZjVeaigEWTbgCtXe.HO",
        "$2a$04$jdCQ7ecPBsho7AYoDoW82eqabJlA57DtjHdCJ7Bpbf4o/gC4566fy",
        "$2a$04$Fdj6vdJPmyc3SVXGhTiu8.2IMM1Bq7TxnZ9Aj/Sn2wXD137MaTJnC",
        "$2a$04$k6i8NQ1nwidLg2Q9./U0u.lSRPQeswJpk6F/0glMmO8b409IgfGIK",
        "$2a$04$H2ePPuyq/D4Lgmc2hCktLO1oYXH09S.0aBO4mwWDcJm0XRCSXKHBq",
        "$2a$04$lhrN0EXOyLjhKTiasQ7os.9jPWYHYcRbgP77IlmwVQ/ovG0Q/eL96",
        "$2a$04$TYMehugTuah5/xC3gvM60eyY0wMu3ZxUshIZAgQlYppULIPMyTf2G",
        "$2a$04$LgcOpj8Ek9kBMI1iYvc84utjtYuUXnDP7h7TYw8hIedGcM3.iMVuW",
        "$2a$04$r98WUYYfacscq6KFksLQ3usRhHjGxQpvQq/D3WLhMNZosTnY7KMxG",
        "$2a$04$SMZbNxoMpPwH8lLNPEYnV.urv8vquTIcTOf6OAez151.ysqImiLrq",
        "$2a$04$PgEynyT/Y1G.uKSmmaT5puB8jsD5998ntvnRC1xsKzQmPpZ09tPgG",
        "$2a$04$gzw5UTqqo7mDljZxSZBr6uiWxSPF9r7g5vfomXoBLtjgIdcUAMoHG",
        "$2a$04$5WwYUo33MWl5uK.qvj9mIufzAWPg6aV47Yca3ckyUJJODWCrb5kmC",
        "$2a$04$IXkPlEVEvv49kzZVK0TFG./ycJLWmb3ti8cp6TTfdhsm2HtEAKLdW",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178634807": {
    _unsafe__answer:
      "flexible,bending,elastic,indulgent,lenient,limber,pliable,soft,tolerant,yielding",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$nh6msoZImaMg4cf0nlzaT.hh4NM0pukbDVdItUnYpe3UrQcp16H9K",
        "$2a$04$cOdSDxcw/MRRgBZkBGDE9.xCl4dhFVxKA4Q1zwme/N5tt9Z1KxUxG",
        "$2a$04$RuW5jfRi6hLvr5T.qy2DL.mj15eEWgp/wNuw5.KDD259H2SsT3rsW",
        "$2a$04$9IPEcycuUlOCpezuvTmkw.LsdPgDjdsG5/WukDb2vhlJgB7jOLqg2",
        "$2a$04$aCRY02OiqpoOv6abI7dch.L0k9HU6CW9TdYt0GFuyHjbSxy8tNHQO",
        "$2a$04$7asrAn7bSJsciH4IKWg/buMOYbRglkYReNbxXLMdk7qO.PadGiOHO",
        "$2a$04$wCRIrD2OR8gRPHKqbbJPH.SltSl4UR7DobfsoK97BIVrJOcUCUu3y",
        "$2a$04$eBBguNG08mc7unp/xXx1geMQWEPNErOBRo4bTcLkr1Ea43eqDbIFm",
        "$2a$04$aCZcAAnh6yKJ09aDQGUiYubaAXif6C.xyddjle9Sdlm0txzPvTZve",
        "$2a$04$yn23RqTxH3KrewjFnkP88uncJnDeAg.rfIN56XXMeyvcHYIWmVGW2",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178634808": {
    _unsafe__answer: "first,earliest,former,preceding,previous,prior",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$DiTQLqR1FPYc2tIZwbNhWOtgjJ.9RP43bQydBwlTrdtjL6Y8OAvqG",
        "$2a$04$/2/q5AC4z2hOnD65aaaCzunPaHIGvmT.AKYlwHTS5uih2a2MVDHxG",
        "$2a$04$HB9HSEfWSsmX8cFQs9iZzOSbVm546KFHv7wjjJKKl3r970xiQ8N3q",
        "$2a$04$QCRaeShH0s5/*****************************************",
        "$2a$04$6v9U4E0a/12QW.c3rK6epujcl0/iF8i9aOiGA3eXzRVOt2.4DZ/X6",
        "$2a$04$IdPsaSPL63UwHwVQKWH/YOJMmtPgSlSVXsAoRvT.Ef1wLoeWxDjaG",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178634809": {
    _unsafe__answer:
      "agitate,aggravate,anger,annoy,antagonize,arouse,disturb,enrage,excite,incite,irritate,provoke, rile, upset",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$X8JdXz7BibgievPHsQLbsO32sJlUfzad339UCbwv1dX3Wb8sE4Fl6",
        "$2a$04$Ue2o6ZuKqsFLesSVAbk6Eenqe8Ixh21DXFPE5mZH.BH0eObf6SNqu",
        "$2a$04$9fISa3PlrOzGSoWcrdcbO.8VR0vKDWR8EWVlT10IsN6lwedQ41Ijm",
        "$2a$04$nC7rTKlkJCySBMzyEcc/SeFBx4BMlE/ZjsnGbIuqcp4eyTN/70lxC",
        "$2a$04$jr2VpBIgyNvzNUDjKQmKOedEZUwWGnh9htkUZUMn/x.avyiC7IU5.",
        "$2a$04$9MkzTD.mJOwCvZnxEx7PI.9TgfD1dRXBgpltGggzI9NWzorHRiAEG",
        "$2a$04$HmuxGdhWCE1SOosBwcoOB.4IABPAP1JhyYaH7qlZJeq0yUx0dpGX2",
        "$2a$04$aDxVTbShxnehSLoCzgCdguqIsSmfAzl6KRqWEIQzZ7hDXta8DvYYq",
        "$2a$04$OHPZJ3nrMsQVIOF63pHCiuf5rkqsWlwjn7SKGx8naCT3eVfIZ0mlC",
        "$2a$04$vffGcTkoCVlpgBNFWARAf.WhOzJYoS/TXu2R.3Ee7vBAaNVkIuuni",
        "$2a$04$CvcwScSOYnFRMUIBh6JbWOZaVw46ZCbrCGgrZayDuOGEdbzB7kf6e",
        "$2a$04$mHL3V88of3dQMcOsym78quV24wCd97hgBFijiRfTsbJcZRbwK2NM2",
        "$2a$04$C430uUIhlRH6fQeWI4bzhewBd2VHKVtLgR6RY8CG3oKnopGaj1wiO",
        "$2a$04$WdGH3Ws2bVdpqjuVnyWNi.POfTItOKwQ15m0XVy8aalFniWV02QPa",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178634810": {
    _unsafe__answer:
      "logical,believable,plausible,rational,realistic, reasonable, sane, sensible",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$W2FjskukUQclLLzMGkG0x.b8L2.zJ7735qfkNPjJmLeHP7vW4DdhK",
        "$2a$04$CrqV5K1i/mfkVkDNIsEkIeXGDMsvn87uk64Nqd4UCfh5q9wNo2Uza",
        "$2a$04$hmh6KrAi3fLMLx10Uo31M.KpSYYDJpc682BE41zVyAIoThsRiub5O",
        "$2a$04$.sFX8/qrGIJ44lm2ZIZhwOihEAapHnZPPcqtMsMi3.jU8xO1shh/.",
        "$2a$04$7QXYtmp8Z5EEPOPtHgKrQ.iv2RdfbCsYHO1X88iU3rDXnG34g36XW",
        "$2a$04$PfYYGwYKRAEJtwGVnUzYMOXYL7qKfrRjeapPS2xGPogphmIR5Q8Xy",
        "$2a$04$dlUNAp.BES1qRISQAUsHvOXutEjDYGO8Z9V6kDdsGaXgMKeStkHAu",
        "$2a$04$Epm8YOioSnu3i1ZhDDkJkug0Le7d0WWb0SwuyndtHlzjp/HLNYzbW",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178634811": {
    _unsafe__answer:
      "outgoing,aggressive,bold,brash,brassy,brazen,flamboyant,forward,gregarious,showy",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$fGlC/bMftops1D5k1rY8B.DGfA0RdAxqfD4VYbe3I2kikUpUsqhD6",
        "$2a$04$PenWliPJvAleS7Fk2/w68ursaQmz/KpGNMeyU2lCrzdxOJwoTb8w2",
        "$2a$04$cPpmJK/kE/BV9jRG1x2wSuWsEc23G3MfCwH1iW97vlbVXQuU5VPLy",
        "$2a$04$9H/gRRXZxE1CLIc5H21AzONMndr6uYGT4ExzgihhP1y9ROmINnuFK",
        "$2a$04$L63gMc2DUWMCxskSCGDNNOTbGtkMvSn7drPoATNzHCYhfH4xiOaom",
        "$2a$04$90VHD6SduG2NLxlYWTT39uJIAvbjC0HF4zgTJ9pX/WrW3HIoxZxzi",
        "$2a$04$k6rI/ys3BYHScNphcRN8UuDJtfIOT/Cj4DxRYZunaMPARPHSwHwMq",
        "$2a$04$G9eLe9DKBx5lL7ExZI.kQecubx00JELChe6cY9n4ACa.rbCv3ySK6",
        "$2a$04$HJ1jIsdBOa0ymxj//ZFfQe5QBUmvvaIJ1bHNotu7yFeJay0IsxzvC",
        "$2a$04$0Y8/XMAqg8emfCURW30yLOE.3jZwIHtWJX27d.zO/JmBCMHoHlDSe",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178634812": {
    _unsafe__answer: "weak,lax,slack,yielding",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$CwhFlXoImFvBrf6lYA4SFeLplHBducNxnW3N3xMGdb8glmEI7hKTa",
        "$2a$04$.LUVrwfQQ6wywG/z2AFMrusj5yrxS0cSsk7JdyVhiEXpoKoO3/SZu",
        "$2a$04$7euVXTV2i6RLYyV8VlvOO.rquEkQzRpWQtbXKZzNnMuDir96sTM5W",
        "$2a$04$MKi9/ZnMPNYnnWN1rNRMOO/oGIlY9MI7bAVK8W2dZZVIjK57SACfO",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178634813": {
    _unsafe__answer:
      "elegant,adroit,polished,refined,sophisticated,tactful,tasteful,urbane",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$CWiFDtuKNV.zFW8tvPNgleNfEsJTyBHVCwOBvmqssA8lrvSjqUdAW",
        "$2a$04$VZBHRpEielIXawcuqB1AO.CUDkTZ8Ftk.rpDRhg7loyPbHwyyt5P.",
        "$2a$04$KgJTdwEdC3XIY9NS/lgwXOYytQ0PbocYD9o06vsOPSs6q/GCV9D.a",
        "$2a$04$7JSvai/QcwAHflK2.KDJjeyxVZ4PUKSfwRFcejNGDSEn4S3o/fwuy",
        "$2a$04$XudZbM6wa2v7dtRP3RQw9eeJ9XSggJQzrdVmSHum0OJQT7goG88yu",
        "$2a$04$5QZVLEjYyDCWb024H3WqXOocMI41ZVabI97bNfJUMWH735NrxKPOm",
        "$2a$04$PMcvd9yGQiPImP1hyJGrqebUYaAPrl.bKMxr9RiTjGlziyxxFApdC",
        "$2a$04$MqpN8CZpoXAresCeFFC/WuyByjK1UtrU.1LUCngNdkVogv4D4IgxS",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178634815": {
    _unsafe__answer:
      "yielding,amenable,gentle,submissive,submitting, susceptible, sympathetic",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$9Mq8x8TakZAvOAUwFAs40ej7p3SKt7uxTyBm0405icNuQsfp/.MBe",
        "$2a$04$/R5i.8.EQBE44cey/ox8/elXqKYWME57sEhVnLuZgd6GedtM07.k6",
        "$2a$04$iEI5PSq/rcUolcsfppfumeivs/OLfcQq2bQoeqWxLRe1tmiE.tLTO",
        "$2a$04$Lsza8adu2PIQxQlnSYhCw.X9VwWSgbzdwNR6rtcB4k0Y7TgeMnqZG",
        "$2a$04$hn2pFBLOaznKHshLVZtVTedPIE4LqQulRLELzLi6LvXYrMYkywwKa",
        "$2a$04$zGFUVLij.ZVbueFgiZBS/uApEl3RlGsBWuJa7T1PY3x0SMoqIYXta",
        "$2a$04$65udgZKgkf95qOl1TaWAAuxaxfnc9sMP5Wv1CPzqTq48tclIntpVu",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178634816": {
    _unsafe__answer: "pessimistic,depressed,sad,unhappy",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$DQ1.A/TDl7GuvXbnRVO9Denuiz.GY7xtsJbfY9Iam/YpkJZ9lNlEG",
        "$2a$04$hKMv25.kQQrabHhLdeUzfe76PC9tVatt1D3zKe9xOLXWr2xuFXV/y",
        "$2a$04$8rtTxUjL1oh0ahVeH2rf9.il.M2Pk.2G6BuzQvsKk7EPMo.FFEQym",
        "$2a$04$isVjW66eWsFkRflaHezxMOl5TjQs1ZL7gwPNHnpuNM57GzgfJZ/c6",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178634817": {
    _unsafe__answer: "serious,dull,humorless",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$Wlr2Zb/fXeqlgDIjUvPjN.146x9gHthUk7eAU0w9e3UiSIHH7jKU.",
        "$2a$04$WPUPwvcX8tp1jxl.05hxQORYeb/9vgh9ZZNgN/06.1C6lqT57afS6",
        "$2a$04$V.FrwmYsdJX6mf/2RYHsEuSbiOwqRhS87hsxPO3RX7OX5GbFnQPF2",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178642178": {
    _unsafe__answer: "exacerbate,accuse,aggravate,blame,condemn",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$5510s4Ov77ub/Iu5PLIzHOg4FKjq6TJbE2YcKr1I1oO634R5aPAeS",
        "$2a$04$kS9VFMzf/wgUiypLY7lo3OCjm3DdYv1ELzRz5BfbmcBsE8696o.zG",
        "$2a$04$rBUHArWtBoEz3E38n/733.gCEtAN0UVAKdPMv0ioNfP0uO.K6a7nG",
        "$2a$04$NksdXCmOEs9k9sXnhnIHfekDwDvIW2YjalVo17t.2BMOpFJRwGGQW",
        "$2a$04$/gsg1fsUMXxZ5.lgnTUbsOIB9cf4e1rqmzaRyuJ9oY0zF9wToH4e.",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178738937": {
    _unsafe__answer: "3,8,6",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$zqOp5c7.lRXnmul.5Fk3veFKvzRxR0MDpaP20ZrGfg1iYRMj7jcnC",
        "$2a$04$7Bp7T3qAnsE6qjFlrOJP6OQJWXhI2ywur7UquelkqVyBeTBM9RPKO",
        "$2a$04$Vq5ZKe2Jp6lcB4CmzAtVXO0IB7abcoI8qgWonu3uxuLnTHfk.tWuy",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178738975": {
    _unsafe__answer: "7,5,8,2",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$jLXfeIcxaYcRd0HJ18YQpeIbpgxiF6wUhtdQHMA9CWG46moH1d4ZG",
        "$2a$04$O.ygyCy50SY/txVEOGFXJu0fPx1NOMAT6nZ8B1XPJJxGTlqc1Ol9m",
        "$2a$04$3k/SjXEQNWftnK8RiShgLu7B5VxQuu3b46.E6/P1hwQPkiMw0485.",
        "$2a$04$73xVqCO1Zq9f3lT7Nff5VuAaNkmoVMvQ3bH3tJazTb3nsL5qVWhhO",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178739040": {
    _unsafe__answer: "8,6",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$2tLXJJlkOGHVb5aukbnZPOTX/79V392NCRo735uVY/FfGA5mtYlCu",
        "$2a$04$vcarE6SIlCmTJQsQMteKv.QMOWL8Jm5UZwc572PYny4mgKPk5.9f6",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178739449": {
    _unsafe__answer: "6,4,7,5",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$WaWavWVgyiJKd0QwEbxhi.BDFT7wQF5YegUYnK3qTKlzsZsfvPaJe",
        "$2a$04$p9e/c/SkXzBRopF04qS.EuQ9hr975SOH5gB5gNUps/OyznighKVli",
        "$2a$04$.dFZtRdoSur2zivnXeqc0uz8aGwbSDP77NUhIDb8fQCrOVb1LG0hq",
        "$2a$04$fP3PCvE582sjfLg.0EQZv.3OWHw9CJSTur05ZTNcJC0TxfaOPnFaO",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178739580": {
    _unsafe__answer: "2,5",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$qa/PYEj5LVn46FTWQEb1fuMV57GQjVlMbxYbOwkEqxUi.rIAlUYJ6",
        "$2a$04$fgT4VkJdMc/aeuEBWIg/1OvtxQQ4Uw5odHAQvxhcDAyQG3zQaAJPa",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178739581": {
    _unsafe__answer: "7,3,6",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$6K5EYYZPZamizBplauozWOBcE9XwXvnTX3EajWJfF6TKemBcjRUFu",
        "$2a$04$LX.VRKZT2ytvDV7LBUlv/ucNIWgt7.yxbqjsOcenMQ52dMbOkjF4y",
        "$2a$04$7njlAX4Urxs/LUnHj76u7OhcbuRdT3DQas02ijV5amYSKzuqZ/e82",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178739598": {
    _unsafe__answer: "1,6,3,9",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$2N03A.9V4DLWIckDZDZkmOMe.HjNwiOsiHp2YIe53EWdukbjxk8Ly",
        "$2a$04$tLk4XAO.SLamD0yCjYOz5.ZZwSlxqcKsFjabFT4nIdyUFselQW8ES",
        "$2a$04$eNiTvnQ4ZD8TlbfNKqKt6.7VCwTarHo5nHiVLJQEcD7SQOkFEat3e",
        "$2a$04$mW.ROYAodpDPAU9/5I6Hs.lpdkVYhzwxYOe8cnAAnw6pedtqK612i",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178739600": {
    _unsafe__answer: "9,3",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$gcDEps83.0WEJ.wv0Zgt/OX05WIUvLhfoeWV9F5wnoAAlIi7loi8S",
        "$2a$04$bmwb38Brqh.bMtQlm2Zug.ZaDap0Z4RXBckCVcFoyLOn20hLmF1Ja",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178739603": {
    _unsafe__answer: "4,7",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$xxr3npydPHqREA07pAin7uohDM5Th/LWl35VvtrckEtKy9iprQ/xG",
        "$2a$04$BFLHktN62YzOgGEqe6JAbe2hmktxQyizJPbzdHhG6y/vsqVmq8k7a",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178739605": {
    _unsafe__answer: "1,6",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$GjV.g35ic6NuRTcOhsrP7uWO3fM9Gh2GBHpl0Tz/FXqGNQ5D6/KHK",
        "$2a$04$YfByxsSr72RW7z1eDEjeneEqIdf7kycYT5vjGBnRGxeccKSnCi7ES",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178739606": {
    _unsafe__answer: "8,5",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$9.NK.vg9NRh/GWv49J9IRedkGB5xbamXOfI6ZbQ8fzspmp0t/4Nji",
        "$2a$04$a/N9G3ZLWaZrDl3ueCX4WOglNxPYXB9wA/hGvtbMCKK12umC/gley",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178739607": {
    _unsafe__answer: "3,9,4",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$jcoS9EAG08OfNrlB60Ponu.xhxosPoC032FuoCD/.Tp1B7tSG4YgK",
        "$2a$04$xc5LxydRUe9a2xU81uOn4uv.1Qux3lIoXoJt9nYOQERbzlG4I635m",
        "$2a$04$Nx/sYsNHU0JmPIBhSRFXse2y.wtHjK4skQiJ23d94zPTGMu49MPz6",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178739616": {
    _unsafe__answer: "8,1,6",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$.4kQiw.IIWv9C6NLrciADeVUxB0IeN84w59rh7qB7u4YM273e9gUG",
        "$2a$04$RtE.AVfgeVRf5ZqCdNkUEuKAZMU7.Qe7oJSxH1p//CI.YgJQg3nGi",
        "$2a$04$2J9CHMseMCSgn0WPfwwgGel2P1Cx8JHBJtec7Ic7WZ421DnK5THsa",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178739617": {
    _unsafe__answer: "5,9,2",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$zXkcBB9RE50.FNzQFicZZeqMOBmtwbFWFqT/AALMpbFXlT.gWVf9W",
        "$2a$04$GtLMUutDuoV4dTq5nYa.ce1aeK7zUTkXv/7CyerucE2N8KM36pcUy",
        "$2a$04$B.VPQr4FL66aDS5y2kfMn.ETu8nLdW5K4sxXem9srDmu4yuhRwEgG",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178739618": {
    _unsafe__answer: "3,7,4",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$O7SeVVd49GJYxYGlHXsV.eGVyeYmwDbXTUqyul2kPK3dwDhu1Dv2.",
        "$2a$04$S6BAh7dX1quJNm8x1tYzoO3kksVgarl8VNUNEsw0GcpUgrPXUNCTe",
        "$2a$04$xGSLEqiihvkCUl0MpEQuoOxnLHEJ0MIB0DR7/jvGZ97A/HHLMSvHC",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178739619": {
    _unsafe__answer: "8,5,2,6",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$xngQEDezcc2Y2CDpgqtW.ukLvARIdUUL300S6azWBQ9tzE/FQruoO",
        "$2a$04$6hNSb27i9TL2C0AzASAXk.jVSvFlMcn.U3djMAouhIw.us5lONXIG",
        "$2a$04$Wrqr6pVa9UMYhn60FxrAY.54lur5J2fTu9sJ5Z5R1JrhrlmgMxOJ.",
        "$2a$04$xlGVOpielINZsl/3o7ztOusZfAZybi3AX7d/wZCG1go5KmV2hAVDu",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178739620": {
    _unsafe__answer: "4,7,3,1",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$H7IiHXfpcFNmbA4G4MYU5.yZHXJ0krQ92NnBrig6.8UFtWArTsl.q",
        "$2a$04$P8XoDuiUg/uBBPbfAUfJxuqT7qv36Ygn3vfCaEX0VtCg0xPIA.KZ.",
        "$2a$04$CsNXQVPFYMi0SYZuhS858Ob6rywrEhNbx8fRyqbDgIXpSlsU7wk5q",
        "$2a$04$hQXPd2F.gdFfMb1qrquyQe66pQMB78EBw0L3uGNzNBE/4Zyfaj.Hi",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178739621": {
    _unsafe__answer: "3,6,2,9",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$C1mPK3tq9M/ktn6WPK1/1eTEoo2y7/VMkMie58TeMvqEhT2Dyu1HO",
        "$2a$04$iyxrfRLoPPWtetn97ThU6eTYfYlJ5MI3vHuOXfeKOOulR6/OMr7Fy",
        "$2a$04$q2HFGG0RCQBuTftZcGUiPuPXNI7jymOOJqy0VebBND8WV40Dno9OW",
        "$2a$04$gJ.qFJpOzg697ns3/snMZeONEANlyCSndIRtwFxWBpe86g.dG7NIS",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178739622": {
    _unsafe__answer: "9,5,7,2",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$Ugiv.T8npwSN57QCMzYOm.cYZBDKGUosryExDqgQR7b7zxXV1hyb6",
        "$2a$04$.HYM/vZ/r753YRKzSrHGN.Pk97g50ZuYsRN5KdmhRV7q//tcmOp0m",
        "$2a$04$1awa/D6CqTDJpie/zhsPqeI1rtvC1DvtjSdbAAwxq4RHK71TJt.oq",
        "$2a$04$Ib7Xan7LzG3eY4.gZAuXPeUOHTIBzys8Xgh9rVJfrq0vmL7cFcFzq",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178739624": {
    _unsafe__answer: "5,9,2,4,7",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$Qcmg/IQFV4zMMNDlJv22GeRqnfr8iNdOE49ZBThWe.xqpTE69bGWa",
        "$2a$04$wYhJppglR8ZapDshWriplO5WDksWAOkzacmounvPoB34cPmW3vTne",
        "$2a$04$SzZnvnt/8i4zbRPJ8LIDlOZdg0E9sWa3RDDG79JPpDIw8xJOOj.5O",
        "$2a$04$HXw0HgyRfYjDFzRwuU5YieYvGF5zg88.2lsiPQd2PaFvJ3GODzU4a",
        "$2a$04$ZNhaU8viiuD9jmdBoDVFvuMHMTGiLsLazW4ufaCK.eFApjP.W3/5e",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178739625": {
    _unsafe__answer: "1,6,4,8,5",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$BDlIQHq/.nfghylERg0/DeY.4Aqk.ATmZM/ePpIH/y9h171BCt50u",
        "$2a$04$brgKyVSaYgaqbKjqUrps7ePcxCu.xSvjuEBrvNb2PdLPBvMTYMVYu",
        "$2a$04$Zff65Alj.bs2wCoX5sroUu.kznCdPE..2eDZWmiLdw4TOHfsOOine",
        "$2a$04$okA6EI43W1hu84/cO80D6eoyScZC6r.8GJFG1W14j6pFCflhIxIxe",
        "$2a$04$fRxbfes3SLH4Ymk15xm3jOthxknMPsL7fnqJm88wpjibujJiG5P2y",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178739626": {
    _unsafe__answer: "5,2,8,3,7",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$0B7K2waeiDc8xvoaKlhcbeTNQEt9ZHFTEyEllAUpGqMBSTqSGADDy",
        "$2a$04$la5K6RWi4rDvKExXbPijzuQEf5mlWc/rcEztsNXjF9wiRosRLT7BO",
        "$2a$04$QAyMNS9oA4HVH2GQnYNa1Ogz96PBPTwwDO.4X6o4ayKk2z8T1OCNW",
        "$2a$04$V8VDP8dkyktUOTZOGMwUuuZ4KctzQF0lDqnnHTW/mrRXv/QiAaPoi",
        "$2a$04$6bRw7DdzY3f3DAVR1Rc2Tu.jlza.j715TA5lYsyFuFfc4jeWwPpr6",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178739627": {
    _unsafe__answer: "8,4,1,6,9",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$Kp09M4puEVB13EPDxd/lr.HlG/1VlU4fgFfJqRjRqBviAVwVCVxDa",
        "$2a$04$CjAGNHZwFk3tho9ouMeR6eHD8TdIALq.UCrOroeTXhD6zv21D9ihO",
        "$2a$04$Ozh9Y7A08dzLmlxuYL4ZZ.0jMJulegzC6tD7ktPFL//Nf7QM6ssoe",
        "$2a$04$KwJXWyWKfj/wkkyqOxgNEOFl.fxLgg5wzVK/97wTl5VB6DWw62HtS",
        "$2a$04$e6xFr7f7CwDKxOvwgL36u.bZEtZJDo7V2cS1EwM0TC6IJTuY2OuKG",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178739628": {
    _unsafe__answer: "4,3,2,8,6",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$QwPNzjz50ejDwpMWoFk7De5D9tjSFgM3/ANEw/EFVdStaZS9iNG8C",
        "$2a$04$IIn8240skoAMbssiuKHRaOikj5zjNhyXMUV8N/DzVzLAL.OhUKvKa",
        "$2a$04$l2kLAO9H5cugvvs.dpJzZeSlobS53qVVN2Mm4V.5Mag12D.3B4EB.",
        "$2a$04$dNeBCMpuWj/CFjc9E0Aas.jtIzXQyW5crtTUVnApTW3jfU1OcyvQy",
        "$2a$04$p4c3vW.rn7dGxyK2bvAS9Op9Ijcny7.e/XI1TBSosFfm77OY8LzQW",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178739629": {
    _unsafe__answer: "2,5,9,3,7,4",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$oNep6dpK6IvP.DXCGOUkfusFmxzBIw.IGMyuTTJyA7.FokC6.9cqW",
        "$2a$04$zSypX3/GhqIU9mWLZs6y2OBmMDR3rbbkOwL/HB48.0gDiTdFI1wVK",
        "$2a$04$EPpJazDhkf6PySFVwaCkMuP9g7ERBekGGqswD1lUsyy9K24ZGpVda",
        "$2a$04$Lh9HFGU0fUZ68WqcBq.y9O2oMuM5D8DLw3cgl5zEmJ088vwDbjU16",
        "$2a$04$873vGWfXureKT/RYsHj2kuu8lDk8apt/GNe8/G7uR/xepNkQS/AWC",
        "$2a$04$tuJgmyf2MRytiUyFAG.dqeNxabLlHVT8sB/cODgh1OOnkSqGjugvm",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178739630": {
    _unsafe__answer: "7,3,6,1,5,2",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$met9cst3WWPq7EssmIcZAetmiJguGsFJ6FnXvcBYlKD5lelCOGA.m",
        "$2a$04$6s/NR6ZOuB6tpsn5Ec3xFOoYI21xm3Lvj8yN80GGEEFHlV3RABkEm",
        "$2a$04$yiaUJAd2e7ypbvkd1jVuGOLquaUm/gen/9sTPQUSsvYNzMijUcKrO",
        "$2a$04$h9Y24BZy7ernO4CZ.0bRjeuz0oD3rvDl3/nvBMcfmRtjHIYdjF7JC",
        "$2a$04$I7iK37incMoMGd5S5TPz1Oh0X9rfvgnj.Ky4Fn4YjlGhLm3M/GKO6",
        "$2a$04$MMbJ4pY6SxubVsmZudruvuFcbl/i8Cp6Q5WxisII10h1cNfcNt0Wi",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178739631": {
    _unsafe__answer: "2,6,8,5,9,4",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$BxlN4bCuRXgYCJ1ePujKGug2yAnzTdg5ntVQ.uoO2VZXha.GCXhFG",
        "$2a$04$iTxd1WkzMiXvTxHNie9/bOjcJ.V7nBMBY7n3kjAE2KoOjCXeu/Elm",
        "$2a$04$8yaQ7wmuh9q0blo2wcOKiOzUzLKmDybBXHCRBsYDLRnOUyli2OhFi",
        "$2a$04$90NKCbygvYbtREg9tJ6nyOsRP6RbN0wckxOm0Copn5WpWtumEXAKW",
        "$2a$04$4wZx8d2A4KHMTSka4hw/Q.u1kqp2.haMupUAp6nPG9tcbFwARDFVS",
        "$2a$04$PuJd0LkbSQ7io2jLSiUYIO6LDfkrmnPdwTo5nXoeUr9FoqcN4navS",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178739632": {
    _unsafe__answer: "3,9,4,2,7,1",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$06BMn3Oz.2dasN70T6zlw.Cxa9CZtYThQCuJYxsmSrlMSN4XAFqZS",
        "$2a$04$YB9aIrEDxF6N/c8zGs.XRuthu.ZBtZAZ3yvGbnWwgOFjFpel.qva6",
        "$2a$04$ce1Yt3uSVyklPaYNDDprg.q2A0v0KGvYddbCRpFiY07Apn4WWJ.CC",
        "$2a$04$kxif87rKvoDWLTd2XBNmweH44F0GzeJWUZXbkQC6Kv9UlY9LGeASi",
        "$2a$04$5ehHrq0/2EhUD9aQrngfzeCYpe1bta7mf8Tk93d7.hCibpnjMni7i",
        "$2a$04$c4iN8fBtJPg3Ym.RX9HJse2.Eakp5XZ1J9rBFeJRFtlMcXJQmIWXq",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178739633": {
    _unsafe__answer: "8,2,7,3,9,5",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$IatGzpjvY8t1sBfqAc4mze47YwZ3/QO80ohXRDaWsN1aSWRcd9hlS",
        "$2a$04$MdR7PZirKV7LOn8ydaRiA.VCQP1sSjUXNNh0eUzbSmY28xNtlKTP2",
        "$2a$04$dXW4WXMe2pGrGa8RDRqdieX2R33kCh/BgTbIIeOA6poIyoIgjldxu",
        "$2a$04$NcT7LXXBTzwhprez4HZ97O99tsd275uGPSoMBZcMjjkc9LWncaTjC",
        "$2a$04$dMkLy7tJd0gZ5yyIlcONBOdkYOarYUCMT2T2ZP5PkslYkdgg89/ge",
        "$2a$04$tb88ngZI3yoeC/B6cy7Al.pLV2bmIJqaBArkG.e6yTUtJyTkZRzbC",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178739634": {
    _unsafe__answer: "8,1,6,3,7,8,5",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$O2rhUeWOPoErynEeul2z2.xoY8AoVnTSEvMuXO5.5u.B37Ex7YJ66",
        "$2a$04$EENIFTlGuvTtaD.xzdX7tecwueziJDncj76w61VJ4UBaj57jPeIZ2",
        "$2a$04$4I4cJCrOHYROf.pTMK0VNeKeT.c7nZLs29OPVqXCSPaMg9wjpqZdu",
        "$2a$04$8h3TCn.jp2LCrvKq/FRSR.723wOxCjlN2KN1FvaDR/rOlGGqJq7Tu",
        "$2a$04$M38jUiNh0AwmQM./h97i2.Hjr505IrpiSxqSN7jyr17//8mLpZSXC",
        "$2a$04$ogsigF.UCeLFGceLNKq2su2y6iuIv4lkyGde/RbCLlouS04YVSDu6",
        "$2a$04$8sSj3R9uEZ/JQFSbmMbHV.kpzjw3NNXV8M9uMbddcuqzqo1R7wlBq",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178739635": {
    _unsafe__answer: "9,3,6,1,7,5,8",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$8/P/2C.WMN44Lj6rbUm0BuQReRZoLMFNv/AfcjQ17bB/gxWlM0vle",
        "$2a$04$VIi49q32AiIXY6xC/3JOK..sMp/XONkNpRrvKv3aTQZVVlPg6SUI6",
        "$2a$04$G2OrzW6KO9yd7g64jXLIKeiteKslsM32UM6z5fhiHDEnCgcYte1We",
        "$2a$04$1Wl1D7qiiLnnAXNyeKdPOOkPaAeipLtgBQz1rZwRpBsWLU7Vq7.kq",
        "$2a$04$v6RxGUlIdkRlFokDkMyx8OHUEMfZTiwcNocLrHvZJeORtgYfdNlsa",
        "$2a$04$s.IGvOP9j9IWEx.../tsI.NHhKfhRp4ryC6S1RTz9WU5gIthi3LYq",
        "$2a$04$47EZ03BItRyx/TNji7e3zOGTrEko0JuGe/ohWqDOTCmG3GGT7egR.",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178739636": {
    _unsafe__answer: "6,3,1,8,4,7,2",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$eI6YadG/RVEgngk9b2wRJ.wstn1iWXU9g5glAEqyAu1Q2KDLq6M/G",
        "$2a$04$fYimTaSi3UQXcEp35KBu7ecaK9998mJOh8NwRITr65b.bLuMq/Q1y",
        "$2a$04$zZK496JFE2TttSSG0efj9OGYuba1ee9AF7B9EnrHClOftNW9ALTIi",
        "$2a$04$5OZnGMy7gNNAlxr1KMDjPOORMg4c.ffRjDO5SHII6gZBmDuRKnmNG",
        "$2a$04$KFZptv7idXOGHd1LAfG8/u1wkVR1/JMceBQpcpiWSCW8Xqu.wYHH.",
        "$2a$04$032zGvyEyhkEDfdzgsjgj.T.JjhCBZfbByGSfI6QolXAbVjYRl09.",
        "$2a$04$VmGJ3vkYkxz8pf0x3qYkCOUfSqip8LgomAuuqqD5yn9l/Y9gnx0Xm",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178739637": {
    _unsafe__answer: "1,8,3,6,9,2,5",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$NfBA.ImIgca2.3SG7jqFg.3ON3XsFyH4yS4svBQyfO3HSJB/60kiu",
        "$2a$04$5MEJJqK4asrafc6XmVCYtOL.S96HwCW8YuTNdyogHHtbWCGORullG",
        "$2a$04$7N.Afd2MjhUACR4lqY5bE.kJ4rcgIJiDo9A8Z9EJnM4IIhRNMXaD.",
        "$2a$04$BVoAizQ0cbsCFgs6jHqVvuY77fsoZFtJDfhd.vMA4hIA0MxBkERr6",
        "$2a$04$lgd6sfDDlCkvJt3yORmMROLeRVl9Ei3sC3dg.0b1rDdKWMx/u86Pq",
        "$2a$04$/8Fb6GNvORNdieczz6IdJuvwco51D7.FMM3kdkp5mooTwreVcUTF6",
        "$2a$04$u.b9Sl29qy4HsFVbHzdpnOZ7BncCsDULVFBZG9BTIOq2RwE.5hsFi",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178739642": {
    _unsafe__answer: "3,6,1,7,4,2,8",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$Har2HPYAl7xTOt4OExYaeOyB3POViwLQfW0nkHN7hyqOtl1lBka.W",
        "$2a$04$od3LPdqHiNRf5.vzpXMWOOzR81AklhcYCbFubmI2xnoBP4Om.mf/G",
        "$2a$04$lq5ht3Uf3N.RJ/..GxitneiHNdKKtYbx4tHCHyihvg53/m0hObNC2",
        "$2a$04$KxRRBLZRrt8CuPNcRIB.5.cQ6.ig7C5PlCgs4UJRG3VrtVlopDwRq",
        "$2a$04$uHdiKoEe7/hfQaOB/M3dfe67TKEa5qkAOZb1SaHJLAOKOlYqGXl/2",
        "$2a$04$.JQpCznXErxDaMvJOe9pmOodpEIcTE/yqlq..fJfasVxFGLJnwB3q",
        "$2a$04$NO2M40rvu8LlfzxcL7.Ph.exb0aJjAXfHBkzej91p6uwqzQxp5A9S",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178739643": {
    _unsafe__answer: "2,9,6,8,3,7,1,4",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$gwHxnta4hrKIuDkL/ql32eotsxVhR52El.wgImJbmIcYKr9WOrRHi",
        "$2a$04$xV4fEGIDQH/xM5lWbVn9uO57xo8v274qdD7J9847u1b8F5syo3Vga",
        "$2a$04$bjTBx1vOPDy//4uX7vVr8.X0D7PZC.0QxH3uVj46TlcQcx6keEWmS",
        "$2a$04$ZNjjxUP.BDLYmLCLuT9KU.poLJinMtuNABhAUGyvpP9BUzpWzuJvC",
        "$2a$04$wqZMhP3unumM3LFeUrdUze2H93bA2DJEHgXJDTu6i0JbEYWzpT.4K",
        "$2a$04$cKunzZu9.oLJ7N2F1Zu9A.FX9nxCjCM0IaG2RtknHuDcNwScy4pBi",
        "$2a$04$JevDECf6.hQFyNxIIbf8SOYcLtnvYeG9vsTUFMRqpCcatVfWuK9NO",
        "$2a$04$45pB.Za98CXUVS9OwdnK7eD5EbX4bpMX5DRGWgpO2F2byjsM82Zhm",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178739644": {
    _unsafe__answer: "3,1,4,6,2,7,9,5",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$4kp6fXAG8n1enqkK5X2L/eNkxCMLa0pLx6bFyg1sCZagL.X.QLBgO",
        "$2a$04$eSsjhxWnnE.54ovkZkHlN.CcmetB4syPTj4nxJY3wOPXOoxPDdpLq",
        "$2a$04$4ECkOWCEpy37Q8LdKeg7c.cK2OEB1k75gXTb2ggqQ6EURHwPKAzkK",
        "$2a$04$voy8rEJRzOJ8JhGGgBTX6evzDhSANmIT8st31exV0..3JPy8nNUuu",
        "$2a$04$XCfNnSgyaBzoWPV/KTtoeOTDItflU1E0kF6sOPw7/v/Gu49Vvz/vi",
        "$2a$04$mfsq6FZRtv.pEtVtEt6o/.t0xZkcbYQ22J1XeyLHPUbmeJRgAIQta",
        "$2a$04$jAf9TOvWk2dr9rMIvUlgB.4fWKU0.ARNTBvQ81C4jtdMZDWudc0zm",
        "$2a$04$miPL6SVq0O5vOpaUjiD23ue18QqZ/IdNKrg9kOCUEg7D13oO8qBmS",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178739645": {
    _unsafe__answer: "7,2,6,9,4,1,5,8",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$Tfw.MS7dAohc1Q87Qo9xI.VcE5LBBO4YKNaDCY4k.fGo3SDK6edbi",
        "$2a$04$xUgp9s.KTGVKeU.l9xATQOAOqypjOLXTWia1778LZxUr3Sk9fQS76",
        "$2a$04$.9kPAK7UHIrNVj6D.sbbXeXFEmOYOrrSMGY/fvQQ/slCathtpPlpq",
        "$2a$04$Rj29EyPHLWPXUdX.bPr1Ne1NSvIxEjnt/7MBl.n3mKo4m//UzNVV.",
        "$2a$04$JQTejQxtmWyuePui7TDq6.RQqKwcO6ilA0Xc.KMZa9GHnAsN5GtUm",
        "$2a$04$yiD8cuTOrVe7AefAfJAzBu28JTaRyDJ4k/K1YmW7BmczUaCoWgmx.",
        "$2a$04$EcjtYXYx185DLkMMoj0PpeGTXSzF.OgOcPHpZwv8QPaKvDDEIH5N6",
        "$2a$04$0CdGUg4/8VQ6ovwxq5v5Yeoxo8NIFYdM4Dc9NAsRQBCqs1kobeKyi",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178739647": {
    _unsafe__answer: "2,5,4,7,3,1,6,9",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$Y7MdPvp5mgBHBurViIyy2eK8PVqyggzTLpQhwcI7JWBZTW5ZQvIG6",
        "$2a$04$c9eDT4fafCDFR9BBMqsRCOAODoErEG6uXKocGIWccoOIlgjeOgNWS",
        "$2a$04$GstCr24SAtUrmFc/GPvSE.Mf0EbU5l6Hv.TB2ZpRtfCfN1GeL6vKS",
        "$2a$04$/PXd2ujwF6O5rDm8MgFDU.HOWKRi4ZPoc/tiTSs.K2WELQRb04bQ6",
        "$2a$04$6KVRzhXoD067Ibd8GHFCYeurI0TgOiexnC7jsrLBEyGJm3smNV/A6",
        "$2a$04$K4KgqYEyxc2Iwll.geAvBeQJYoqJZO3Pg6iJktxdsQM8pVUWeRSNC",
        "$2a$04$.gcK46MTE/o.Aq3lW5uCC.6nIw8cJAULsPwaHw7OasiGOvPEQK.tC",
        "$2a$04$1sfzFzR770n3uD3EZqLAw.RGWWBE3RVEqmBHqXelXmiPusV4YXeQW",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "178740090": {
    _unsafe__answer: "80",
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      values: "80",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740091": {
    _unsafe__answer: "80",
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      values: "80",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740124": {
    _unsafe__answer: "80",
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      values: "80",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740142": {
    _unsafe__answer: "80",
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      values: "80",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740143": {
    _unsafe__answer: "80",
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      values: "80",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740145": {
    _unsafe__answer: "80",
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      values: "80",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740147": {
    _unsafe__answer: "80",
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      values: "80",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740148": {
    _unsafe__answer: "80",
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      values: "80",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740149": {
    _unsafe__answer: "80",
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      values: "80",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740150": {
    _unsafe__answer: "80",
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      values: "80",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740151": {
    _unsafe__answer: "80",
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      values: "80",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740152": {
    _unsafe__answer: "80",
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      values: "80",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740153": {
    _unsafe__answer: "80",
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      values: "80",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740156": {
    _unsafe__answer: "80",
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      values: "80",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740157": {
    _unsafe__answer: "80",
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      values: "80",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740158": {
    _unsafe__answer: "80",
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      values: "80",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740160": {
    _unsafe__answer: "80",
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      values: "80",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740164": {
    _unsafe__answer: "80",
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      values: "80",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740165": {
    _unsafe__answer: "80",
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      values: "80",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740166": {
    _unsafe__answer: "80",
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      values: "80",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740167": {
    _unsafe__answer: "80",
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      values: "80",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740191": {
    _unsafe__answer: "80",
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      values: "80",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740196": {
    _unsafe__answer: "80",
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      values: "80",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740199": {
    _unsafe__answer: "80",
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      values: "80",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740201": {
    _unsafe__answer: "80",
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      values: "80",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740202": {
    _unsafe__answer: "80",
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      values: "80",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740203": {
    _unsafe__answer: "80",
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      values: "80",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740204": {
    _unsafe__answer: "80",
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      values: "80",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740205": {
    _unsafe__answer: "80",
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      values: "80",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740207": {
    _unsafe__answer: "80",
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      values: "80",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740208": {
    _unsafe__answer: "80",
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      values: "80",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740210": {
    _unsafe__answer: "80",
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      values: "80",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740211": {
    _unsafe__answer: "80",
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      values: "80",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740212": {
    _unsafe__answer: "80",
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      values: "80",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740213": {
    _unsafe__answer: "80",
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      values: "80",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740215": {
    _unsafe__answer: "80",
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      values: "80",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740217": {
    _unsafe__answer: "80",
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      values: "80",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740218": {
    _unsafe__answer: "80",
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      values: "80",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740219": {
    _unsafe__answer: "80",
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      values: "80",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740221": {
    _unsafe__answer: "80",
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      values: "80",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740222": {
    _unsafe__answer: "80",
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      values: "80",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "178740327": {
    _unsafe__answer: "first",
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$KM/LR0/El4DjMqpy5CAGde.va3RDdYU/449wis5SqIO.wT3mRWatW",
    },
  },
  "178740347": {
    _unsafe__answer: "mountain",
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$ZozGuTV.gpEQpMa5.mTACuDfsKyoaVo1fPQGSFd19zD6EOaHKeT7i",
    },
  },
  "178740349": {
    _unsafe__answer: "he",
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$yMTNifWp2x12i0D2slTTlevrMvmDgqfmsWcT.5rPxTPlX91MYRlwG",
    },
  },
  "178740354": {
    _unsafe__answer: "he",
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$3lGObth5nksfAMBrcfFmZOZWkF9jXGRZu2F7LFs79hjUVPlpDteJe",
    },
  },
  "178740373": {
    _unsafe__answer: "my",
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$XM1Iylj2/Wed8vCZA4otu.RIIs8QjpJKoGSfFOioDviVlyVY4e03u",
    },
  },
  "178740387": {
    _unsafe__answer: "had",
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$pBZlhrMN3XQCyr6ZK8DujOjEp2eJvhdi1Gs3gZ/7vGyVwB1mFstB6",
    },
  },
  "178740390": {
    _unsafe__answer: "ten",
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$nXmFaEm7zNNCm570D.7aM.PHqD2Gzcqb6zwwwFLE6NbsUaUnybZoS",
    },
  },
  "178740394": {
    _unsafe__answer: "bee",
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$MFxysIyKiQXOZ8W7vHUCau/mYlomfpuUeUEXBzwBknusZf2/hS30C",
    },
  },
  "178740395": {
    _unsafe__answer: "was",
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$Xabp2KTVxSrdkCdw9FBQYek0Vxacbn/M51tRRFNA/gud6fz1lqGpO",
    },
  },
  "178740398": {
    _unsafe__answer: "mother",
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$uAkruxyNZwVemBJI5vxSme.owuZ82pLvwLnnQBBobvTeUm2z.rP/2",
    },
  },
  "178740401": {
    _unsafe__answer: "house",
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$BqVpcHEOQYnMoBATfZIeke6YlqyVIDOMUe79V1OlwsbVVcdsDFiXu",
    },
  },
  "178740404": {
    _unsafe__answer: "funny",
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$PKNJtYZriqbuxTV4SYrWYe9H0Zj7Y1j8IlkpT6Mo.9YVjZg9Ts0ei",
    },
  },
  "178740406": {
    _unsafe__answer: "said",
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$sKU.wH7n5LmpqOCVhq5UcuqiV2D1kg/84RhyN9hF2G1wzxbHOMrCS",
    },
  },
  "178740408": {
    _unsafe__answer: "place",
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$Rk.aAgwGOaF34YJyrbUg7ONaWSHSU/S58h7jLLHlEPLBOK12Omh8.",
    },
  },
  "178740419": {
    _unsafe__answer: "shoe",
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$UdFBV87EqqW.PguQncn2NeQGCZuYX9Do8CXhFxBqyAeB2SFOtpEqu",
    },
  },
  "178740420": {
    _unsafe__answer: "were",
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$E83SGhTGpU1jwnMJCxDdE.ywTjTdjaX8PJS/v.zqcqlBUpCJygrKW",
    },
  },
  "178740421": {
    _unsafe__answer: "early",
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$Ae5KpYao30Dh8lJ7FqqhgOGWS1DikPtgJ5YTzNDMGRiyn09GZ0nJW",
    },
  },
  "178740444": {
    _unsafe__answer: "before",
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$h/ZgBYJmA3uNSTiW09SfledHaSG0sCsCXaIY.0Mu1hZojcy/D9TPa",
    },
  },
  "178740445": {
    _unsafe__answer: "handle",
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$xxy46KogSY4SGWy5LQa7D.DF/JKKTUy/Ar58jBvmDbhY.3IrK6X1e",
    },
  },
  "178740446": {
    _unsafe__answer: "second",
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$MQctIqYL.wHgfTvi91mRB.DH6Mk91QjW5SB2muUugE2SojC2yI/0e",
    },
  },
  "178740449": {
    _unsafe__answer: "question",
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$lkpyVHZnBEcvWvvByXX66uowXeP6odGisIDubAyDE7Re.qBx3Sc76",
    },
  },
  "178740452": {
    _unsafe__answer: "popular",
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$XQEKEi7HYvD6N8FmFzdJJOikDrPLJnWk7ullHwv385EZtsWAuxr/e",
    },
  },
  "178740453": {
    _unsafe__answer: "laughing",
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$tO9WbU.msZHZLbKh01RgleWycBxVgwNvPUeEhVVQ41cl2PPWDUBnC",
    },
  },
  "178740454": {
    _unsafe__answer: "garage",
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$bxYdVDKPZexfYQlS7ietnOl9M3zexvA1Cq9HqTW24aYhs7c1nBCuO",
    },
  },
  "178740455": {
    _unsafe__answer: "international",
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$mCuo1fz/nnQxnebkDZ1PJuBTH2fD/R9NhlS3ZsMA2r1YT5ysv9JiK",
    },
  },
  "178740456": {
    _unsafe__answer: "general",
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$GHIQavOUbpgL6ymOl..I9u6ax80tdrLw/SylRbRsMy8ruDebJ7F/6",
    },
  },
  "178740458": {
    _unsafe__answer: "doubt",
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$3pT3x6ehcm5jdkTRP4F5d.Ksy/uG.fYeLXVM7vl0O17pK3T5K2K6u",
    },
  },
  "178740462": {
    _unsafe__answer: "beautiful",
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$/6BE6WK6DfhgzP/6LbY3oeLijYe/OMwoF8d76kE9NeiTtFR87tGN.",
    },
  },
  "178740463": {
    _unsafe__answer: "concrete",
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$MLl.HrzXZU1svgx3XG4HvOTa9xghWRoBEFlNMDQXXixCMN1MG2HnK",
    },
  },
  "178740464": {
    _unsafe__answer: "bicycle",
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$4P2ngVVyCcSzMKuANA.Ff.GdUE5LAOckXGeIzqKVujG7KOY9O6ApC",
    },
  },
  "178741517": {
    _unsafe__answer: "tomorrow",
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$gSFmRbwbMURHSNmTOW.nWOCuMqzLbYwpfo1qx9PLyLorcpPxgk7vO",
    },
  },
  "178741519": {
    _unsafe__answer: "advertisement",
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$HnlIHIxNE8UWrC8U8Edh4.iGEILJ5nO7HmVyDYaPlnWj6nGRL8MLa",
    },
  },
  "178741521": {
    _unsafe__answer: "coax",
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$THXIlxQyiBDc.rcD3qBfjeirAnYeSN3Z6ReJMYsU2CdjfZj75iurW",
    },
  },
  "178741526": {
    _unsafe__answer: "congenial",
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$2B1XeI2uuqOfx08CbjpMW.dOTKu0I8gslSHe8p3GQ0sSAyQGzeI8q",
    },
  },
  "178741527": {
    _unsafe__answer: "squirrel",
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$VrScIFiZzlsVCxzzIpyYR.qQIZrwRadG4maHv21mqLMy8R5b/b7O2",
    },
  },
  "178741530": {
    _unsafe__answer: "necessary",
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$yeZMAvpd9XF8iNj3PoZNB.RYenUo8N5ggZlUh7GLm2EO.y9cOLElK",
    },
  },
  "178741531": {
    _unsafe__answer: "mortgage",
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$aGw9wWliO5yIfAgM/F4eVuaB/9PRz7ooyd0XadInYxmlhMb6T8wzi",
    },
  },
  "178741532": {
    _unsafe__answer: "arrogance",
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$1oVTj28x6nAoX524nBtrleMD6cxqybmHE.A1nFn867oNIKe/lQWGC",
    },
  },
  "178760533": {
    _unsafe__answer: "exquisite",
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$urXtOpy0a1FiGu..okf4..ETw6PITCY7r/AB7/HA9z84LkLHMt5Q2",
    },
  },
  "178799883": {
    _unsafe__answer: "anonymous",
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$EaGbg.fsomyk80FE8gg7d.2GfL61ODDWs.ipoFXmLoYxvwL3CkLmm",
    },
  },
  "178799885": {
    _unsafe__answer: "zephyr",
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$CXAGJlQGGb1y1t6I/9Sx.Onp4urdUCOU.mMvcz7bb/cjPZV.hBOpy",
    },
  },
  "178799889": {
    _unsafe__answer: "dilemma",
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$13wPsUb9obMM52ODEoBysupcyS2iOwtlwYt0awvh.2bv3zwArw79m",
    },
  },
  "178799891": {
    _unsafe__answer: "acquaintance",
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$8FoRrKmGrb.12HXE7xNQ9Oz4BJpb0o1S3lzvBcDD4V6E5tG0Ff1Gq",
    },
  },
  "178799897": {
    _unsafe__answer: "exacerbate",
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$H8J/iHB/b8qBROYCj4uCYuGFvzD3RrnX8AI9rmtb4FARXDiiQls2O",
    },
  },
  "178799899": {
    _unsafe__answer: "conscientious",
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$wYx4H27jP9sM1QtDxnGnCezsPhpxyG9DwPajTWmvQkTzF7nTB7JWy",
    },
  },
  "178799901": {
    _unsafe__answer: "questionnaire",
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$vtEg9coISzRLXWiYhuduketwHf9uESZ6pNxerc22i13NwrOI4uije",
    },
  },
  "178799902": {
    _unsafe__answer: "camouflage",
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$I78zqaZLaYZGuzhiHKy9xu.DjH3pVAewvIIEsWTGYo4dCXntrLNKy",
    },
  },
  "178799905": {
    _unsafe__answer: "iridescent",
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$x04M.6jOhm6qrFlKGKypVuHXEB70J9fDkF0yUT0JxP53DfBjknR8m",
    },
  },
  "178799906": {
    _unsafe__answer: "impugn",
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$uB0t4EMQIwCks6.o6kZE.O8ASXKqViPpbFRN83/NmrN7KKR4QgnTu",
    },
  },
  "178799907": {
    _unsafe__answer: "scintillant",
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$hy1qhnVjxnRV/zvIKl1eZOBZiOEfWyNK/ZUzfklvAwB0WbsIRFXZu",
    },
  },
  "178799908": {
    _unsafe__answer: "lachrymose",
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$4nhAhNH..6CskE0lz5tR9.wFZ5AF56MYG8Mayxn5IlmmIxIl/ygka",
    },
  },
  "178832427": {
    _unsafe__answer: "",
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$aBC1gQpKcefJolMK9c6yOuuVmCdsTY1f9pXYYgCwoQNhCkGETtFP6",
    },
  },
  "178832428": {
    _unsafe__answer: "",
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$K6/GkFviwZq4l5/Z3hgIwuceN8tipPU3wnDMzZJ0otpXddtDx.VxK",
    },
  },
  "179241620": {
    _unsafe__answer: "(0,2),(0,3) - (0,6),(0,7)",
    responseType: "pair cancellation",
    answer: {
      markAs: "score_pair_cancellation",
      values: [
        "$2a$04$JhK8OJr7TyS35rAl3HX6E.hC2VKSGeXfA4hBjfZgUY4yOdl7idF1W",
        "$2a$04$adIOzLnDR0hFb3cj9af8MOphAKVIV6.swMT.JlWbOryiXbngNBrsG",
      ],
      description: {
        code: 7,
        description:
          "Receives answer value as arrays of coordinates [ ['1,2', '1,3'], ['4,4', '4,5'] ]",
      },
    },
  },
  "179241621": {
    _unsafe__answer: "(0,2),(0,3) - (0,6),(0,7)",
    responseType: "pair cancellation",
    answer: {
      markAs: "score_pair_cancellation",
      values: [
        "$2a$04$x4DF6bLkxc8HFejxiXTww.xa6PMD8GDBdmFJS5HfMKJDEMrdHDPgS",
        "$2a$04$BJ.D5CaQaF9nTVdG.Khi9.BMbEq2cJ92CTfuNwE0uF/ELpBZcVL2K",
      ],
      description: {
        code: 7,
        description:
          "Receives answer value as arrays of coordinates [ ['1,2', '1,3'], ['4,4', '4,5'] ]",
      },
    },
  },
  "179241622": {
    _unsafe__answer: "(0,2),(0,3) - (0,6),(0,7)",
    responseType: "pair cancellation",
    answer: {
      markAs: "score_pair_cancellation",
      values: [
        "$2a$04$43LSbsAdbxP6/OtYtGg8ueEjeIy/Ei9BQR4Mvr6sp7Mk9jCkW4XkW",
        "$2a$04$917G9PczVyukXptvxfrk1.Ao9ZAIq9gdvmHrTzFTBvh.zm420vLMa",
      ],
      description: {
        code: 7,
        description:
          "Receives answer value as arrays of coordinates [ ['1,2', '1,3'], ['4,4', '4,5'] ]",
      },
    },
  },
  "179241623": {
    _unsafe__answer: "(0,2),(0,3) - (0,6),(0,7)",
    responseType: "pair cancellation",
    answer: {
      markAs: "score_pair_cancellation",
      values: [
        "$2a$04$RY/uQLUAeaMKIqeBN3PvGOqpSdlE7t4TJ1eQ2szs6BWNSLgtu2QAa",
        "$2a$04$QE7dgAgeOCkyH2ViLXUFIO94L4sy/azuGCle82IDNaYoBxqSXei/O",
      ],
      description: {
        code: 7,
        description:
          "Receives answer value as arrays of coordinates [ ['1,2', '1,3'], ['4,4', '4,5'] ]",
      },
    },
  },
  "179248450": {
    _unsafe__answer: "(0,2),(0,3) - (0,6),(0,7)",
    responseType: "pair cancellation",
    answer: {
      markAs: "score_pair_cancellation",
      values: [
        "$2a$04$ne4NFVqVNERDtYjsqXckY.u9kT8.BFy5/0.e7x1KLB02F9HI0IE2a",
        "$2a$04$vqqh2/PAxqEytoIvmnYwYu.XkfdpQSCh4DhoPL2eIXXcp2CyVsUHW",
      ],
      description: {
        code: 7,
        description:
          "Receives answer value as arrays of coordinates [ ['1,2', '1,3'], ['4,4', '4,5'] ]",
      },
    },
  },
  "179248458": {
    _unsafe__answer: "(0,2),(0,3) - (0,6),(0,7)",
    responseType: "pair cancellation",
    answer: {
      markAs: "score_pair_cancellation",
      values: [
        "$2a$04$NnD3QhWVmh7d89DILd4MRe8CemlhQwiQezc3tzzVE1.tRhIYu33R.",
        "$2a$04$gmORlAeRdHKAKtkZhXf5KO9.vUimYRkf6cSGNSaOQn9dL9JxyD/uO",
      ],
      description: {
        code: 7,
        description:
          "Receives answer value as arrays of coordinates [ ['1,2', '1,3'], ['4,4', '4,5'] ]",
      },
    },
  },
  "181771222": {
    _unsafe__answer: "80",
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      values: "80",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "181772006": {
    _unsafe__answer: "80",
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      values: "80",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "181772014": {
    _unsafe__answer: "80",
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      values: "80",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "181772038": {
    _unsafe__answer: "80",
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      values: "80",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "181772041": {
    _unsafe__answer: "80",
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      values: "80",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "181772044": {
    _unsafe__answer: "80",
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      values: "80",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "181772045": {
    _unsafe__answer: "80",
    responseType: "voice",
    answer: {
      markAs: "score_voice",
      values: "80",
      guide: {
        code: 6,
        description:
          "Receives answer value unencrypted as a number % back from the voice recognition API",
      },
    },
  },
  "181825373": {
    _unsafe__answer: "man,boy,child,daddy,girl,lady,mommy,person, woman",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$G6qRAVLHP6R8nzp/qD55y.W6pnb2912JYBSE/tcsH/IjQrdIZYdH.",
        "$2a$04$C4BlyKCaLBur3p7oWWz.U.2htcTO1kKf/55QHkhaupQpe2GkoPica",
        "$2a$04$2E/oIVHNCq4zAoqOq26PFO6wOjei0DtKr7AtAZsM3B9qqWin9khlC",
        "$2a$04$j7vuiOd3jKdoIFxRdu6t6O0WPIjW3rT9mRXU5s3ju3zWvOTjxpWAy",
        "$2a$04$TVATYzP18N7iBb7e0QuH1OfdnPV5aE4aRaKHzQCvw.IXqmBFdItjC",
        "$2a$04$2Ax0jSJYFwi/Qv8aQFAc/Otsct6JI.IzNESjJhEwwTdzPZF48tARa",
        "$2a$04$LiYWzdUZCeV6lvdKlgQd/u6y./Xk2pxVCUGEQ0YX50FSuaQ.djKy2",
        "$2a$04$jQkw0T0vjmn/FyaZ08puZOcDprYQiT2qjGj01dQcvnld2yfJesgSy",
        "$2a$04$Z55FkfVo88/vTN.UgWcjTe3.Zd//tskFS5Eo7qPdW0bhWoma/p7DC",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "181825460": {
    _unsafe__answer: "stairs, steps",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$ayZe3MJi7KsymYRMmy7Z0ez5.SxZ85M2nFRG9j8ptxXeAJwn1vPOK",
        "$2a$04$DY3..q0begeZCIr0ZRCQ2.0tWySsgmu5uvV4wnlVWeh4UR4dLaOHq",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "181825465": {
    _unsafe__answer: "table",
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$C0dsDit9G9fyestPf9Uzo.sR7.nhwwYHINLnqd.MHPvKZD/KaUIPa",
    },
  },
  "181825540": {
    _unsafe__answer: "chair",
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$HYRcZ2DYP46eRUsa/KP19eeXaziW8G0RnJp2NTYMHug6k8h0ON8Zi",
    },
  },
  "181825541": {
    _unsafe__answer: "sofa,couch,davenport,chesterfield",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$es7c8TEn85Oy3yaeH4fuuO03tfAfHNsomVq5CA7eFl/4kwEZ8XBuW",
        "$2a$04$GUrRJVa7kg224jc90JR/gOZlWHz/ujaAXCO4UGtSgIwP0jCwQUWBu",
        "$2a$04$JGwEkTNnB5r2I6mL.aV1ruQ/pw1MFWZqfIVQhtyvKe4U.GRmS2sz.",
        "$2a$04$AzNal9LGRLf0MNeZ0e0twuNEewVFvmtqLE.sVEb2ieBLBAZU1f3wS",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "181825957": {
    _unsafe__answer: "is, looks,was",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$ilAjTgQeZbLV/y2/FAm7DeUiBLwm/YMXsIhpONl4btCGuEvCw.4J2",
        "$2a$04$rKrB2JiiDgjBmiMoCg4jHuhJgkSjJjnuFhEDIloclQw6t2ty1LBsm",
        "$2a$04$8siq1H8wIqptXeK6tYwnSua6qxVP.rUvDzxB0.edT4/Q8VbNes3HW",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "181825960": {
    _unsafe__answer: "under,beneath",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$grWBd4Zm.XmDxPcBwepfteyIXzg5skXIB6bsO.icUkQlmBaKf2fAe",
        "$2a$04$80SAdhySV.C0gogh7v.llOKFoQcSL5y67u/3JZv/J6w3WnmdY6Ds2",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "181825961": {
    _unsafe__answer: "book",
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$/MXLLS.bFehEtDVoCNhnZOhaHE.VGF6XK8HATVxMgmhzJMzlh37Fe",
    },
  },
  "181825962": {
    _unsafe__answer: "pick,clean,straighten,tidy",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$9nuhRmCfkrqsauamERkjSes3NMixDS5zSuJeCNgltdf23ou99w9oC",
        "$2a$04$B0v1Z.XCmq5x0fnkCSUYF.7t/cm4EGc50ZFsDn70YjHICzHctTtoS",
        "$2a$04$lqEsTF71xkTzg2cV0kXOZuFAHjiYZxWihBedzK8YshUnDj14yFsOW",
        "$2a$04$v/fBWbx0PG8zzxlXRpsYcuDWOFqnDAvBkhghpLwtoZgiIvFwPnc12",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "181825964": {
    _unsafe__answer: "dinner,breakfast,lunch,supper",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$ZbhqrjOF5JJSQToO1QXz4OTPxzblTl37so0yg2h54WuJSBgDGBnQC",
        "$2a$04$EMbPh3dTU1o5nfdN1sOeiuPdPHJR1lgLtlbbHSW0u4z6VEKDLAtqq",
        "$2a$04$L60AGUAP/.ppF7Ht.xcg5ugwqr166GZd9h9J2azJsO6OYiGWUSGwi",
        "$2a$04$iFpQW8PdNl.bN7R9.2eHTOqB7.9Zy68aoBJZvJDnPF8XwHkJjuD4.",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "181825983": {
    _unsafe__answer: "read,checkout,do,explore,get",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$RJVHcpIRdI3h6nvZfVNF9OuFQ419GYb5Fzu2UdicRxP9qtcBWSlhu",
        "$2a$04$PW8XyoEIS8fyrD3RvbIYXePh48eQyOcwtd9Nyw15.fPHs1hH2Q4Oi",
        "$2a$04$CWPjg1cnfu.aGX1lmUp8Ue3orBfK2vdLhowEE0//KB8DV/7wkYViu",
        "$2a$04$umGPqks734b9cuEwgi0WzuMSoONT/uobgWWsDjibI05/aaqo.gjce",
        "$2a$04$6B7CcUPXqkuAIzG0gK7hp.GeCnXplgQ.Fx.zpesPjY2YAooS2.mCy",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "181825985": {
    _unsafe__answer: "down, back",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$28eWEGtXzpQYBGK8cltzjOdW63d.UkQAGH6bwBo/3vD6rOpuz/uO2",
        "$2a$04$P7WlOaGqWK9OGVUyPORp2OkYHS8IDrX0Z.kdOQ2Ni.WDO/il6E04q",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "181825988": {
    _unsafe__answer: "every,at,night",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$wV.hMAKgotMdfU19bghmveIeID.GYHAswte1lqLgnfePV5b7SEBKq",
        "$2a$04$B3WnrR64ljpVjll6XA2wge4G/FXjt9Y8x3IOewLPGjwboFgunOZlK",
        "$2a$04$32YxRrGclOxnU8NRCL5Lw.6jiJ6niLUB9SKFF5yvyfVGRe8SMy85S",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "181825990": {
    _unsafe__answer: "cold,freezing",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$MmJxlk/KoBtTijvsHhV1/OgGHugeOa0T6Tu165a4SpDZ0TbhiJ3Qy",
        "$2a$04$tBoSSDM0IcESFjP6M3KIbulJpNSlxRN6ED3FQsdZk6IhvYdfAwsgS",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "181825992": {
    _unsafe__answer: "ringing,beeping,buzzing,vibrating",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$VBFPMBUZ3VT3RhwA.oeSl.s1wM2VrYuiAMONuazNljy/eYRRzmGZK",
        "$2a$04$gd9zQQ3UbVukJ2Is4P/E/u3p5PFydqyUMNYQPhMPkLxc2qkz1W3xW",
        "$2a$04$R4ITxZ7kknHwuL7W5YxcB.SZn3X1BoGr9TC7UB5FxWf/oEMhhHMx6",
        "$2a$04$5R4LuiNkNcldXSVyEcuZBOqIOOjR6Xlp7ihFe9n3kYpFvfIEujj6K",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "181825995": {
    _unsafe__answer: "egg",
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$cCYF3FgeW2kgOmhSlz0szOVszMmhLy0zi.SdZ31ZYBAOChP7rd8we",
    },
  },
  "181825997": {
    _unsafe__answer: "band-aid,bandage,towel, plaster",
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$Rprg4SMGO2TXNu5XJnWktudtPkFItOcksjshex.vr7LxFLJelobXG",
    },
  },
  "181826004": {
    _unsafe__answer: "water",
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$hwaU.Jnl./pjMyvLsLBzbOZhRFtPTyyp0OEx..Wr0Y8dTCsZUbhc6",
    },
  },
  "181826009": {
    _unsafe__answer: "fly, glide",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$KOJtNV3jUXsBAWTPkJd.j.1C2YDukDlJ8WzJG2H4K2UJKOiwqZL0u",
        "$2a$04$67A1bCXY.wzaDkVMlJvCMeElzFTCimU9hEVJOJtbru6O0DMBTzkjW",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "181826012": {
    _unsafe__answer: "round,rounded,circular,shaped,spherical",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$2ANEZHEoKvNMLeQuUUK8.uH.A4CpD4XKWiqvPONJWdH4uEJwvpjby",
        "$2a$04$47ZUn4c6LLaeJVUaCEP5puTVM8KgcoOdjQV/Pj9Ot3QMZ/VMkdg1G",
        "$2a$04$2Jsi80avWMcL6QKjrqRnRusb2EeNLcM0qHv598jm9AyEMDhXPd4iC",
        "$2a$04$LoLROzV/P4tsBE.0KvNJq.IWjuO6YtEdVaQEaljkorX3zyb40bDeu",
        "$2a$04$6jgWwjHmyI4TxVhOwXtRXedmeFjwuA4Jm2/KytQ31HrQXR.IGW8iS",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "181826137": {
    _unsafe__answer: "rained,hailed,snowed",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$.IR2/OdGXwnNUPNE.0r5PO3xL77bK04Ou8G04zO14lnKRKuAjXAYq",
        "$2a$04$TcE7An0ghlNLq9.A.bBzc.e7z.csI2ko.PiXWLf4CXU5P6tW54Fnm",
        "$2a$04$f5GTlqZxvsH6sQj2oQ8NIuLbF0WwNw/hIzYcjGKj2lrqTL/wAI1oW",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "181826139": {
    _unsafe__answer: "speech,lecture,presentation",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$Y274NtdQylIMa0/ov1b79uCLNDuka3VUx9lCydvKGvXLg11E.Hj9.",
        "$2a$04$NbYSGeCIAqqn/kYv/zTKU.tWkk0OVebSYoNHjOUC1SSSGiT7.UH.e",
        "$2a$04$jggcv2E8SABJJVz6lrygZuGUVSliR4f/YqzHy1hOfaXEsQGGZ6hC.",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "181826143": {
    _unsafe__answer: "four",
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$NcjvnceLwVVcRFJVjtvX6eWn.9oxS6zYIyZbdYNZJTRL3Cw9EmUXW",
    },
  },
  "181826147": {
    _unsafe__answer: "fear,apprehension,dilemma,feelings,fright,plight",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$X.nGKdKaBIgc.pDwg8bmM.I.jToA3RvU/WdjQNqOA061GeH8DbprK",
        "$2a$04$XQFtffAMrD1mFSRx8F1B6es6zc.Lct12ULg5nfFdgYflCq3gL/p02",
        "$2a$04$5xUYpo.B4buUV1KO6K9Nwue7mr64h56JDrQGrvW37/WT.0pTLQg1y",
        "$2a$04$ZWPsGTwYPa2hn8q3AKz1k.ul3SES3il616wMbT4Vl9PersvZRcQQe",
        "$2a$04$QdKTMXgb4sm21LWH5setEe6MXdQCrsg3g1.m2DmzbBk7sRVtCcTSy",
        "$2a$04$NWwOYK.PpbHuuBSbajKAEeeimjYw2O6N4drulGIZd0MdYvyKysKpu",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "181826148": {
    _unsafe__answer: "money,investments,savings",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$bNqKsbbytE56MxJmwit9mO/SOTZLhjISToCc3xbnDPd0SMS8oHKLW",
        "$2a$04$EqRbUmFpH5RonSE2M0Fwyu2ZN3Y7lZpZ/jurA/TU2QUL5psq4Adty",
        "$2a$04$JncpqCdgvqtkQ60FHqjRGuMLHHIZu0K0gDgtEKRZQsoR38d4BIHTG",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "*********": {
    _unsafe__answer: "drivers,cars,driving,travel,travelers",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$Z.zKYP5hkovcjK2e0TadyOLGg2i8vvRpB9kG.DlAqobN3EUvA21V2",
        "$2a$04$9ZZr9kf70w.vqunyeJsaCuLf8faq/oxCoUllX0C1aZdMJDtE/zHr6",
        "$2a$04$WUDb40jcx9Fau8ZtUj4rDOl1lzJhjB2IjjTR6I2YKsFqfRegcdl1.",
        "$2a$04$ZrtlUABvBD10a7vRiSu2xuDsEIJeHWasGvrsVB6uITcMRUA.xItae",
        "$2a$04$LIanluMc7oXAuPxtgAiZLebXsZ8XCyE5sPzxiYKCIi02vPY.V87Ui",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "*********": {
    _unsafe__answer: "sun",
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$4gP2uIGgEWMBd1PRw0FhFuTPtxaYGMedAzqUq9kHxwNAyU3UvCfJO",
    },
  },
  "*********": {
    _unsafe__answer: "roots",
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$dWoIK8xH/IkNcQrJWjUDj.Q0Zt8UwWSStHF6IPulPqHzuHuMhcTLe",
    },
  },
  "181826157": {
    _unsafe__answer: "leaves",
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$h4QQWUUJcRe1ksQ8rWS9T.colwuivadVd879X1tQ8wOdFLwOJ4vgm",
    },
  },
  "182597969": {
    _unsafe__answer:
      "secretive,confidential,discreet,exclusive,mysterious,private, sneaky",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$lD7izSO2iKd5Kfk166v/1uzb5cqR7k3By31..NGoToFXD2jBebS2S",
        "$2a$04$06gjMomtTrkGCSwJH6eRweXdbDfnExpVnOm3HYqGqzz8TnLHdCBiS",
        "$2a$04$QBmlqlcNtzn1LAr1J2Zqpe4QGINsU1C.tjPB2Q8M5SSLkKQ3Pb0yu",
        "$2a$04$ZAAE9WlESW1NeyrdQqNIg.tAv/XiSiF3oP3EgVBv8X43lVncDZHEO",
        "$2a$04$2zDU9/FDTbMRMyUIItsVHecKx8n/hUIhoXcmxCX7MiiH0nwY9SxB6",
        "$2a$04$6WLb8qnJ/CMi3JXYVRjqyO2aQu1X3E20qM9thVu6QCVRpymaskMni",
        "$2a$04$ArOUpMnp3Y5eTuWKJ8KbQejIqpVviiWJk8bqnVbVSSA7OKC1t0ByC",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "182597974": {
    _unsafe__answer: "nomads,wanderers",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$Rof3mlZwUoEMOahRNdZbX.o8zKuVGx.4ki1HvavlT6HyrRPAajUqK",
        "$2a$04$4zXcBSM.1WO8tenmolg3GO1xLIRV5Ot.VrU0XuzMbjVEy2bCFkuGO",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "182597984": {
    _unsafe__answer: "help,support,volunteering",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$2bvcQ.m4Xivbx8cdiFrR8OWCD7clkjd7KgCnyh3or4.r/QsL2DAF.",
        "$2a$04$dsMrc.Q67q/MfjvWdT60KuhjAyG5tyKTgmTQwmue8pI6Hsov/.KWq",
        "$2a$04$DpE35zeDpzD0P4i.NqOhSO.6Z7o2sIdUR8ZblmblscNgR04YE80De",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "182597990": {
    _unsafe__answer: "ready,prepared,willing",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$JSBuClHAsm7e.H.FfL.3ju55tOdwOO71.GXnqgw0kCQF3VkwyjixC",
        "$2a$04$NydsD84NIEBX0BuG0NtsteV8Gb720pKjlccfjGRrTBN.pIcJq6l2G",
        "$2a$04$fEm/9yOQ2SUqrXf9oDMLcOZCXdhHH6CTF4eDJKhx.th7ohJAE3zre",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "182597996": {
    _unsafe__answer: "writing,keyboarding,typing",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$MwokcTf0/mgsZ2uhrBt6GO2OpZm9AeSHl.96mDI07IIVKwPRMEv5q",
        "$2a$04$snrQf3AYMAuYZB/rnS2e.OidpfXPTq8lGayKAjz48eES6RjrYeZFy",
        "$2a$04$nK/FKYk4clQg8kg7oe9vC.39TKTrWYvKjYCVhL5qg15Nkm1aHoE8.",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "182598092": {
    _unsafe__answer: "birds,chicks,penguins",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$39dvEgv4akO4Vc0F8AVwo.JqnBX8EQ5iBpKdb1q3FCoWQgiJY3kMu",
        "$2a$04$0S59zdj4zgw9YMqOvLiA.exL4QjfGGWxXkqyhilbFHTrdyRLsTRS.",
        "$2a$04$ojkTCtrMKjCZgHOQMD6WU.ownX9WmKqwRsFYPK62z1nQKEcM0anL.",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "182598100": {
    _unsafe__answer: "roots",
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$HUkDFnhYUDL93WOQ57.DnexA3.Rf5qxF79x0FoYqoQj8rJA9L/C3a",
    },
  },
  "182598107": {
    _unsafe__answer: "parasitic",
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$dGdiLh6SKsLjJqvqPX7/puFz7gmfBC/3ZRFQ2PxePQCRoQlkVG2xe",
    },
  },
  "182608819": {
    _unsafe__answer: "tides",
    responseType: "text",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$y/kDSpiGSbbzZAjGf9PGGuflU.UPI9WMJlGCU02bP4o/pVNo7HXaS",
    },
  },
  "182608889": {
    _unsafe__answer: "false,bogus,fake,falsified,fraudulent,unethical",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$LU6OOXB1bmoKxrX1GbddMeGvDctPB1uCHFgryNt9U96e7V99GATwq",
        "$2a$04$IgdhsnR.pQ8TDdcGQGo34OfXocs1Qv6Ixh3CIijeDuZHxj.lOIR0y",
        "$2a$04$FhWtbhtPm4x8WPrjG3MU3.W7e7kdjAc9W/ykVvWSYb7tCc2OFgXUW",
        "$2a$04$TXeAndRxNF3cB8FGNfHWfOKUuYLz0T2259Ii7uKPgfpoYxUz.6Cy.",
        "$2a$04$I73jW5ynBv6isN4F9x0oPen9L3W2UPIsO6yBNsogfaFNLoYZYhHeu",
        "$2a$04$4/MBkt/0.8skQ6Ujif6No.ZzemxzKW6go/pXCC9E8KXYDWJJZGGmy",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "182608892": {
    _unsafe__answer:
      "value,appeal,demand,impact,importance,necessity,need,popularity,relevance,significance, usefulness, utility, worth",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$s3MDDFnXcpdrX9vYasbBouYREr2kxs4VxO9tGQZBLj.bZ.EJTN1XW",
        "$2a$04$2IXijuZTRzwj0u9vA.axMOgM6lF0dHuzHNK6xm0z./Z4RuMG9xzNO",
        "$2a$04$XA0pLLPMYu3ujuSG/wxji.MpMo6FxPugH3kyAumMxiUZQimkiJZsy",
        "$2a$04$WsRp4zCZqai6WN2qQqpKY.WFXCW/.crWqb7nRiN/QoTBQCneAiZLK",
        "$2a$04$diEihwysQmS0Y/3Yw6E2lesUwFgWCnEifEARGAGgUgld3vhMTUfSq",
        "$2a$04$Ya.fcryCBg7.0a.JAXHxXuEujgNpj2XROOT/SFKDxcvq8w1t0wQfi",
        "$2a$04$JU1ZJaXs2yNwhKrcdi77xes0GeCTsENdV.aYDUFJEmPfpcvoxTPSW",
        "$2a$04$X8uP/cvKkFPelrQhreMVq.wjDiAtRWnDeL6mKIbSZb4mlc7BRBF3i",
        "$2a$04$QanOGKzozZR8Ug3OPxdvmej9LusFCojCP0ekPYRGq18WTfWXSejze",
        "$2a$04$fXe7R3QoO.jTKYL2in0lXuMVN91Km73k5lcxvZy7v/63mfUs9Gz.m",
        "$2a$04$ySvcBGobn2wzwafOgK7Z2eBfVL86oTjJYtjpFDN7ggE.0YkEwENK6",
        "$2a$04$Ik9TCTdD/l/i582ArGRtFuhYG.kcUpkdTTMjsY8i6E3B0dXiQNQMi",
        "$2a$04$fx1MPjZQ.Ofeyhw9veB2/u6dqFFnmOmHbCD2WHaDHAG.udCyFtcqO",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "182608895": {
    _unsafe__answer: "lawnmower,engine,mower,mowing,tractor",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$siV0.2f/LUi.1qR9h1zhU.7u5zxkdkm2/7.CkD7MnFScsur1Pgrny",
        "$2a$04$jjJSGkp0pCQPoLdAekIZX.1V2L29bwW.64mwfJXKuEn69clRsYDJG",
        "$2a$04$TAn6mPXdPLz7IhnQ/fSF2egx/Z78s9OVnZjSA0yBcViXcvZxz8Sw2",
        "$2a$04$fIedk7MFjsWxtyYRxej57.yvwNZFQPip.mPCMtr3xicpVwj3JdjiW",
        "$2a$04$3BJt0ZvKtok7iFnxBZoPJO4wI6Ii8GKpNZIGEzAgkqqI7FVm52CcS",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "182613067": {
    _unsafe__answer: "peaceful,gentle,subtle,underlying",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$pSU2bq6R0lEGy8silVYbQ.xSvqtbNLZF277.MESYfAlO6ufk0zy8m",
        "$2a$04$NQAMZbFoqAMcFuhp9Q4fCOhKTU6k0C8TB2iu3uLzy2Ui0WwuseeEK",
        "$2a$04$xlbEtTLfVqm/aa3LQM6nKuPSl4IfCNjsRolhFtqSJjGNs7wAJGObG",
        "$2a$04$DNKaZiPTy5LW.sDD2Uekp.hwbxgnpi6/9jIMvpgN9ABaQrXF73mGS",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "182613068": {
    _unsafe__answer:
      "regarding,about,concerning,criticizing,critiquing,describing,reviewing",
    responseType: "text",
    answer: {
      markAs: "match_any_in_range",
      values: [
        "$2a$04$1FPoCraJ9.oD2uSxjvVw2.Ii0C00dAkYYKCGW0boN/GhHA5KyKMuO",
        "$2a$04$jrjJkz0fJqrNcsqbafyTu.hzbI.h43IfTW8ccWhPVZw1NIslC/R5q",
        "$2a$04$t4D.AcKIUJsKMNyOuJEKuOlFjmqQEdunZQxgQNU0XcKkOYSIAbFWC",
        "$2a$04$/KgGWzNg81bCmBle5PdYu.Wr3o39q36pMLf1IpCExaQRz1ttcq1r6",
        "$2a$04$g9EowuTtHGPbDPSRvloWqObyMeCpiURedi0TfyDByvxKqTPMstxvG",
        "$2a$04$e7u93Fn9aJmNXXcxfznvd.qeJqReO.srtbZCuptfZ7v2qJt5eK0G2",
        "$2a$04$watnzw9M2WXxBfDTL5jSMuLrLE/aOF1d7X2UNuBasOwP7DyFrwltq",
      ],
      guide: {
        code: 3,
        description:
          "Receives answer value as a string, there are many correct answers",
      },
    },
  },
  "182873929": {
    _unsafe__answer: "Different",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$HFdUgpktNcZ0ppvg/pvV7ee9O.oN0rGl8mLuiZ1y.DVv779z/Gxga",
    },
  },
  "182873945": {
    _unsafe__answer: "Same",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$pVRH0wj3yiBtPkfXokuOf.ZBdF4ovKMSgZ0ansZDJYc3Msg/1gv76",
    },
  },
  "182873947": {
    _unsafe__answer: "Different",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$.4/KyfyW3Uv0.rgk3AxdLesWh0SR0.Z7od1Rh9CEq/M7uu1scAyjK",
    },
  },
  "182873948": {
    _unsafe__answer: "Different",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$767zW4/yI/2aY/c9zJCS5.8JVhJ19v15Tl0EDc5pqPThW58b6nSju",
    },
  },
  "182873950": {
    _unsafe__answer: "Same",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$p2abthYt8nUoCbj3H90ELelD9itGNQnWibGUyzs5.M/opusDW4d9y",
    },
  },
  "182873954": {
    _unsafe__answer: "Same",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$l3Xoi4257csKsRTvoQIJf.8flrVOeVjIuX4r/o4VD5BLf6pQyg0cm",
    },
  },
  "182873955": {
    _unsafe__answer: "Different",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$bj/AwUmjNEHNx/yNopm5oO.2VQU6XX7oDi9q1uYeavf//YBzVrAse",
    },
  },
  "182873968": {
    _unsafe__answer: "Same",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$Jb2M4rvq8MlnR8mkE9ifYOeDL6o2kxhKSbhs./S7xvC/OQ8oz9pg6",
    },
  },
  "182873970": {
    _unsafe__answer: "Different",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$X.vncBP9WtVDyJslKFYgQu3yu0Wnfq49PertWcZMdlIhbR6lNOGS6",
    },
  },
  "182873974": {
    _unsafe__answer: "Same",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$YSZQ0wBoigjBk3M6OQmJpevJmr5P0TnDQwuMmW0P4/Md74XIyBosG",
    },
  },
  "182873975": {
    _unsafe__answer: "Different",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$QnIkKCDFkhr.X77wcuCF..fG11klSzWtTgVp7iUEr7PninK1ygHcC",
    },
  },
  "182873977": {
    _unsafe__answer: "Different",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$uD8uePecO0R4Nxhay8AU8eA/LjTZ/LXYEu44X.BwET/w/Bsvar3Pq",
    },
  },
  "182873979": {
    _unsafe__answer: "Same",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$AYS5c5B3Fu1TShJ7Idr7sOmIcVCEG7PqI7jyatIlMymsBupdyz27C",
    },
  },
  "182873985": {
    _unsafe__answer: "Different",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$ohJBwOlSa1qD4eRop56Xi.faoPN3St7zgrKfeUeiDbLAXBamRdJe2",
    },
  },
  "182873986": {
    _unsafe__answer: "Same",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$KJUcM9sRWojmmHq1pR53gOcLMSuqgjCD3xxoKHHKd9YPJV43sFB6y",
    },
  },
  "182873988": {
    _unsafe__answer: "Different",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$30uiJMqA7Y3Mp9jdjvDRCeQwwcDnPKuug40PiLV8Uw4YKoBJva3Wq",
    },
  },
  "182873989": {
    _unsafe__answer: "Same",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$wcQZeYjolhz5IeXkyVVl4.aLAfkmvpQCRcerkf7EjEysK9Q24a2tq",
    },
  },
  "182873990": {
    _unsafe__answer: "Same",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$S/YC1kZl8N0SpL/Db3oqROu8C/E5gRA1Da7G6EDGZWYao0VhDV9L2",
    },
  },
  "182873991": {
    _unsafe__answer: "Different",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$6wiHVhmPzh/hmBfqd3gQr.Zmgkx0kZmcjxyPe4hy4pmvWd/pIQJLK",
    },
  },
  "182873993": {
    _unsafe__answer: "Same",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$t5DFwkKBpvJ824KYBF/l3OMjFEaucAK9XyZNBoubtEbWdNRVaAcqu",
    },
  },
  "182873994": {
    _unsafe__answer: "Different",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$7f2FpApypUIbuHVhjfH1OeuZ04.GHNPjDFsXWKLEjURn0oBLBAQ62",
    },
  },
  "182873995": {
    _unsafe__answer: "Different",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$Jy6aJO/.rL8FIcUOi9RGWuOJf7YrZ/qVqqKjfrqLtMAJSpZ2IZsOm",
    },
  },
  "182873998": {
    _unsafe__answer: "Different",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$NgFGs2QPXM2PPmu2waBOxuF4b6.v3pZaZg9edoMr6fSjkGRdrk0/.",
    },
  },
  "182874000": {
    _unsafe__answer: "Same",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$n7hy6c8X.ovCVjBr03lwk.Gs8oJ754DTC5x9MWjwc2RsiC86SZ8hy",
    },
  },
  "182874001": {
    _unsafe__answer: "Different",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$Y5bFfY6ugsymMT/YEkaRrerm/g/EchkASqb4YujUcC90R3JhNqsGm",
    },
  },
  "182874005": {
    _unsafe__answer: "Different",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$DDeIyMAe/PCTSoAnLnOR5OCGTbR/YGWeujO2yQfB6p1yACKiGird2",
    },
  },
  "182874007": {
    _unsafe__answer: "Different",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$932anOmiZ00imQX8rxms5.byj7.a3KtF4o4WAxjcxCGWQusA8QnWm",
    },
  },
  "182874008": {
    _unsafe__answer: "Different",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$CqO7Q2qvOpARmI7xE28qs.hWl9JxUqujdgMUsFj.8LXBSh283LgNm",
    },
  },
  "182874010": {
    _unsafe__answer: "Different",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$lHs6INySvlxlAvRXvvKzDerE0NmraOtt3KRIPBAESZgDnaludSyOi",
    },
  },
  "182874012": {
    _unsafe__answer: "Same",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$cQhiphtgy1CopwjnHiKQ/.3SQTI9dqXJg1Zpp965AC1TnFcco7k8K",
    },
  },
  "182874013": {
    _unsafe__answer: "Same",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$hH9GnXXes//VwP/BfVNeJuuxSFRNEXt39HACSroAdSkDSSU4MzATO",
    },
  },
  "182874014": {
    _unsafe__answer: "Different",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$fyiayp1QcQZSKw07NevrlOFg/6TiEZzfFkjzGXFl51fW/oRZFA/M6",
    },
  },
  "182874015": {
    _unsafe__answer: "Same",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$0onzRop5VDtq6fzcRdzZvOaMBFlBrzQvmLB1nq095QIkmRqN/OMGm",
    },
  },
  "182874017": {
    _unsafe__answer: "Different",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$XbCSAQoWFBs/a.3DM2OVBej9T/PblHhqaQG5AqJef1esMH3qtsLIa",
    },
  },
  "182874018": {
    _unsafe__answer: "Same",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$8l0S/xAcaSOyKN0dOcRZDuidWXSvDT7abK5ti.CmVx2yV6wnezAhO",
    },
  },
  "182874019": {
    _unsafe__answer: "Same",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$mNsJXxieAqIhCPwfMKFwB.WkEJHVQX1rNMQbx0U1XuLsFwYj4dsWK",
    },
  },
  "182874020": {
    _unsafe__answer: "Same",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$ieflJwGG7M6aVnSFAdKdfez4/5z2JzmlDrIAlIdhxMGtuigwBFXHO",
    },
  },
  "182874021": {
    _unsafe__answer: "Different",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$jHpE7FRDLi0OUVs34bWOnu2Y8lkjmPP8UPw03e8OBEQpR65Bf7NT.",
    },
  },
  "182874022": {
    _unsafe__answer: "Same",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$UXZGmpvryRs2cVTD9qjDb.PW5OYsYlQmXvM4JU.dEjOlOoWd0Vqkq",
    },
  },
  "182874023": {
    _unsafe__answer: "Same",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$TLlaowFb/vqvsvhSbd1kf.3xl0PUhFptffocm3EJdJjOfzDv0Kqhe",
    },
  },
  "182874029": {
    _unsafe__answer: "Different",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$jBySKscXRJnoKaTQsjI2ueHY6t0.u7rkiIt9E0acuViDX2mQpzy3K",
    },
  },
  "182874031": {
    _unsafe__answer: "Same",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$S0sFCXJQkTeq8RwW2r58jeeHlD9ep765ekfy/yRm3E8DKnvs3cawS",
    },
  },
  "182874034": {
    _unsafe__answer: "Same",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$It448kTFh6EHqlPRAzutc.3j3yn83XmtCDDPjbdWGafHp/NI0Z2pW",
    },
  },
  "182874037": {
    _unsafe__answer: "Different",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$g5J/hBnWDu.0OJF7/fbjp.plm107hgYeDf8bR4hjab30k7sTrv9jG",
    },
  },
  "182874039": {
    _unsafe__answer: "Same",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$WU7pbsKuxwCMOJPpk3oqhOzBtVpDVPv3Q.FaXx9T3p.cSstVIrAj2",
    },
  },
  "182874040": {
    _unsafe__answer: "Same",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$p72g1f3qu1wymq0Y.lVE/.qgD6NwGUpkHR3aeBA0DSiK5FbPosu8C",
    },
  },
  "182874041": {
    _unsafe__answer: "Different",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$xKC.uHWswhjdxMOON.0AQe8UcSgVozPvDxwXyB0Gp/GpoBd9C3Zdu",
    },
  },
  "182874043": {
    _unsafe__answer: "Different",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$8i8a6nOeEjaWBOda2fHBaOKPbJEfQNnJd7LBED2dBZJrauEZma1X6",
    },
  },
  "182874044": {
    _unsafe__answer: "Same",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$qoq0oSisvdbbgCMJLTltde4l5XMqLZZ5QOhAgtmtt1pCKlWnO0KxC",
    },
  },
  "182874045": {
    _unsafe__answer: "Different",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$UBEKOHo/uCS7OQ.hK8RYleKrwjaD/STp.ygyc4HcyqxqY9grYynsq",
    },
  },
  "182874049": {
    _unsafe__answer: "Same",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$bXWeLRfTZ8MMFOwrR5hFteegW6qXvu9YuKKQ3N3uOZMJWosAS7nEC",
    },
  },
  "182874050": {
    _unsafe__answer: "Same",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$o3D7EbTrWDyzGAs20f/6JukxEdNJ67LLqz2Jfyb3HxpXI.crhult2",
    },
  },
  "182874051": {
    _unsafe__answer: "Different",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$YXZ.jF3c1YivVPLN4XGbE.l5Tw687saYekOqMaeeyw3fZhwAHQ4zC",
    },
  },
  "182874061": {
    _unsafe__answer: "Different",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$/WlJMUaxowHN.42IPeg9Eu..jSvfGJmw7//YVDlKbrQJwa7lLFEWa",
    },
  },
  "182874062": {
    _unsafe__answer: "Same",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$rR9BadKZDnbrXVf1Nye9Lup13cCz8Klg.XLnAP5YO/qKaeXRX4Gee",
    },
  },
  "182874063": {
    _unsafe__answer: "Different",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$3ZSEdg19TWK0t8MNDa4WO.7h3F5u2CdiU5s4s1cf.OOQpO2adzqFW",
    },
  },
  "182874064": {
    _unsafe__answer: "Different",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$tnBiVZvKZTVHIlDocyR2ou8pF2wv0iieqp.iNWCCODkj5A07zQNRy",
    },
  },
  "182874080": {
    _unsafe__answer: "Same",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$uPs18Z9nAQpB5YyyKgGT5.DyTOA4ebD5WFH347035e9x19wga2pFu",
    },
  },
  "182874081": {
    _unsafe__answer: "Same",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$JaXBRfUIwhjE8Iz1p/LJ5eBKhSvik7QuFECLCdQBbI9ywrjYSyzQu",
    },
  },
  "182874086": {
    _unsafe__answer: "Different",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$3Of/OtTF5IplLzYconNjmevmjp..TpQWTykWTXgxddivvH7lh8yi6",
    },
  },
  "182874090": {
    _unsafe__answer: "Different",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$9oZY/tOvtwNYeVnTq/BcdOLV17qWeZ.0wmAiO5jJjbA0FW9M.UlFm",
    },
  },
  "182874091": {
    _unsafe__answer: "Same",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$w0N88KZwLuA.s7ceVHS2nel69ptv4YV/gZcpPDFW5wT7Ee0nRaoJ2",
    },
  },
  "182874092": {
    _unsafe__answer: "Different",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$Y54ceMj0xStrF.Z0YnnlluzIWklZP03V4rks6KRjBxBDHHaOjvSWy",
    },
  },
  "182874093": {
    _unsafe__answer: "Same",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$gkG5OzTPxT33E3B2oCW6Werad3hsRGMz7eytKOhiH9dKKPspGz9X2",
    },
  },
  "182874112": {
    _unsafe__answer: "Different",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$2bxMJfGL7B8TDIa9qxNWm.YEKfmNJaV4qdJ1OH./Al/L/temnL.8q",
    },
  },
  "182874114": {
    _unsafe__answer: "Different",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$uyH2n2NHeNyoVesV4.U0Q.EJ9fVsHkK.18zjTLhqG6uu4PDRWP30q",
    },
  },
  "182874115": {
    _unsafe__answer: "Same",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$S0FeTCe0O28xd.qstvo.1u7byuGbV3Fg0obFwsVN9.bYL9iOhefs6",
    },
  },
  "182874142": {
    _unsafe__answer: "Same",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$KhrmHUI/WpMGskzy.9qNreULoqU0E9kvWspaZjXWPunoSQn/RqfkC",
    },
  },
  "182874144": {
    _unsafe__answer: "Same",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$IuTpzmYL/3I7mIld0KNSGemp3C7oVCuIP23KOYmLtfOlZyEjmcGza",
    },
  },
  "182874145": {
    _unsafe__answer: "Different",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$LYNdhNHwLf//Qkc29rONjuKwCbcQrEMXuVaW2.UGEZdAVKCiL0v0O",
    },
  },
  "182874148": {
    _unsafe__answer: "Same",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$rHpuu9l0lE5CETQrD2Sw4.sxqZwKTcqhqOe8cK9gIz4waKg5Lkj0.",
    },
  },
  "182874151": {
    _unsafe__answer: "Same",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$iksuPLLVGuZ8sMHPxu0Xh.nCPKyGWp6lHh5GCNGfeICRcqLQXa1Ka",
    },
  },
  "182874152": {
    _unsafe__answer: "Same",
    responseType: "binary",
    answer: {
      markAs: "single_exact_match",
      values: "$2a$04$Lfkx9j6px7BT3pM6/xB/zONjOE7/wY0X21leEtRCNhqyf.ksTmaA6",
    },
  },
};
