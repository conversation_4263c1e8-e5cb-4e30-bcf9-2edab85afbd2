"use server";

import { createClient } from "@supabase/supabase-js";

import { Org } from "@/components/Admin/AddOrg/AddOrgForm";
import { BASE_URL } from "@/constants/constants";
import { supabaseConfig } from "@/data/constants";
import { AdminRole, InviteStatus } from "@/types/admin";

export const addOrg = async (org: Org) => {
  const supabase = createClient(
    supabaseConfig.supabaseUrl,
    supabaseConfig.supabaseKey
  );
  const tableName = "organisation";

  const { data: orgData, error: orgError } = await supabase
    .from(tableName)
    .insert({
      org_name: org.organisationName,
    })
    .select("org_id")
    .single();

  if (!orgData || orgError) {
    return null;
  }

  const orgId = orgData["org_id"];

  const findUserByEmail = async (email) => {
    const { data } = await supabase
      .from("admins")
      .select("user_id, id")
      .eq("email", email)
      .single();

    return data ? data.user_id : null;
  };

  let userId = await findUserByEmail(org.email);

  if (!userId) {
    const { data: invitedUser, error: invitedUserError } =
      await supabase.auth.admin.inviteUserByEmail(org.email, {
        redirectTo: `${BASE_URL}/auth/confirm`,
        data: { orgType: tableName, orgId },
      });

    if (invitedUserError || !invitedUser) {
      return null;
    }

    userId = invitedUser.user.id;
  }

  const { data: adminUserData, error: adminUserError } = await supabase
    .from("admins")
    .upsert({
      user_id: userId,
      name: org.contactName,
      email: org.email,
      permissions: AdminRole.OrgAdmin,
      invite_status: InviteStatus.Pending,
    })
    .select("id")
    .single();

  if (adminUserError || !adminUserData) {
    console.error("Error in upserting admin user:", adminUserError);
    return null;
  }

  const adminId = adminUserData.id;

  //Add user to organisations table
  const { error: updateError } = await supabase
    .from(tableName)
    .update({ admin_id: adminId })
    .match({ org_id: orgId });

  if (updateError) {
    console.error("Error updating organisation  with admin user:", updateError);
    return null;
  }

  if (adminUserError) {
    return null;
  }

  return orgId;
};
