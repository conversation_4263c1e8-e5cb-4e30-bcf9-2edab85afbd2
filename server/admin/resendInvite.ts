"use server";

import { createClient } from "@supabase/supabase-js";

import { BASE_URL } from "@/constants/constants";
import { supabaseConfig } from "@/data/constants";
import { sendEmail } from "@/pages/api/brevo/send-email";

type ResendInviteProps = {
  email: string;
};

export const resendInvite = async ({ email }: ResendInviteProps) => {
  const supabase = createClient(
    supabaseConfig.supabaseUrl,
    supabaseConfig.supabaseKey
  );

  const findVerificationCodeByEmail = async (email) => {
    const { data } = await supabase
      .from("admins")
      .select("user_id, id, verification_code")
      .eq("email", email)
      .single();

    return data ? data.verification_code : null;
  };

  const verificationCode = await findVerificationCodeByEmail(email);

  //Send verification email
  await sendEmail({
    email: email,
    type: "schoolVerification",
    params: {
      verificationCode,
    },
  });

  return true;
};
