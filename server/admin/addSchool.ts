"use server";

import { createClient } from "@supabase/supabase-js";

import { School } from "@/components/Admin/AddSchool/AddSchoolForm";
import { supabaseConfig } from "@/data/constants";
import { sendEmail } from "@/pages/api/brevo/send-email";
import { AdminRole, InviteStatus } from "@/types/admin";
import { generateVerificationCode } from "@/utils/admin/generate-verification-code";

export const addSchool = async (school: School) => {
  const verificationCode = generateVerificationCode();

  const supabase = createClient(
    supabaseConfig.supabaseUrl,
    supabaseConfig.supabaseKey
  );
  const tableName = "school";

  const organizationId = school.organisation
    ? parseInt(school.organisation, 10)
    : null;

  const { data: orgData, error: orgError } = await supabase
    .from(tableName)
    .insert({
      org_id: organizationId,
      school_name: school.schoolName,
      location: school.location,
      school_type: school.type,
      school_age: school.age,
    })
    .select("school_id")
    .single();

  if (!orgData || orgError) {
    return null;
  }

  const orgId = orgData[`school_id`];

  const findUserByEmail = async (email) => {
    const { data } = await supabase
      .from("admins")
      .select("user_id, id")
      .eq("email", email)
      .single();

    return data ? data.user_id : null;
  };

  let userId = await findUserByEmail(school.email);

  if (!userId) {
    const { data: invitedUser, error: invitedUserError } =
      await supabase.auth.signUp({
        email: school.email,
        password: `${process.env
          .NEXT_PUBLIC_STUDENT_PASSWORD!}_school_${verificationCode}`,
        options: {
          data: {
            orgType: tableName,
            orgId,
            verificationCode: verificationCode,
          },
        },
      });

    if (invitedUserError || !invitedUser) {
      return null;
    }

    userId = invitedUser?.user?.id;

    //Send verification email
    await sendEmail({
      email: school.email,
      type: "schoolVerification",
      params: {
        verificationCode: verificationCode,
      },
    });
  }

  const { data: adminUserData, error: adminUserError } = await supabase
    .from("admins")
    .upsert({
      user_id: userId,
      name: school.contactName,
      email: school.email,
      permissions: AdminRole.SchoolAdmin,
      invite_status: InviteStatus.Pending,
      verification_code: verificationCode,
    })
    .select("id")
    .single();

  if (adminUserError || !adminUserData) {
    console.error("Error in upserting admin user:", adminUserError);
    return null;
  }

  const adminId = adminUserData.id;

  //Add user to schools table
  const { error: updateError } = await supabase
    .from(tableName)
    .update({ admin_user: adminId })
    .match({ school_id: orgId });

  if (updateError) {
    console.error("Error updating school with admin user:", updateError);
    return null;
  }

  if (adminUserError) {
    return null;
  }

  return orgId;
};
