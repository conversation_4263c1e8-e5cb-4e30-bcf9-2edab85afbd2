"use client";

import { Box } from "@chakra-ui/react";
import React, { useEffect, useState } from "react";

import { theme } from "@/styles/theme";

interface Star {
  id: number;
  x: number;
  y: number;
  size: number;
  rotation: number;
  opacity: number;
}

export interface StarsBgProps {
  numberOfStars?: number;
  bgColor?: string;
  zIndex?: number;
}

export const StarsBg = ({
  numberOfStars = 100,
  bgColor = theme.colors.secondary.purple_01.hex,
  zIndex = 10,
}: StarsBgProps) => {
  const [stars, setStars] = useState<Star[]>([]);
  const [activeStar, setActiveStar] = useState<number | null>(null);

  useEffect(() => {
    const generateStars = (count: number): Star[] => {
      return Array.from({ length: count }, (_, index) => ({
        id: index,
        x: Math.random() * window.innerWidth,
        y: Math.random() * window.innerHeight,
        size: Math.random() * 18,
        rotation: Math.random() * 360,
        opacity: Math.random(),
      }));
    };

    setStars(generateStars(numberOfStars));

    const interval = setInterval(() => {
      const randomStarIndex = Math.floor(Math.random() * numberOfStars);
      setActiveStar(randomStarIndex);
    }, 2700);

    return () => clearInterval(interval);
  }, [numberOfStars]);

  return (
    <Box
      pos="fixed"
      width="100vw"
      height="100vh"
      top={0}
      left={0}
      bgColor={bgColor}
      zIndex={zIndex}
    >
      {stars.map((star, index) => (
        <svg
          key={star.id}
          data-testid={index === activeStar ? "active-star" : "star"}
          width={star.size}
          height={star.size}
          style={{
            position: "absolute",
            left: star.x,
            top: star.y,
            opacity: star.id === activeStar ? 1 : star.opacity,
            transform:
              `rotate(${star.rotation}deg)` +
              (star.id === activeStar ? " scale(1.6)" : ""),
            transition: "transform 1s, opacity 1s, fill 1s",
            fill: star.id === activeStar ? "yellow" : "white",
            filter:
              star.id === activeStar ? "drop-shadow(0 0 10px yellow)" : "none",
          }}
          viewBox="0 0 17 14"
        >
          <path
            d="M10.4028 5.00158L8.51191 1.0686L6.24288 5.00158L1.89392 6.80419L6.24288 8.93455L8.51191 12.5398L10.4028 8.93455L15.1299 6.80419L10.4028 5.00158Z"
            stroke="white"
            strokeOpacity="0.1"
          />
        </svg>
      ))}
    </Box>
  );
};
