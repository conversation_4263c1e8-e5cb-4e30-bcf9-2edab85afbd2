import { Box, Card, Input, VStack } from "@chakra-ui/react";
import { useSupabaseClient } from "@supabase/auth-helpers-react";
import Head from "next/head";
import Link from "next/link";
import { useRouter } from "next/router";
import posthog from "posthog-js";
import React, { useContext, useEffect, useState } from "react";

import { Text } from "@/components/v2/Text/Text";
import { useStudentStore } from "@/context/student-store";
import { UserContext } from "@/context/user-context";
import { AuthLayout } from "@/layouts";
import { theme } from "@/styles/theme";
import { CreditStatus } from "@/types/admin";
import { calculateAge } from "@/utils/students/calculateAge";

import { Button } from "../v2";
import { Loader } from "../v2/Loader/Loader";

export const StudentLogin: React.FC = () => {
  const [authError, setAuthError] = useState(false);
  const [isLoggingIn, setIsLoggingIn] = useState(false);
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [email, setEmail] = useState("");
  const { user: userContext } = useContext(UserContext);
  const supabase = useSupabaseClient();
  const resetStudentStore = useStudentStore((state) => state.reset);
  const { push } = useRouter();

  const {
    setFirstName,
    setCredits,
    setDateOfBirth,
    setAge,
    firstName,
    credits,
  } = useStudentStore();

  const isStudent = userContext?.metadata?.is_student;
  const primaryColor = authError
    ? theme.colors.ui.alert_red_01.hex
    : theme.colors.primary.purple.hex;

  useEffect(() => {
    if (firstName) {
      setIsLoggedIn(true);
    }
  }, [firstName, credits]);

  useEffect(() => {
    const checkSession = async () => {
      const { data } = await supabase.auth.getSession();
      if (!data?.session) {
        console.log("Resetting the student store");
        setIsLoggedIn(false);
        resetStudentStore();
      }
    };

    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange((event, session) => {
      if (event === "SIGNED_OUT" || !session) {
        console.log("Resetting the student store");
        setIsLoggedIn(false);
        resetStudentStore();
      }
    });

    checkSession();

    return () => {
      subscription?.unsubscribe();
    };
  }, [supabase, resetStudentStore]);

  async function handleSignIn(email: string) {
    setIsLoggingIn(true);
    const response = await supabase.auth.signInWithPassword({
      email: `${email}@talamo.co.uk`,
      password: process.env.NEXT_PUBLIC_STUDENT_PASSWORD as string,
    });

    if (response?.data?.session?.access_token) {
      const studentId = response?.data?.user?.user_metadata?.student_id;
      posthog.identify(response?.data?.user.email, {
        userId: response?.data?.user.id,
      });

      const { data: studentData, error: studentError } = await supabase
        .from("student")
        .select("first_names, date_of_birth")
        .eq("student_id", studentId)
        .single();

      const { data: creditsData, error: creditsError } = await supabase
        .from("credits")
        .select("status, assessment_id")
        .eq("student_id", studentId);

      if (studentError || creditsError) {
        setAuthError(true);
        console.error("Error fetching data:", studentError || creditsError);
      } else {
        const dateOfBirth = new Date(studentData.date_of_birth);
        const studentAge = calculateAge(studentData.date_of_birth);

        setFirstName(studentData.first_names);
        setCredits(creditsData);
        setDateOfBirth(dateOfBirth);
        setAge(studentAge);
        setIsLoggedIn(true);
      }

      setIsLoggingIn(false);
    } else {
      setIsLoggedIn(false);
      setAuthError(true);
      setIsLoggingIn(false);
      console.error(response?.error?.message);
    }
  }

  const handleStart = () => {
    push(`/assessment/${credits[0].assessment_id}`);
  };

  const logout = () => {
    push("/logout");
  };

  return (
    <>
      <Head>
        <title>Login</title>
      </Head>
      {isLoggingIn ? (
        <Loader variant="huzzah" loadingText="Thinking" isActive />
      ) : (
        <>
          <AuthLayout student>
            <Card
              w={{ sm: "100%", md: "464px" }}
              border="none"
              borderRadius={theme.border.radius.md.px}
              bg={theme.colors.primary.white.hex}
              flex={1}
              overflow="hidden"
              variant="outline"
              p={"32px"}
              pos="relative"
              css={{
                boxShadow: theme.shadow.box,
              }}
            >
              {!isStudent && (
                <>
                  <VStack mb={theme.spacing.sm.rem}>
                    <Text
                      element="h3"
                      variant="xs"
                      textAlign="left"
                      alignSelf="flex-start"
                      m={0}
                      color={authError ? primaryColor : "black"}
                    >
                      ID
                    </Text>
                    <Input
                      _placeholder={{
                        color: "black",
                        fontWeight: "500",
                      }}
                      fontWeight="500"
                      css={{
                        padding: "20px 12px",
                        border: "2px solid " + primaryColor,
                        minWidth: "164px",
                        flex: 1,
                      }}
                      type="email"
                      onChange={(e) => setEmail(e.target.value)}
                    />
                    {authError && (
                      <Text
                        element="p"
                        variant="sm"
                        textAlign="left"
                        alignSelf="flex-start"
                        m={0}
                        color={primaryColor}
                      >
                        ID not found, try again or ask for help
                      </Text>
                    )}
                  </VStack>
                  <Box>
                    <VStack>
                      <Button
                        mt={theme.spacing.md.px}
                        onClick={() => handleSignIn(email)}
                      >
                        Login
                      </Button>
                    </VStack>
                  </Box>
                </>
              )}
              {isLoggedIn &&
                (credits.length === 0 ||
                  credits[0].status === CreditStatus.Redeemed) && (
                  <>
                    <Text
                      element="h2"
                      variant="xl"
                      textAlign="center"
                      m={0}
                      color="black"
                    >
                      Hi, {firstName} 👋
                    </Text>
                    <Text
                      element="p"
                      variant="lg"
                      textAlign="center"
                      m={0}
                      mt={theme.spacing.xs.px}
                      color="black"
                    >
                      Nothing to do today. Check with your teacher if you’re
                      supposed to be doing an activity today.
                    </Text>
                    <Button
                      size="lg"
                      color="red"
                      mt={theme.spacing.lg.px}
                      onClick={() => logout()}
                      alignSelf="center"
                    >
                      Logout
                    </Button>
                  </>
                )}

              {isLoggedIn &&
                credits.length > 0 &&
                credits[0].status !== "Redeemed" && (
                  <>
                    <Text
                      element="h2"
                      variant="xl"
                      textAlign="center"
                      m={0}
                      color="black"
                    >
                      Hi, {firstName} 👋
                    </Text>
                    <Text
                      element="p"
                      variant="lg"
                      textAlign="center"
                      m={0}
                      mt={theme.spacing.xs.px}
                      color="black"
                    >
                      When you’re ready, click Start!
                    </Text>
                    <Button
                      size="lg"
                      mt={theme.spacing.lg.px}
                      onClick={() => handleStart()}
                      alignSelf="center"
                    >
                      Start
                    </Button>
                  </>
                )}
            </Card>
            {isLoggedIn &&
              credits.length > 0 &&
              credits[0].status !== "Redeemed" && (
                <Text
                  element="p"
                  variant="md"
                  textAlign="center"
                  m={0}
                  mt={theme.spacing.md.px}
                  color="white"
                >
                  If this isn’t you,{" "}
                  <Link
                    href="/logout"
                    style={{ fontWeight: "500", textDecoration: "underline" }}
                  >
                    logout
                  </Link>{" "}
                  and try again
                </Text>
              )}
          </AuthLayout>
        </>
      )}
    </>
  );
};
