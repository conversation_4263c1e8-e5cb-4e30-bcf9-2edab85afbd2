import { Button, HStack } from "@chakra-ui/react";
import { useRouter } from "next/router";
import { IoArrowBackOutline } from "react-icons/io5";

import { theme } from "@/styles/theme";

export function Back({ label, route }: { label?: string; route?: string }) {
  const router = useRouter();
  return (
    <HStack
      justifyContent={"flex-start"}
      alignItems="center"
      mb={theme.spacing.sm.rem}
    >
      <Button
        onClick={() => (route ? router.push(route) : router.back())}
        css={{
          textDecoration: "underline",
          color: theme.colors.primary.black.hex,
          fontWeight: 600,
          opacity: 0.7,
          ":hover": {
            opacity: 1,
            color: theme.colors.primary.purple.hex,
          },
        }}
        p={0}
        variant={"tertiary"}
      >
        {label || "Back"}
      </Button>
    </HStack>
  );
}
