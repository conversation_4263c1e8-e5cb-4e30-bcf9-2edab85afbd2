import { hasCookie, setCookie } from "cookies-next";
import { useEffect, useState } from "react";

import { theme } from "@/styles/theme";

import { Modal } from "../v2/Modal/Modal";

export const CookieConsent = () => {
  const [showConsent, setShowConsent] = useState(true);
  const cookieAge = 15552000; //age in seconds

  useEffect(() => {
    setShowConsent(hasCookie("cookieConsent"));
  }, []);

  const acceptCookie = () => {
    setShowConsent(true);
    setCookie("cookieConsent", "true", { maxAge: cookieAge });
  };

  const declineCookie = () => {
    setShowConsent(true);
    setCookie("cookieConsent", "false", { maxAge: cookieAge });
  };

  if (showConsent) {
    return null;
  }

  const consentText =
    '<p>Talamo.co.uk uses cookies - some are strictly necessary to run the site but below are the optional ones:</p><ul><li>Used for measuring how the site is used</li><li>For advertising marketing and social media</li></ul><p>Read more on our <a href="https://www.talamo.co.uk/cookie-policy" target="_blank">cookie policy</a></p>';

  return (
    <>
      <Modal
        title="Cookies"
        isOpen={true}
        setIsOpen={() => {}}
        primaryCtaText="Decline"
        primaryCtaColor="whiteOutline"
        primaryCtaVariant="secondary"
        onPrimaryCtaClick={() => {
          declineCookie();
        }}
        description={consentText}
        secondaryCtaText="Accept all"
        secondaryCtaColor="purple"
        secondaryCtaVariant="primary"
        onSecondaryCtaClick={() => {
          acceptCookie();
        }}
      />
    </>
  );
};
