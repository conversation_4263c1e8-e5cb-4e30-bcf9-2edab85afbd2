"use client";

import { Box, FormLabel, HStack, Input, useToast } from "@chakra-ui/react";
import { useFormik } from "formik";
import type { Asserts } from "yup";
import * as Yup from "yup";

import { Button } from "@/components/v2/Button/Button";
import { Modal } from "@/components/v2/Modal/Modal";
import { Text } from "@/components/v2/Text/Text";
import { useAddCreditsMutation } from "@/hooks/mutations/useAddCreditsMutation";
import { theme } from "@/styles/theme";

type AddCreditsProps = {
  schoolId: number;
  orgId?: number;
  isModalOpen: boolean;
  setIsModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
};

const creditsValidation = Yup.object().shape({
  numCredits: Yup.number().required("Required"),
});

export type Credits = Asserts<typeof creditsValidation>;

const AddCredits = ({
  isModalOpen,
  setIsModalOpen,
  schoolId,
  orgId,
}: AddCreditsProps) => {
  const { mutate: addCredits } = useAddCreditsMutation();

  const toast = useToast();
  const formik = useFormik({
    initialValues: {
      numCredits: 0,
    },
    validationSchema: creditsValidation,
    onSubmit: async ({ numCredits }) => {
      addCredits(
        { schoolId, orgId, numCredits },
        {
          onSuccess: () => {
            setIsModalOpen(false);
            toast({
              title: "Credits added",
              description:
                "The credits have successfully applied to this account",
              status: "success",
              duration: 5000,
              isClosable: true,
            });
          },
          onError: (error) => {
            console.error("Mutation error:", error);
          },
        }
      );
    },
  });
  return (
    <div>
      <Modal
        isOpen={isModalOpen}
        setIsOpen={setIsModalOpen}
        title="Add credits"
        overlayBackground={`rgba(${theme.colors.secondary.purple_01.rgbRaw}, 0.9)`}
        showCloseBtn
        size="xl"
      >
        <form onSubmit={formik.handleSubmit}>
          <Box display="flex" flexDir="column" gap="24px">
            <HStack gap="20px">
              <Box flex="1">
                <HStack
                  alignItems="center"
                  justifyContent="space-between"
                  mb="10px"
                >
                  <FormLabel htmlFor="schoolName" m={0}>
                    Credits
                  </FormLabel>{" "}
                  {formik.touched.numCredits && formik.errors.numCredits && (
                    <Text
                      element="p"
                      variant="xxs"
                      color={theme.colors.ui.alert_red_01.rgb}
                    >
                      {formik.errors.numCredits}
                    </Text>
                  )}
                </HStack>
                <Input
                  id="numCredits"
                  name="numCredits"
                  type="number"
                  onChange={formik.handleChange}
                  value={formik.values.numCredits}
                />
              </Box>
            </HStack>
          </Box>
          <Box mt="30px" display="flex" justifyContent="center">
            <Button type="submit">Add</Button>
          </Box>
        </form>
      </Modal>
    </div>
  );
};

export default AddCredits;
