"use client";

import { ExternalLinkIcon } from "@chakra-ui/icons";
import { Box } from "@chakra-ui/react";
import Link from "next/link";

import { RiskCard } from "@/components/RiskCard/RiskCard";
import { Loader } from "@/components/v2/Loader/Loader";
import { Text } from "@/components/v2/Text/Text";
import { useAnalysisByIdQuery } from "@/hooks/queries/useAnalysisByIdQuery";
import { theme } from "@/styles/theme";
import { GeneralResultsQuery, RiskLevelsQuery } from "@/types/graphql/graphql";

export const LevelOfNeed: React.FC<{
  studentCode: string;
  isAdmin?: boolean;
  riskLevels: RiskLevelsQuery;
  generalResults: GeneralResultsQuery;
}> = ({ studentCode, isAdmin = false, riskLevels, generalResults }) => {
  const { data: reportData } = useAnalysisByIdQuery({ studentCode });

  if (!reportData) return <Loader />;

  const levelOfNeedRisk = reportData.analysis.levelOfNeedRisk;
  const dyslexiaRisk = reportData.analysis.dyslexiaRisk;
  const studentName = reportData.student.first_names;

  if (reportData?.hide_report && !isAdmin) {
    return null;
  }

  const levelOfNeedCards = riskLevels.allRiskLevels.filter(
    ({ riskType, requiredRiskLevel }) =>
      riskType === "levelOfNeed" && requiredRiskLevel === levelOfNeedRisk
  );
  const dyslexiaCards = riskLevels.allRiskLevels.filter(
    ({ riskType, requiredRiskLevel }) =>
      riskType === "dyslexia" && requiredRiskLevel === dyslexiaRisk
  );

  const replaceStudentName = (text: string): string =>
    text.replace(/{{name}}/g, studentName);

  const risksLinkTitle = generalResults.general?.risksLinkTitle;
  const risksLinkUrl = generalResults.general?.risksLinkUrl;

  return (
    <Box display="flex" flexDirection="column" gap={theme.spacing.md.px}>
      <Box display="flex" alignItems="center" justifyContent="space-between">
        <Text element="h4" variant="xl">
          Risks
        </Text>
        {risksLinkUrl && risksLinkTitle ? (
          <Link href={risksLinkUrl} target="_blank">
            <Box
              display="flex"
              alignItems="center"
              gap={theme.spacing.xs.px}
              cursor="pointer"
            >
              <Text
                element="h5"
                variant="sm"
                color={theme.colors.ui.link.hex}
                cursor="pointer"
                _hover={{ textDecoration: "underline" }}
              >
                {risksLinkTitle}
              </Text>
              <ExternalLinkIcon
                width={"20px"}
                height={"20px"}
                color={theme.colors.ui.link.hex}
              />
            </Box>
          </Link>
        ) : null}
      </Box>

      {levelOfNeedCards.map((level) => (
        <RiskCard
          key={level.id}
          level={level.requiredRiskLevel}
          heading={replaceStudentName(level.title)}
          description={replaceStudentName(level.description)}
        />
      ))}
      {dyslexiaCards.map((level) => (
        <RiskCard
          key={level.id}
          level={level.requiredRiskLevel}
          heading={replaceStudentName(level.title)}
          description={replaceStudentName(level.description)}
        />
      ))}
    </Box>
  );
};
