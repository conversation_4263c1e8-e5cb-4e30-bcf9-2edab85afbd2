"use client";

import { Box, Flex } from "@chakra-ui/react";

import { InstructorIcon } from "@/components/v2";
import { Text } from "@/components/v2/Text/Text";
import { theme } from "@/styles/theme";
import { ReportRecommendationsQuery } from "@/types/graphql/graphql";

export const HighPriority: React.FC<{
  recommendation: ReportRecommendationsQuery["allRecommendations"][0];
}> = ({ recommendation }) => {
  return (
    <Box
      bg={theme.colors.secondary.purple_05.hex}
      p={theme.spacing.sm.px}
      borderRadius={theme.border.radius.lg.px}
      display="flex"
      alignItems="center"
      gap={theme.spacing.sm.px}
    >
      <Flex alignItems="center" mr={2} gap={2}>
        <InstructorIcon
          bgColor={theme.colors.secondary.purple_03.hex}
          width={40}
          height={40}
        />
      </Flex>
      <Text
        element="h4"
        variant="sm"
        color={theme.colors.secondary.purple_01.hex}
      >
        <div dangerouslySetInnerHTML={{ __html: recommendation.title || "" }} />
      </Text>
    </Box>
  );
};
