"use client";

import { Box } from "@chakra-ui/react";
import { ArrowPathIcon } from "@heroicons/react/24/solid";
import { useRouter } from "next/navigation";
import { useState } from "react";

import { AnalysisChart } from "@/components/Admin/AnalysisChart/AnalysisChart";
import { AnalysisTableView } from "@/components/Admin/AnalysisChart/AnalysisTableView";
import { Button, Panel } from "@/components/v2";
import { Text } from "@/components/v2/Text/Text";
import { useAnalysisByIdQuery } from "@/hooks/queries/useAnalysisByIdQuery";
import { theme } from "@/styles/theme";

export const AnalysisWrapper: React.FC<{
  studentCode: string;
  isAdmin?: boolean;
}> = ({ studentCode, isAdmin = false }) => {
  const router = useRouter();
  const [dataView, setDataView] = useState<"chart" | "table">("chart");
  const { data: reportData, isFetched } = useAnalysisByIdQuery({
    studentCode,
  });

  const toggleView = () => {
    setDataView(dataView === "chart" ? "table" : "chart");
  };

  const handleAssignTest = () => {
    router.push("/school");
  };

  if (!isFetched) {
    return null;
  }

  if (!reportData && isFetched) {
    return (
      <Panel preset="panel">
        <Box
          maxW="600px"
          textAlign="center"
          display="flex"
          flexDir="column"
          alignItems="center"
        >
          <Text element="h4" variant="md">
            No results yet!
          </Text>
          <Text element="p" variant="md" mt={theme.spacing.md.px}>
            If they haven’t yet been assigned the Test, click the link below to
            go to Tests and assign it to this student.
          </Text>
          <Box mt={theme.spacing.md.px}>
            <Button size="sm" onClick={handleAssignTest}>
              Assign test
            </Button>
          </Box>
        </Box>
      </Panel>
    );
  }

  if (reportData?.hide_report && !isAdmin) {
    return (
      <Panel preset="warning">
        <Box
          maxW="600px"
          textAlign="center"
          display="flex"
          flexDir="column"
          alignItems="center"
        >
          <Text element="h4" variant="md">
            Report in progress!
          </Text>
          <Text element="p" variant="md" mt={theme.spacing.md.px}>
            This student&apos;s report is now in progress. You will receive a
            notification when it is ready for review.
          </Text>
        </Box>
      </Panel>
    );
  }

  return (
    <Box mb={theme.spacing.md.px}>
      <Box
        display="flex"
        alignItems="center"
        justifyContent="space-between"
        mb={theme.spacing.sm.px}
      >
        <Text element="h4" variant="xl">
          Cognitive profile
        </Text>
        <Box
          id="switcher"
          display="flex"
          alignItems="center"
          gap={theme.spacing.xs.px}
          cursor="pointer"
          onClick={toggleView}
        >
          <Text
            element="h5"
            variant="sm"
            color={theme.colors.ui.link.hex}
            cursor="pointer"
            _hover={{ textDecoration: "underline" }}
          >
            {dataView === "chart"
              ? "Switch to table view"
              : "Switch to chart view"}
          </Text>
          <ArrowPathIcon
            width={20}
            height={20}
            color={theme.colors.ui.link.hex}
          />
        </Box>
      </Box>
      {dataView === "chart" ? (
        <AnalysisChart studentCode={studentCode} />
      ) : (
        <AnalysisTableView studentCode={studentCode} />
      )}
      <Box display="flex" flexDir="column" alignItems="center">
        <Box maxW="600px" mt={theme.spacing.lg.px}>
          <Text element="p">
            This {dataView} shows your student&apos;s scores across various
            areas and gives a snapshot of their ability in key SEN areas.
          </Text>
        </Box>
      </Box>
    </Box>
  );
};
