"use client";

import { Box, Flex } from "@chakra-ui/react";

import { InstructorIcon } from "@/components/v2";
import { Text } from "@/components/v2/Text/Text";
import { theme } from "@/styles/theme";

export const HighPriorityFallback: React.FC<{
  recommendation: string;
}> = ({ recommendation }) => {
  return (
    <Box
      bg={theme.colors.ui.alert_green_02.hex}
      p={theme.spacing.sm.px}
      borderRadius={theme.border.radius.lg.px}
      display="flex"
      alignItems="center"
      gap={theme.spacing.sm.px}
    >
      <Flex alignItems="center" mr={2} gap={2}>
        <InstructorIcon
          bgColor={theme.colors.ui.alert_green_01.hex}
          width={40}
          height={40}
        />
      </Flex>
      <Text
        element="h4"
        variant="sm"
        color={theme.colors.ui.alert_green_01.hex}
      >
        <div dangerouslySetInnerHTML={{ __html: recommendation }} />
      </Text>
    </Box>
  );
};
