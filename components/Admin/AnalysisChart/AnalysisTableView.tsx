"use client";

import { Box, Table, Tbody, Td, Th, Thead, Tr } from "@chakra-ui/react";

import { Loader } from "@/components/v2/Loader/Loader";
import { Text } from "@/components/v2/Text/Text";
import { useAnalysisByIdQuery } from "@/hooks/queries/useAnalysisByIdQuery";
import { theme } from "@/styles/theme";

export const AnalysisTableView: React.FC<{ studentCode: string }> = ({
  studentCode,
}) => {
  const { data: reportData } = useAnalysisByIdQuery({ studentCode });

  if (!reportData) return <Loader />;

  const formatConfidenceInterval = (confidenceInterval) => {
    const { lowerBound, upperBound } = confidenceInterval;
    return lowerBound && upperBound ? `${lowerBound} - ${upperBound}` : "-";
  };

  const formatCategoryName = (name: string) => {
    const sentenceCase = name
      .replace(/([A-Z])/g, " $1")
      .trim()
      .toLowerCase();
    return sentenceCase.charAt(0).toUpperCase() + sentenceCase.slice(1);
  };

  const cognitiveProfile = reportData.result_data.cognitiveProfile;

  const orderedCategories = [
    "visualReasoning",
    "verbalReasoning",
    "phonologicalAwareness",
    "processingSpeed",
    "workingMemory",
    "readingSpeed",
    "readingComprehension",
    "spelling",
  ];

  const sortedCategories = orderedCategories
    .filter((category) => cognitiveProfile[category])
    .map((category) => [category, cognitiveProfile[category]]);

  return (
    <Box
      border="1px"
      borderColor={"rgba(0,0,0,0.1)"}
      borderRadius={theme.border.radius.lg.px}
      padding={theme.spacing.md.px}
    >
      <Text element="h4" variant="xs" mb={theme.spacing.sm.px}>
        Student data
      </Text>

      <Table variant="simple">
        <Thead>
          <Tr>
            <Th>Category</Th>
            <Th>Standard score</Th>
            <Th>Score rank</Th>
            <Th>Percentile rank</Th>
            <Th>95% conf. interval</Th>
          </Tr>
        </Thead>
        <Tbody>
          {sortedCategories.map(([category, data]) => {
            const { composite, scales } = data;
            const scale = scales[0];

            return (
              <Tr
                key={category}
                _hover={{
                  bg: theme.colors.ui.grey_04.hex,
                  td: {
                    fontWeight: "semibold",
                  },
                }}
              >
                <Td minWidth="200px" maxWidth="200px">
                  {formatCategoryName(category)}
                </Td>
                <Td minWidth="100px" maxWidth="100px">
                  {composite.standardScore}
                </Td>
                <Td minWidth="150px" maxWidth="150px">
                  {composite.scoreRank}
                </Td>
                <Td minWidth="100px" maxWidth="100px">
                  {composite.percentileRank}
                </Td>
                <Td minWidth="150px" maxWidth="150px">
                  {formatConfidenceInterval(composite.confidenceInterval)}
                </Td>
              </Tr>
            );
          })}
        </Tbody>
      </Table>
    </Box>
  );
};
