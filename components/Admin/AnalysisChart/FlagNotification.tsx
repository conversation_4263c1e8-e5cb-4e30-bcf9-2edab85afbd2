"use client";

import { <PERSON>, <PERSON> } from "@chakra-ui/react";
import { format, parseISO } from "date-fns";
import React from "react";

import { Loader } from "@/components/v2/Loader/Loader";
import { Text } from "@/components/v2/Text/Text";
import { useAnalysisByIdQuery } from "@/hooks/queries/useAnalysisByIdQuery";
import { theme } from "@/styles/theme";
import { AnalysisData } from "@/types/analysis";

const Notification = ({
  report,
  isAdmin = false,
}: {
  report: AnalysisData;
  isAdmin?: boolean;
}) => {
  if (report.hide_report && !isAdmin) {
    return null;
  }

  const unusualCategories = Object.entries(report.result_data.cognitiveProfile)
    .filter(([_, data]) => data.composite.unusualResults)
    .map(([category, _]) =>
      category
        .replace(/([A-Z])/g, " $1")
        .toLowerCase()
        .replace(/\b\w/g, (char) => char.toUpperCase())
    );

  if (unusualCategories.length === 0) {
    return (
      <Box mb={theme.spacing.lg.px}>
        <Text element="h4" variant="sm" color={theme.colors.primary.black.hex}>
          {`These results are from the Dyslexia & Cognitive profile screener taken
          on ${format(parseISO(report.assessment_meta.dateTaken), "dd/MM/yy")}`}
        </Text>
      </Box>
    );
  }

  const formatCategories = (categories) => {
    if (categories.length === 1) {
      return categories[0];
    } else if (categories.length === 2) {
      return `${categories[0]} and ${categories[1]}`;
    } else {
      return `${categories.slice(0, -1).join(", ")} and ${
        categories[categories.length - 1]
      }`;
    }
  };

  return (
    <Box mb={theme.spacing.lg.px}>
      <Text element="h4" variant="sm" color={theme.colors.ui.alert_red_01.hex}>
        We noticed unusual scores for {formatCategories(unusualCategories)}{" "}
        which may make the results and recommendations inaccurate.{" "}
        <Link href="/contact" color={theme.colors.ui.link.hex}>
          Get in touch
        </Link>{" "}
        if you want to discuss.
      </Text>
    </Box>
  );
};

export const FlagNotification: React.FC<{
  studentCode: string;
  isAdmin?: boolean;
}> = ({ studentCode, isAdmin = false }) => {
  const { data: reportData } = useAnalysisByIdQuery({ studentCode });

  if (!reportData) return <Loader />;

  return <Notification report={reportData} isAdmin={isAdmin} />;
};
