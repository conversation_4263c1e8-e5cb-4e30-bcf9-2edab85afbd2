"use client";

import { Box } from "@chakra-ui/react";
import {
  BarElement,
  CategoryScale,
  Chart as ChartJS,
  ChartData,
  ChartOptions,
  defaults,
  Legend,
  LinearScale,
  Tick,
  Title,
  Tooltip,
} from "chart.js";
import React, { useEffect, useRef } from "react";
import { Bar } from "react-chartjs-2";

interface CustomChartOptions extends ChartOptions<"bar"> {
  scales: {
    x: {
      grid: {
        display: boolean;
      };
      border: {
        display: boolean;
      };
      ticks: {
        font: {
          size: number;
        };
        color: string;
      };
    };
    y: {
      type: "customScale";
      border: {
        dash: number[];
        display: boolean;
      };
      grid: {
        color: string;
        lineWidth: number;
      };
      beginAtZero: boolean;
      max: number;
      afterBuildTicks: (axis: any) => void;
      ticks: {
        font: {
          size: number;
        };
        color: string;
        stepSize: number;
        callback: (value: number) => string | number;
      };
    };
  };
}

ChartJS.register(
  CategoryScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  LinearScale
);

class CustomScale extends LinearScale {
  static id = "customScale";
  static defaults = LinearScale.defaults;

  generateTickLabels(ticks: Tick[]) {
    const originalValues = [0, 70, 85, 100, 115, 130];
    const mappedValues = [0, 1, 2, 3, 4, 5];
    ticks.forEach((tick, index) => {
      const scoreValue = originalValues[mappedValues.indexOf(index)];
      tick.label = scoreValue ? scoreValue.toString() : undefined;
    });
  }
}

ChartJS.register(CustomScale);

const calculateLevel = (value: number) => {
  const originalValues = [0, 70, 85, 100, 115, 130, 200];
  const mappedValues = [0, 1, 2, 3, 4, 5, 6];

  for (let i = 0; i < originalValues.length; i++) {
    if (value === originalValues[i]) {
      return mappedValues[i];
    }
    if (value < originalValues[i]) {
      const ratio =
        (value - originalValues[i - 1]) /
        (originalValues[i] - originalValues[i - 1]);
      return (
        mappedValues[i - 1] + ratio * (mappedValues[i] - mappedValues[i - 1])
      );
    }
  }
  return value;
};

const interpolateRgb = (rgb1, rgb2, factor) => {
  return [
    Math.round(rgb1[0] + (rgb2[0] - rgb1[0]) * factor),
    Math.round(rgb1[1] + (rgb2[1] - rgb1[1]) * factor),
    Math.round(rgb1[2] + (rgb2[2] - rgb1[2]) * factor),
  ];
};

const rgbToHex = (rgb) => {
  return `#${rgb.map((x) => x.toString(16).padStart(2, "0")).join("")}`;
};

const getScoreColor = (score: number) => {
  const color1 = [241, 115, 115];
  const color2 = [255, 215, 105];
  const color3 = [111, 208, 109];

  if (score <= 70) return rgbToHex(color1);
  if (score > 131) return rgbToHex(color3);

  let interpolatedColor;

  if (score <= 100) {
    const factor = (score - 70) / (100 - 70);
    interpolatedColor = interpolateRgb(color1, color2, factor);
  } else {
    const factor = (score - 100) / (131 - 100);
    interpolatedColor = interpolateRgb(color2, color3, factor);
  }

  return rgbToHex(interpolatedColor);
};

const transformData = (reportData: any) => {
  const { cognitiveProfile } = reportData.result_data;

  return [
    {
      name: "Visual reasoning",
      originalScore: cognitiveProfile.visualReasoning.composite.standardScore,
      score: calculateLevel(
        cognitiveProfile.visualReasoning.composite.standardScore
      ),
      color: getScoreColor(
        cognitiveProfile.visualReasoning.composite.standardScore
      ),
    },
    {
      name: "Verbal ability",
      originalScore: cognitiveProfile.verbalReasoning.composite.standardScore,
      score: calculateLevel(
        cognitiveProfile.verbalReasoning.composite.standardScore
      ),
      color: getScoreColor(
        cognitiveProfile.verbalReasoning.composite.standardScore
      ),
    },
    {
      name: "Phonological awareness",
      originalScore:
        cognitiveProfile.phonologicalAwareness.composite.standardScore,
      score: calculateLevel(
        cognitiveProfile.phonologicalAwareness.composite.standardScore
      ),
      color: getScoreColor(
        cognitiveProfile.phonologicalAwareness.composite.standardScore
      ),
    },
    {
      name: "Processing speed",
      originalScore: cognitiveProfile.processingSpeed.composite.standardScore,
      score: calculateLevel(
        cognitiveProfile.processingSpeed.composite.standardScore
      ),
      color: getScoreColor(
        cognitiveProfile.processingSpeed.composite.standardScore
      ),
    },
    {
      name: "Working memory",
      originalScore: cognitiveProfile.workingMemory.composite.standardScore,
      score: calculateLevel(
        cognitiveProfile.workingMemory.composite.standardScore
      ),
      color: getScoreColor(
        cognitiveProfile.workingMemory.composite.standardScore
      ),
    },
    {
      name: "Reading speed",
      originalScore: cognitiveProfile.readingSpeed.composite.standardScore,
      score: calculateLevel(
        cognitiveProfile.readingSpeed.composite.standardScore
      ),
      color: getScoreColor(
        cognitiveProfile.readingSpeed.composite.standardScore
      ),
    },
    {
      name: "Reading comp.",
      originalScore:
        cognitiveProfile.readingComprehension.composite.standardScore,
      score: calculateLevel(
        cognitiveProfile.readingComprehension.composite.standardScore
      ),
      color: getScoreColor(
        cognitiveProfile.readingComprehension.composite.standardScore
      ),
    },
    {
      name: "Spelling ability",
      originalScore: cognitiveProfile.spelling.composite.standardScore,
      score: calculateLevel(cognitiveProfile.spelling.composite.standardScore),
      color: getScoreColor(cognitiveProfile.spelling.composite.standardScore),
    },
  ];
};

export const AnalysisChartPDF: React.FC<{
  reportData: any;
  onBase64Ready?: (base64: string) => void;
}> = ({ reportData, onBase64Ready }) => {
  const chartRef = useRef<any>(null);

  const transformedData = transformData(reportData);

  const scores = transformedData.map((item) => item.score);
  const originalScores = transformedData.map((item) => item.originalScore);

  useEffect(() => {
    document.fonts.ready.then(() => {
      chartRef.current?.chart?.update();
    });
  }, []);

  const chartData: ChartData<"bar"> = {
    labels: transformedData.map((item) => item.name.split(" ")),
    datasets: [
      {
        label: "Standardised scores",
        data: scores,
        backgroundColor: transformedData.map((item) => item.color),
        borderRadius: 5,
        hoverBackgroundColor: transformedData.map((item) => item.color),
        minBarLength: 20,
        barPercentage: 0.9,
        categoryPercentage: 1,
      },
    ],
  };

  const options: CustomChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        callbacks: {
          title: () => "",
          label: (context) => {
            return originalScores[context.dataIndex];
          },
        },
        displayColors: false,
        yAlign: "bottom",
        bodyFont: {
          size: 30,
          weight: "bolder",
        },
      },
      title: {
        display: false,
        text: "Standardised scores",
      },
    },
    scales: {
      x: {
        grid: {
          display: false,
        },
        border: {
          display: false,
        },
        ticks: {
          font: {
            size: 28,
          },
          color: "#000000",
        },
      },
      y: {
        type: "customScale",
        border: { dash: [8, 8], display: false },
        grid: {
          color: "#F7F7F8",
          lineWidth: 2,
        },
        beginAtZero: true,
        max: 5.3,
        afterBuildTicks: (axis) => {
          axis.ticks = [0, 1, 2, 3, 4, 5, 6].map((value) => ({
            value,
            label: value.toString(),
          })) as Tick[];
        },
        ticks: {
          font: {
            size: 28,
          },
          color: "#000000",
          stepSize: 1,
          callback: (value) => {
            const originalValues = [0, 70, 85, 100, 115, 130, 200];
            return originalValues[value];
          },
        },
      },
    },
    devicePixelRatio: 2,
    animation: {
      onComplete(event) {
        const image = event.chart.toBase64Image();

        if (onBase64Ready) {
          onBase64Ready(image);
        }
      },
    },
  };

  return (
    <Box height="800px" width="1600px">
      <Bar ref={chartRef} data={chartData} options={options} />
    </Box>
  );
};
