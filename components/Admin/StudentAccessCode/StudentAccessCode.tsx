"use client";

import { Box, useToast } from "@chakra-ui/react";

import { Button } from "@/components/v2";
import { Loader } from "@/components/v2/Loader/Loader";
import { Text } from "@/components/v2/Text/Text";
import { useStudentByCodeQuery } from "@/hooks/queries/useStudentByCodeQuery";
import { theme } from "@/styles/theme";
import { printLoginCodes } from "@/utils/admin/print-login-codes";

import { AdminPanel } from "../AdminPanel/AdminPanel";

export const StudentAccessCode: React.FC<{ studentCode: string }> = ({
  studentCode,
}) => {
  const { data: studentData } = useStudentByCodeQuery({ studentCode });
  const toast = useToast();

  if (!studentData) return <Loader />;

  return (
    <AdminPanel>
      <Box
        display="flex"
        justifyContent="space-between"
        marginBottom={theme.spacing.md.px}
      >
        <Text element="h4" variant="xl">
          Access
        </Text>
        <Button
          size="sm"
          onClick={() => {
            printLoginCodes([studentData.student_id]);
            toast({
              title: "Login ID downloaded!",
              description: "Please open the pdf you've downloaded to print",
              status: "success",
              duration: 5000,
              isClosable: true,
            });
          }}
        >
          Print
        </Button>
      </Box>
      <Box display="flex" gap={theme.spacing.sm.px} alignItems="center">
        <Text element="h5" variant="sm">
          Unique ID:
        </Text>
        <Text element="p" variant="3xl">
          {studentData.student_code}
        </Text>
      </Box>
    </AdminPanel>
  );
};
