"use client";

import { Box } from "@chakra-ui/react";
import {
  ArrowDownTrayIcon,
  ArrowPathIcon,
  MagnifyingGlassIcon,
  XCircleIcon,
} from "@heroicons/react/24/solid";
import { format, parse } from "date-fns";
import { motion } from "framer-motion";
import { useRouter } from "next/navigation";
import React, { useState } from "react";
import { useDropzone } from "react-dropzone";
import { read, utils, WorkBook } from "xlsx";
import { z, ZodIssue } from "zod";

import { Button, Panel, Text } from "@/components/v2";
import { useAddStudentsMutation } from "@/hooks/mutations/useAddStudentsMutation";
import { theme } from "@/styles/theme";
import { studentSchema } from "@/types/zod/student-schema";
import { generateStudentCode } from "@/utils/security/generate-student-code";

enum UploadStatus {
  Resting = "Resting",
  Adding = "Adding",
  StudentsFound = "StudentsFound",
  Uploaded = "Uploaded",
  Success = "Success",
  Error = "Error",
  TooManyStudents = "TooManyStudents",
}

const STUDENT_UPLOAD_LIMIT = 500;

const UploadStudents = ({ user }: { user: any }) => {
  const [isDragging, setIsDragging] = useState(false);
  const [uploadStatus, setUploadStatus] = useState(UploadStatus.Resting);
  const [numStudents, setNumStudents] = useState(0);
  const [transformedData, setTransformedData] = useState({});
  const [originalJsonData, setOriginalJsonData] = useState<any[]>([]);

  const [validationErrors, setValidationErrors] = useState<
    string[] | ZodIssue[]
  >([]);
  const { mutate: addStudents } = useAddStudentsMutation();
  const { push } = useRouter();

  const schoolId = user.data.user.user_metadata.orgId;

  // Cleanup the expected output
  const formatExpectedCopy = (str: string) => {
    let cleanedStr = str.replace(/'Don't know'/g, "Don't know");
    cleanedStr = cleanedStr.replace(/'([^']+)'/g, "$1");

    const parts = cleanedStr.split("|").map((part) => part.trim());

    if (parts.length > 1) {
      return parts.slice(0, -1).join(", ") + ", or " + parts.slice(-1);
    }
    return parts[0];
  };

  // Validate the uploaded data against the schema
  const validateStudentData = (students) => {
    const schema = z.array(studentSchema);
    const zodParse = schema.safeParse(students);

    if (zodParse.success) {
      setNumStudents(zodParse.data.length);

      if (zodParse.data.length > STUDENT_UPLOAD_LIMIT) {
        setUploadStatus(UploadStatus.TooManyStudents);
      } else {
        setUploadStatus(UploadStatus.Uploaded);
      }

      setValidationErrors([]);
    } else {
      setUploadStatus(UploadStatus.Error);
      setValidationErrors(zodParse.error.errors);
    }
  };

  const capitalize = (str) => {
    if (!str) return "";
    return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
  };

  // Transform the data for database insertion
  const transformDataForDatabase = async (dataArray) => {
    return dataArray.map((dataObject) => {
      const [day, month, year] = dataObject["Date of birth"].split(/[./]/);
      const transformedYear = year.length === 2 ? `20${year}` : year;
      const dateToTransform =
        month > 12
          ? `${transformedYear}-${day}-${month}`
          : `${transformedYear}-${month}-${day}`;

      const isoDate = format(
        parse(dateToTransform, "yyyy-MM-dd", new Date()),
        "yyyy-MM-dd'T'HH:mm:ss.SSSXXX"
      );

      return {
        surname: dataObject["Surname"],
        first_names: dataObject["First names"],
        school_id: schoolId,
        year: Number(dataObject["Year group"]),
        student_code: generateStudentCode({
          firstname: dataObject["First names"],
          surname: dataObject["Surname"],
          dateOfBirth: isoDate,
          schoolId: schoolId,
        }),
        date_of_birth: isoDate,
        dyslexia: capitalize(dataObject["Dyslexia status"]),
        adhd: capitalize(dataObject["ADHD status"]),
        other_conditions: capitalize(dataObject["Other conditions"]),
        reading_ability: parseInt(dataObject["Reading ability"], 10),
        processing_speed: parseInt(dataObject["Processing speed"], 10),
        spelling_ability: parseInt(dataObject["Spelling ability"], 10),
        academic_ability: parseInt(dataObject["Academic ability"], 10),
      };
    });
  };

  const transformStudentData = (student) => {
    const dob = student["Date of birth"];

    if (
      !dob ||
      dob === null ||
      typeof dob !== "string" ||
      !(dob.includes("/") || dob.includes("."))
    ) {
      return {
        ...student,
        "Date of birth": dob,
      };
    }
    const [day, month, year] = dob
      .split(/[/\.]/)
      .map((part) => parseInt(part, 10));
    const transformedYear =
      year.toString().length === 2 ? `20${year}` : year.toString();
    const dateToTransform =
      month > 12
        ? `${transformedYear}-${day}-${month}`
        : `${transformedYear}-${month}-${day}`;

    const isoDate = format(
      parse(dateToTransform, "yyyy-MM-dd", new Date()),
      "yyyy-MM-dd'T'HH:mm:ss.SSSXXX"
    );
    const capitalize = (str) => {
      const words = str.toLowerCase().split(" ");
      return words
        .map((word, index) =>
          index === 0 ? word.charAt(0).toUpperCase() + word.slice(1) : word
        )
        .join(" ");
    };

    return {
      ...student,
      "Date of birth": isoDate,
      "Reading ability": capitalize(student["Reading ability"] || ""),
      "Spelling ability": capitalize(student["Spelling ability"] || ""),
      "Academic ability": capitalize(student["Academic ability"] || ""),
      "Processing speed": capitalize(student["Processing speed"] || ""),
      "Dyslexia status": capitalize(student["Dyslexia status"] || ""),
      "ADHD status": capitalize(student["ADHD status"] || ""),
    };
  };

  // Handle the file drop event
  const onFileDrop = (acceptedFiles: File[]) => {
    setIsDragging(false);

    if (acceptedFiles.length > 0) {
      const file = acceptedFiles[0];
      const reader = new FileReader();

      reader.onload = async (e) => {
        try {
          const data = read(new Uint8Array(e.target?.result as ArrayBuffer), {
            type: "array",
          }) as WorkBook;

          const worksheet = data.Sheets[data.SheetNames[0]];
          const jsonData = utils.sheet_to_json(worksheet, {
            raw: false,
            blankrows: false,
          });
          setOriginalJsonData(jsonData);

          const transformedStudents = jsonData.map(transformStudentData);

          validateStudentData(transformedStudents);

          const transformData = await transformDataForDatabase(jsonData);

          setTransformedData(transformData);
        } catch (error) {
          console.log(error);
          setUploadStatus(UploadStatus.Error);
        }
      };

      reader.readAsArrayBuffer(file);
    }
  };

  // Use the dropzone hooks
  const { getRootProps, getInputProps, acceptedFiles } = useDropzone({
    onDrop: onFileDrop,
    multiple: false,
    maxFiles: 1,
    noClick: true,
    onDragEnter: () => {
      setIsDragging(true);
    },
    onDragLeave: () => {
      setIsDragging(false);
    },
    onDropRejected: () => {
      setUploadStatus(UploadStatus.Error);
    },
  });

  // Handle student upload
  const handleStudentUpload = async () => {
    setUploadStatus(UploadStatus.Adding);
    addStudents(
      { students: transformedData },
      {
        onSuccess: () => {
          setTimeout(() => {
            setUploadStatus(UploadStatus.Success);
          }, 1000);
        },
        onError: () => {
          setTimeout(() => {
            setUploadStatus(UploadStatus.Error);
          }, 1000);
        },
      }
    );
  };

  if (uploadStatus === UploadStatus.Uploaded) {
    // Uploaded state
    return (
      <Panel preset="panel">
        <Box
          display="flex"
          flexDir="column"
          alignItems="center"
          gap={theme.spacing.xs.px}
        >
          <Text element="h2" m={0}>
            {acceptedFiles.length > 0 && acceptedFiles[0].name}
          </Text>
          <Text
            element="p"
            textDecor="underline"
            mt={theme.spacing.xs.px}
            cursor="pointer"
            fontWeight="600"
            color={theme.colors.ui.link.hex}
            onClick={() => {
              setUploadStatus(UploadStatus.Resting);
            }}
          >
            Change file
          </Text>
          <Button
            color="purple"
            size="sm"
            type="button"
            mt={theme.spacing.sm.px}
            onClick={() => setUploadStatus(UploadStatus.StudentsFound)}
          >
            Import file
          </Button>
        </Box>
      </Panel>
    );
  }

  if (uploadStatus === UploadStatus.StudentsFound) {
    // Students found state
    return (
      <Panel preset="panel">
        <Box
          display="flex"
          flexDir="column"
          alignItems="center"
          gap={theme.spacing.xs.px}
        >
          <MagnifyingGlassIcon
            width={48}
            height={48}
            color={theme.colors.primary.purple.hex}
          />
          <Text element="h2">
            {numStudents} student{numStudents > 1 ? "s" : ""} found
          </Text>
          <Button
            color="purple"
            size="sm"
            type="button"
            mt={theme.spacing.xs.px}
            onClick={handleStudentUpload}
          >
            Add student{numStudents > 1 ? "s" : ""}
          </Button>
          <Text
            element="p"
            textDecor="underline"
            mt={theme.spacing.sm.px}
            cursor="pointer"
            fontWeight="600"
            color={theme.colors.ui.link.hex}
            onClick={() => {
              setUploadStatus(UploadStatus.Resting);
            }}
          >
            Change file
          </Text>
        </Box>
      </Panel>
    );
  }

  if (uploadStatus === UploadStatus.Adding) {
    // Adding students state
    return (
      <Panel preset="panel">
        <Box
          display="flex"
          flexDir="column"
          alignItems="center"
          gap={theme.spacing.xs.px}
        >
          <motion.div
            animate={{ rotate: [0, 360] }}
            transition={{
              duration: 1,
              repeat: Infinity,
              repeatType: "loop",
              ease: "linear",
            }}
          >
            <ArrowPathIcon
              width={48}
              height={48}
              color={theme.colors.primary.purple.hex}
            />
          </motion.div>
          <Text element="h2">Adding student{numStudents > 1 ? "s" : ""}</Text>
          <Button color="purple" size="sm" type="button" onClick={() => {}}>
            ...
          </Button>
        </Box>
      </Panel>
    );
  }

  if (uploadStatus === UploadStatus.Success) {
    // Success state
    return (
      <Panel preset="success">
        <Box
          display="flex"
          flexDir="column"
          alignItems="center"
          gap={theme.spacing.xs.px}
        >
          <XCircleIcon
            width={48}
            height={48}
            color={theme.colors.ui.alert_green_01.hex}
          />
          <Text element="h2" textAlign="center" maxW={600}>
            Success! {numStudents} student{numStudents > 1 ? "s" : ""} added
          </Text>
          <Button
            color="green"
            size="sm"
            type="button"
            onClick={() => {
              push("/school");
            }}
          >
            View students
          </Button>
        </Box>
      </Panel>
    );
  }

  if (uploadStatus === UploadStatus.Error) {
    // Error state
    return (
      <Panel preset="error">
        <Box
          display="flex"
          flexDir="column"
          alignItems="center"
          gap={theme.spacing.xs.px}
          padding={theme.spacing.lg.px}
        >
          <XCircleIcon
            width={48}
            height={48}
            color={theme.colors.ui.alert_red_01.hex}
          />
          <Text element="h2" textAlign="center" maxW={600}>
            We&apos;ve found {validationErrors.length} issue
            {validationErrors.length > 1 && "s"}!
          </Text>
          <Text
            element="p"
            textDecor="underline"
            mb={theme.spacing.sm.px}
            cursor="pointer"
            fontWeight="600"
            color={theme.colors.ui.link.hex}
            onClick={() => {
              setUploadStatus(UploadStatus.Resting);
              setValidationErrors([]);
            }}
          >
            Change file
          </Text>
          <Box
            className="custom-scrollbar"
            width={680}
            bgColor={theme.colors.primary.white.hex}
            display={"flex"}
            flexDir={"column"}
            gap={theme.spacing.xs.px}
            borderRadius={theme.border.radius.md.px}
            p={theme.spacing.md.px}
            mb={theme.spacing.xs.px}
            maxH={318}
            overflow={"scroll"}
          >
            {validationErrors.map((error, index) => {
              const rowNumber = originalJsonData[error.path[0]].__rowNum__ + 1;
              const columnName = error.path[1];
              const customMessage = error.message;
              const expected =
                error.expected && error.expected.replace(" | '-'", "");
              const code = error.code;
              const enumOptions = error.options;
              const formattedStatuses = enumOptions
                .filter(
                  (status) => enumOptions.includes(status) && status != "-"
                )
                .map((status, index, array) =>
                  index === array.length - 1 && array.length > 1
                    ? `or ${status}`
                    : status
                )
                .join(", ");
              return (
                <Box key={index} display={"flex"} gap={theme.spacing.xs.px}>
                  <Box>
                    <Text element="p" variant="sm" fontWeight="600">
                      Row {rowNumber}:
                    </Text>
                  </Box>
                  <Box flex={1}>
                    <Text element="p" variant="sm">
                      {customMessage && expected === "string"
                        ? customMessage
                        : code === "invalid_type"
                          ? `${columnName} must be one of ${formatExpectedCopy(
                              expected
                            )}`
                          : code === "invalid_enum_value"
                            ? `${columnName} must be one of ${formattedStatuses} instead of ${error.received}`
                            : "An error occurred"}
                    </Text>
                  </Box>
                </Box>
              );
            })}
          </Box>
        </Box>
      </Panel>
    );
  }

  if (uploadStatus === UploadStatus.TooManyStudents) {
    // TooManyStudents state
    return (
      <Panel preset="error">
        <Box
          display="flex"
          flexDir="column"
          alignItems="center"
          gap={theme.spacing.xs.px}
          padding={theme.spacing.lg.px}
        >
          <XCircleIcon
            width={48}
            height={48}
            color={theme.colors.ui.alert_red_01.hex}
          />
          <Text element="h2" textAlign="center" maxW={600}>
            You can upload a maximum of {STUDENT_UPLOAD_LIMIT} students at once!
          </Text>
          <Text
            element="p"
            textDecor="underline"
            mb={theme.spacing.sm.px}
            cursor="pointer"
            fontWeight="600"
            color={theme.colors.ui.link.hex}
            onClick={() => {
              setUploadStatus(UploadStatus.Resting);
              setValidationErrors([]);
            }}
          >
            Change file
          </Text>
        </Box>
      </Panel>
    );
  }

  return (
    <>
      <Panel preset="dropzone" isAnimating={isDragging} {...getRootProps()}>
        <input {...getInputProps()} />
        <Box
          display="flex"
          flexDir="column"
          alignItems="center"
          gap={theme.spacing.md.px}
        >
          <ArrowDownTrayIcon width={48} height={48} />
          <Text element="h2">👋 Drop your file here</Text>
        </Box>
      </Panel>
    </>
  );
};

export { UploadStudents };
