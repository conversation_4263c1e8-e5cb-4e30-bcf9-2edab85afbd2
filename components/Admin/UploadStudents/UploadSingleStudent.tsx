"use client";

import {
  Box,
  FormControl,
  FormErrorMessage,
  Spinner,
  Stack,
  useToast,
} from "@chakra-ui/react";
import { zodResolver } from "@hookform/resolvers/zod";
import { format, parse } from "date-fns";
import { useRouter } from "next/navigation";
import { useEffect, useRef, useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { FormLabel } from "@/components/FormLabel";
import { Radio } from "@/components/Radio/Radio";
import { RadioGroup } from "@/components/RadioGroup/RadioGroup";
import { Button } from "@/components/v2";
import { Input } from "@/components/v2/Input/Input";
import { Panel } from "@/components/v2/Panel/Panel";
import { Select } from "@/components/v2/Select/Select";
import { Text } from "@/components/v2/Text/Text";
import { useAddStudentsMutation } from "@/hooks/mutations/useAddStudentsMutation";
import { useSchoolsByIdQuery } from "@/hooks/queries/useSchoolsByIdQuery";
import { theme } from "@/styles/theme";
import {
  diagnosisStatus,
  numberEnum,
  yearEnum,
} from "@/types/zod/student-schema";
import { generateStudentCode } from "@/utils/security/generate-student-code";

const WIDE_INPUT_WIDTH = "560px";
const NARROW_INPUT_WIDTH = "150px";

const yearOptions = yearEnum.options;
const statusOptions = diagnosisStatus.options.filter(
  (option) => option !== "-"
);
const numberOptions = numberEnum.options.filter((option) => option !== "-");

const formSchema = z.object({
  name: z.string().min(1, "First name is required"),
  surname: z.string().min(1, "Surname is required"),
  dateOfBirth: z.string().refine(
    (value) => {
      const dateRegex = /^(0[1-9]|[12][0-9]|3[01])\/(0[1-9]|1[0-2])\/\d{4}$/;
      if (!dateRegex.test(value)) return false;

      const [day, month, year] = value.split("/").map(Number);
      const date = new Date(year, month - 1, day);
      return (
        date.getDate() === day &&
        date.getMonth() === month - 1 &&
        date.getFullYear() === year
      );
    },
    {
      message: "Invalid date format. Expected dd/mm/yyyy.",
    }
  ),
  year: z.union([yearEnum, z.literal("")]).refine((value) => value !== "", {
    message: "Year is required",
  }),
  dyslexiaStatus: diagnosisStatus.refine((value) => value !== "-", {
    message: "Dyslexia status is required",
  }),
  adhdStatus: diagnosisStatus.refine((value) => value !== "-", {
    message: "ADHD status is required",
  }),
  readingAbility: numberEnum.refine((value) => value !== "-", {
    message: "Reading ability is required",
  }),
  spellingAbility: numberEnum.refine((value) => value !== "-", {
    message: "Spelling ability is required",
  }),
  academicAbility: numberEnum.refine((value) => value !== "-", {
    message: "Academic ability is required",
  }),
  processingSpeed: numberEnum.refine((value) => value !== "-", {
    message: "Processing speed is required",
  }),
  otherConditions: z.string().optional(),
});

export const UploadSingleStudent = ({ user }: { user: any }) => {
  const toast = useToast();
  const router = useRouter();
  const [isSaving, setIsSaving] = useState(false);

  const dateRef = useRef<HTMLDivElement>(null);

  const {
    handleSubmit,
    register,
    setValue,
    watch,
    formState: { errors, isSubmitting },
  } = useForm({
    resolver: zodResolver(formSchema),
  });

  const schoolId = user.data.user.user_metadata.orgId;
  const { data: schoolData, isLoading: schoolsLoading } = useSchoolsByIdQuery({
    schoolId: schoolId,
  });
  const { mutate: addStudents } = useAddStudentsMutation();

  useEffect(() => {
    if (
      errors.dateOfBirth &&
      dateRef.current &&
      !errors.name &&
      !errors.surname
    ) {
      dateRef.current.scrollIntoView();
      document.getElementById("day")?.focus();
    }
  }, [errors]);

  if (schoolsLoading) {
    return <Spinner />;
  }

  if (!schoolData) {
    return null;
  }
  const updateDateOfBirth = () => {
    const day = watch("day");
    const month = watch("month");
    const year = watch("dobYear");

    if (day && month && year) {
      const combinedDate = `${day.padStart(2, "0")}/${month.padStart(
        2,
        "0"
      )}/${year}`;
      setValue("dateOfBirth", combinedDate);
    }
  };

  const onSubmit = (values) => {
    setIsSaving(true);
    const [day, month, year] = values.dateOfBirth.split(/[./]/);
    const transformedYear = year.length === 2 ? `20${year}` : year;
    const dateToTransform =
      month > 12
        ? `${transformedYear}-${day}-${month}`
        : `${transformedYear}-${month}-${day}`;

    const isoDate = format(
      parse(dateToTransform, "yyyy-MM-dd", new Date()),
      "yyyy-MM-dd'T'HH:mm:ss.SSSXXX"
    );

    const student = [
      {
        surname: values.surname,
        first_names: values.name,
        school_id: schoolId,
        year: Number(values.year),
        student_code: generateStudentCode({
          firstname: values.name,
          surname: values.surname,
          dateOfBirth: isoDate,
          schoolId: schoolId,
        }),
        date_of_birth: isoDate,
        dyslexia: values.dyslexiaStatus,
        adhd: values.adhdStatus,
        other_conditions: values.otherConditions,
        reading_ability: parseInt(values.readingAbility, 10),
        processing_speed: parseInt(values.processingSpeed, 10),
        spelling_ability: parseInt(values.spellingAbility, 10),
        academic_ability: parseInt(values.academicAbility, 10),
      },
    ];

    addStudents(
      { students: student },
      {
        onSuccess: () => {
          router.push("/school");
        },
        onError: () => {
          setIsSaving(false);
          toast({
            title: "There was an error",
            status: "error",
            duration: 5000,
            isClosable: true,
          });
        },
      }
    );
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <Box display="flex" flexDirection="column" gap={theme.spacing.md.px}>
        <Panel preset="outline">
          <Text element="h4" variant="md" mb={theme.spacing.sm.px}>
            The basics
          </Text>
          <Text
            element="h6"
            variant="xs"
            color={theme.colors.ui.alert_red_01.hex}
          >
            Required
          </Text>
          <Text element="p" variant="md">
            We use these details to create the user and put them in the right
            year group
          </Text>
          <Box display="flex" w="full" flexDirection="column">
            <FormControl
              isInvalid={!!errors.name}
              mb={theme.spacing.md.px}
              w="full"
              maxW={WIDE_INPUT_WIDTH}
            >
              <FormLabel htmlFor="name">First name</FormLabel>
              <Input id="name" {...register("name")} />
              <FormErrorMessage>
                {errors.name && (errors.name.message as string)}
              </FormErrorMessage>
            </FormControl>

            <FormControl
              isInvalid={!!errors.surname}
              mb={theme.spacing.md.px}
              w="full"
              maxW={WIDE_INPUT_WIDTH}
            >
              <FormLabel htmlFor="surname">Surname</FormLabel>
              <Input id="surname" {...register("surname")} />
              <FormErrorMessage>
                {errors.surname && (errors.surname.message as string)}
              </FormErrorMessage>
            </FormControl>

            <FormControl
              isInvalid={!!errors.dateOfBirth}
              mb={theme.spacing.md.px}
              w="full"
            >
              <div ref={dateRef}>
                <FormLabel htmlFor="dateOfBirth">
                  Date of birth (DD/MM/YYYY)
                </FormLabel>
                <Box
                  display="flex"
                  alignItems="center"
                  justifyContent="space-between"
                  w="full"
                  maxW={NARROW_INPUT_WIDTH}
                  borderWidth="2px"
                  borderColor={
                    errors.dateOfBirth
                      ? theme.colors.ui.alert_red_01.hex
                      : theme.colors.ui.grey_01.hex
                  }
                  backgroundColor={
                    errors.dateOfBirth
                      ? theme.colors.ui.alert_red_02.hex
                      : "transparent"
                  }
                  padding={theme.spacing.sm.px}
                  borderRadius="6px"
                >
                  <Input
                    id="day"
                    {...register("day")}
                    w="50px"
                    p={0}
                    maxLength={2}
                    max={31}
                    border="none"
                    sx={{
                      _invalid: { backgroundColor: "transparent" },
                    }}
                    onChange={(e) => {
                      const day = e.target.value.replace(/\D/g, "");
                      setValue("day", day);
                      if (day.length === 2) {
                        document.getElementById("month")?.focus();
                      }
                      updateDateOfBirth();
                    }}
                  />
                  <Text mx={1}>/</Text>
                  <Input
                    id="month"
                    {...register("month")}
                    w="50px"
                    p={0}
                    max={12}
                    maxLength={2}
                    border="none"
                    onKeyDown={(e) => {
                      const target = e.target as HTMLInputElement;
                      if (e.key === "Backspace" && target.value === "") {
                        document.getElementById("day")?.focus();
                      }
                    }}
                    onChange={(e) => {
                      const month = e.target.value.replace(/\D/g, "");
                      setValue("month", month);
                      if (month.length === 2) {
                        document.getElementById("dobYear")?.focus();
                      }
                      updateDateOfBirth();
                    }}
                    sx={{
                      _invalid: { backgroundColor: "transparent" },
                    }}
                  />
                  <Text mx={1}>/</Text>
                  <Input
                    id="dobYear"
                    {...register("dobYear")}
                    w="100px"
                    p={0}
                    min={1900}
                    maxLength={4}
                    border="none"
                    onKeyDown={(e) => {
                      const target = e.target as HTMLInputElement;
                      if (e.key === "Backspace" && target.value === "") {
                        document.getElementById("month")?.focus();
                      }
                    }}
                    onChange={(e) => {
                      const year = e.target.value.replace(/\D/g, "");
                      setValue("dobYear", year);
                      updateDateOfBirth();
                    }}
                    sx={{
                      _invalid: { backgroundColor: "transparent" },
                    }}
                  />
                </Box>
                <FormErrorMessage>
                  {errors.dateOfBirth
                    ? "Date of birth should be in dd/mm/yyyy format"
                    : null}
                </FormErrorMessage>
              </div>
            </FormControl>

            <FormControl
              isInvalid={!!errors.year}
              mb={theme.spacing.md.px}
              w="full"
            >
              <FormLabel htmlFor="year">Year</FormLabel>
              <Select
                placeholder="Select year"
                maxW={NARROW_INPUT_WIDTH}
                {...register("year")}
              >
                {yearOptions.map((year) => (
                  <option key={year} value={year}>
                    {year}
                  </option>
                ))}
              </Select>
              <FormErrorMessage>
                {errors.year && (errors.year.message as string)}
              </FormErrorMessage>
            </FormControl>
          </Box>
        </Panel>
        <Panel preset="outline">
          <Text element="h4" variant="md" mb={theme.spacing.sm.px}>
            Context
          </Text>
          <Text
            element="h6"
            variant="xs"
            color={theme.colors.ui.alert_red_01.hex}
          >
            Required
          </Text>
          <Text element="p" variant="md">
            This context helps us identify any anomalous results when the
            student completes the assessment. This will not be used in our
            scoring model.
          </Text>
          <Box display="flex" w="full" flexDirection="column">
            <FormControl
              isInvalid={!!errors.dyslexiaStatus}
              mb={theme.spacing.md.px}
              w="full"
              maxW={WIDE_INPUT_WIDTH}
            >
              <FormLabel htmlFor="dyslexiaStatus">
                What is their dyslexia status?
              </FormLabel>
              <RadioGroup
                onChange={(value) =>
                  setValue("dyslexiaStatus", value, { shouldValidate: true })
                }
                value={watch("dyslexiaStatus")}
              >
                <Stack direction="row" columnGap={theme.spacing.md.px}>
                  {statusOptions.map((status) => (
                    <Radio key={status} value={status}>
                      {status}
                    </Radio>
                  ))}
                </Stack>
              </RadioGroup>
              <FormErrorMessage>
                {errors.dyslexiaStatus &&
                  (errors.dyslexiaStatus.message as string)}
              </FormErrorMessage>
            </FormControl>

            <FormControl
              isInvalid={!!errors.adhdStatus}
              mb={theme.spacing.md.px}
              w="full"
              maxW={WIDE_INPUT_WIDTH}
            >
              <FormLabel htmlFor="adhdStatus">
                What is their ADHD status?
              </FormLabel>
              <RadioGroup
                onChange={(value) =>
                  setValue("adhdStatus", value, { shouldValidate: true })
                }
                value={watch("adhdStatus")}
              >
                <Stack direction="row" columnGap={theme.spacing.md.px}>
                  {statusOptions.map((status) => (
                    <Radio key={status} value={status}>
                      {status}
                    </Radio>
                  ))}
                </Stack>
              </RadioGroup>
              <FormErrorMessage>
                {errors.adhdStatus && (errors.adhdStatus.message as string)}
              </FormErrorMessage>
            </FormControl>

            <FormControl
              isInvalid={!!errors.readingAbility}
              mb={theme.spacing.md.px}
              w="full"
              maxW={WIDE_INPUT_WIDTH}
            >
              <FormLabel htmlFor="readingAbility">
                From 1-5 (5 being highest), how would you rate their Reading
                ability?
              </FormLabel>
              <RadioGroup
                onChange={(value) =>
                  setValue("readingAbility", value, { shouldValidate: true })
                }
                value={watch("readingAbility")}
              >
                <Stack direction="row" columnGap={theme.spacing.md.px}>
                  {numberOptions.map((number) => (
                    <Radio key={number} value={number}>
                      {number}
                    </Radio>
                  ))}
                </Stack>
              </RadioGroup>
              <FormErrorMessage>
                {errors.readingAbility &&
                  (errors.readingAbility.message as string)}
              </FormErrorMessage>
            </FormControl>

            <FormControl
              isInvalid={!!errors.spellingAbility}
              mb={theme.spacing.md.px}
              w="full"
              maxW={WIDE_INPUT_WIDTH}
            >
              <FormLabel htmlFor="spellingAbility">
                From 1-5 (5 being highest), how would you rate their Spelling
                ability?
              </FormLabel>
              <RadioGroup
                onChange={(value) =>
                  setValue("spellingAbility", value, { shouldValidate: true })
                }
                value={watch("spellingAbility")}
              >
                <Stack direction="row" columnGap={theme.spacing.md.px}>
                  {numberOptions.map((number) => (
                    <Radio key={number} value={number}>
                      {number}
                    </Radio>
                  ))}
                </Stack>
              </RadioGroup>
              <FormErrorMessage>
                {errors.spellingAbility &&
                  (errors.spellingAbility.message as string)}
              </FormErrorMessage>
            </FormControl>

            <FormControl
              isInvalid={!!errors.academicAbility}
              mb={theme.spacing.md.px}
              w="full"
              maxW={WIDE_INPUT_WIDTH}
            >
              <FormLabel htmlFor="academicAbility">
                From 1-5 (5 being highest), how would you rate their Academic
                ability?
              </FormLabel>
              <RadioGroup
                onChange={(value) =>
                  setValue("academicAbility", value, { shouldValidate: true })
                }
                value={watch("academicAbility")}
              >
                <Stack direction="row" columnGap={theme.spacing.md.px}>
                  {numberOptions.map((number) => (
                    <Radio key={number} value={number}>
                      {number}
                    </Radio>
                  ))}
                </Stack>
              </RadioGroup>
              <FormErrorMessage>
                {errors.academicAbility &&
                  (errors.academicAbility.message as string)}
              </FormErrorMessage>
            </FormControl>

            <FormControl
              isInvalid={!!errors.processingSpeed}
              mb={theme.spacing.md.px}
              w="full"
              maxW={WIDE_INPUT_WIDTH}
            >
              <FormLabel htmlFor="processingSpeed">
                From 1-5 (5 being highest), how would you rate their Processing
                speed?
              </FormLabel>
              <RadioGroup
                onChange={(value) =>
                  setValue("processingSpeed", value, { shouldValidate: true })
                }
                value={watch("processingSpeed")}
              >
                <Stack direction="row" columnGap={theme.spacing.md.px}>
                  {numberOptions.map((number) => (
                    <Radio key={number} value={number}>
                      {number}
                    </Radio>
                  ))}
                </Stack>
              </RadioGroup>
              <FormErrorMessage>
                {errors.processingSpeed &&
                  (errors.processingSpeed.message as string)}
              </FormErrorMessage>
            </FormControl>
          </Box>
        </Panel>

        <Panel preset="outline">
          <Text element="h4" variant="md" mb={theme.spacing.sm.px}>
            Additional comments
          </Text>
          <Text element="h6" variant="xs" color={theme.colors.ui.grey_01.hex}>
            Optional
          </Text>
          <Text element="p" variant="md" maxW={665}>
            This will not be used in our scoring model, but may be useful for
            you to keep your notes alongside the report.
          </Text>
          <Box display="flex" w="full" flexDirection="column">
            <FormControl
              isInvalid={!!errors.otherConditions}
              mb={theme.spacing.md.px}
              w="full"
              maxW={WIDE_INPUT_WIDTH}
            >
              <FormLabel htmlFor="otherConditions">
                Any other conditions?
              </FormLabel>
              <Input id="otherConditions" {...register("otherConditions")} />
              <FormErrorMessage>
                {errors.otherConditions &&
                  (errors.otherConditions.message as string)}
              </FormErrorMessage>
            </FormControl>
          </Box>
        </Panel>
        <Box display="flex" justifyContent="center">
          <Button type="submit" disabled={isSaving || isSubmitting}>
            {isSaving || isSubmitting ? "  Saving..." : "Add student"}
          </Button>
        </Box>
      </Box>
    </form>
  );
};
