import { Box } from "@chakra-ui/react";
import { ArrowTopRightOnSquareIcon } from "@heroicons/react/24/solid";
import Link from "next/link";

import { Text } from "@/components/v2/Text/Text";
import { theme } from "@/styles/theme";
import { replaceString } from "@/utils/replacement/replacement";

export const RenderLink = ({
  title,
  description,
  url,
  studentName,
  isPrimary = false,
}: {
  title: string;
  description?: string | null;
  url?: string | null;
  studentName: string;
  isPrimary?: boolean;
}) => (
  <Box
    color={theme.colors.ui.link.hex}
    display="flex"
    flexDir="column"
    gap="4px"
  >
    {url ? (
      <Link href={url} target="_blank">
        <Box display="flex" alignItems="center" gap="4px">
          <Text
            element="h6"
            variant={isPrimary ? "sm" : "mdsm"}
            margin={0}
            color={theme.colors.ui.link.hex}
            cursor="pointer"
            _hover={{ textDecoration: "underline" }}
          >
            {title}
          </Text>
          <Box mt="-5px">
            <ArrowTopRightOnSquareIcon width={16} height={16} />
          </Box>
        </Box>
      </Link>
    ) : (
      <Text element="h6" variant={isPrimary ? "sm" : "mdsm"} margin={0}>
        {title}
      </Text>
    )}
    {description && (
      <Text element="p" variant="md">
        <div
          dangerouslySetInnerHTML={{
            __html: replaceString(description, { name: studentName }),
          }}
        />
      </Text>
    )}
  </Box>
);
