"use client";

import { <PERSON>, Tab, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>b<PERSON>ane<PERSON>, Ta<PERSON> } from "@chakra-ui/react";
import { useRouter } from "next/navigation";

import { But<PERSON>, Panel } from "@/components/v2";
import { Text } from "@/components/v2/Text/Text";
import { useAnalysisByIdQuery } from "@/hooks/queries/useAnalysisByIdQuery";
import { useStudentByCodeQuery } from "@/hooks/queries/useStudentByCodeQuery";
import { theme } from "@/styles/theme";
import { ReportRecommendationsQuery } from "@/types/graphql/graphql";
import { RecommendationCategory, ScoringType } from "@/types/recommendations";
import { replaceString } from "@/utils/replacement/replacement";
import { calculateAge } from "@/utils/students/calculateAge";
import { camelCaseToSentence } from "@/utils/students/camelCaseToSentence";

import { AdminPanel } from "../AdminPanel/AdminPanel";
import { Recommendation } from "./Recommendation";

const getTestAreaScore = (testArea: string, reportData: any) =>
  reportData.result_data.cognitiveProfile[testArea]?.composite?.standardScore ||
  100;

export const getFilteredRecommendations = (
  studentAge: number,
  reportData: any,
  recommendations: ReportRecommendationsQuery["allRecommendations"]
) => {
  const filterByAge = (rec: any) =>
    Array.isArray(rec.relevantAges) &&
    rec.relevantAges.includes(studentAge.toString());

  const filterByScoring = (rec: any) => {
    const { scoringType, testArea, maximumScore, minimumScore } = rec;
    const dyslexiaRisk = reportData.analysis.dyslexiaRisk;
    const levelOfNeedRisk = reportData.analysis.levelOfNeedRisk;

    if (
      scoringType === ScoringType.Test &&
      testArea &&
      maximumScore !== undefined &&
      minimumScore !== undefined
    ) {
      const score = getTestAreaScore(testArea, reportData);
      if (score < minimumScore || score >= maximumScore) {
        return false;
      }
    }
    if (
      scoringType === ScoringType.DyslexiaRisk &&
      (dyslexiaRisk < minimumScore || dyslexiaRisk >= maximumScore)
    ) {
      return false;
    }
    if (
      scoringType === ScoringType.LevelOfNeedRisk &&
      (levelOfNeedRisk < minimumScore || levelOfNeedRisk >= maximumScore)
    ) {
      return false;
    }
    return true;
  };

  const recommendationsList = recommendations
    .filter(filterByAge)
    .filter(filterByScoring)
    .filter(
      (rec, index, self) => index === self.findIndex((r) => r.id === rec.id)
    );

  recommendationsList.sort((a, b) => {
    if (
      a.scoringType === ScoringType.LevelOfNeedRisk &&
      b.scoringType !== ScoringType.LevelOfNeedRisk
    ) {
      return -1;
    }
    if (
      b.scoringType === ScoringType.LevelOfNeedRisk &&
      a.scoringType !== ScoringType.LevelOfNeedRisk
    ) {
      return 1;
    }

    if (
      a.scoringType === ScoringType.DyslexiaRisk &&
      b.scoringType !== ScoringType.DyslexiaRisk
    ) {
      return -1;
    }
    if (
      b.scoringType === ScoringType.DyslexiaRisk &&
      a.scoringType !== ScoringType.DyslexiaRisk
    ) {
      return 1;
    }

    const scoreA = a.testArea ? getTestAreaScore(a.testArea, reportData) : 100;
    const scoreB = b.testArea ? getTestAreaScore(b.testArea, reportData) : 100;

    if (scoreA !== scoreB) {
      return scoreA - scoreB;
    }

    return (a.testArea || "").localeCompare(b.testArea || "");
  });

  return recommendationsList;
};

export const RecommendationsTabs = ({
  studentCode,
  isAdmin = false,
  recommendations,
  recommendationIcons,
  recommendationCopy,
}) => {
  const router = useRouter();
  const { data: studentData } = useStudentByCodeQuery({ studentCode });
  const { data: reportData, isFetched } = useAnalysisByIdQuery({ studentCode });

  const handleAssignTest = () => router.push("/school");

  if (!isFetched || !studentData) return null;

  if (!reportData && isFetched) {
    return (
      <Panel preset="panel">
        <Box
          maxW="600px"
          textAlign="center"
          display="flex"
          flexDir="column"
          alignItems="center"
        >
          <Text element="h4" variant="md">
            No results yet!
          </Text>
          <Text element="p" variant="md" mt={theme.spacing.md.px}>
            If they haven’t yet been assigned the Test, click the link below to
            go to Tests and assign it to this student.
          </Text>
          <Button size="sm" mt={theme.spacing.md.px} onClick={handleAssignTest}>
            Assign test
          </Button>
        </Box>
      </Panel>
    );
  }

  if (reportData?.hide_report && !isAdmin) {
    return (
      <Panel preset="warning">
        <Box
          maxW="600px"
          textAlign="center"
          display="flex"
          flexDir="column"
          alignItems="center"
        >
          <Text element="h4" variant="md">
            Report in progress!
          </Text>
          <Text element="p" variant="md" mt={theme.spacing.md.px}>
            This student&apos;s report is now in progress. You will receive a
            notification when it is ready for review.
          </Text>
        </Box>
      </Panel>
    );
  }

  const studentAge = calculateAge(studentData.date_of_birth);
  const filteredRecommendations = getFilteredRecommendations(
    studentAge,
    reportData,
    recommendations
  );

  const categoryText = {
    "Extra Support": replaceString(
      recommendationCopy.general.extraSupportCopy,
      { name: studentData.first_names }
    ),
    Classroom: replaceString(recommendationCopy.general.classroomCopy, {
      name: studentData.first_names,
    }),
    "At Home": replaceString(recommendationCopy.general.atHomeCopy, {
      name: studentData.first_names,
    }),
    "High Priority": replaceString(
      recommendationCopy.general.highPriorityCopy,
      {
        name: studentData.first_names,
      }
    ),
    "Recommendation Introduction": replaceString(
      recommendationCopy.general.recommendationsIntroductionCopy,
      {
        name: studentData.first_names,
      }
    ),
  };

  const recommendationCategories = [
    {
      name: "Classroom",
      recommendations: filteredRecommendations.filter(
        (rec) => rec.category === RecommendationCategory.Classroom
      ),
    },
    {
      name: "Extra Support",
      recommendations: filteredRecommendations.filter(
        (rec) => rec.category === RecommendationCategory.ExtraSupport
      ),
    },
    {
      name: "At Home",
      recommendations: filteredRecommendations.filter(
        (rec) => rec.category === RecommendationCategory.AtHome
      ),
    },
  ];

  return (
    <Box marginTop={theme.spacing.lg.px}>
      <Box
        display="flex"
        flexDir="column"
        gap={theme.spacing.sm.px}
        marginBottom={theme.spacing.md.px}
      >
        <Box mb={theme.spacing.lg.px}>
          <Text element="h4" variant="xl">
            Recommendations
          </Text>
          <Text element="p" variant="md">
            <div
              dangerouslySetInnerHTML={{
                __html: categoryText["Recommendation Introduction"],
              }}
            />
          </Text>
        </Box>
        <Tabs variant="soft-rounded">
          <TabList>
            {recommendationCategories.map(
              ({ name, recommendations }) =>
                recommendations.length > 0 && (
                  <Tab key={name}>
                    <strong>{name}</strong>
                    <span>&nbsp;({recommendations.length})</span>
                  </Tab>
                )
            )}
          </TabList>

          <TabPanels>
            {recommendationCategories.map(
              ({ name, recommendations }) =>
                recommendations.length > 0 && (
                  <TabPanel key={name}>
                    <Box
                      display="flex"
                      flexDir="column"
                      gap={theme.spacing.md.px}
                    >
                      {categoryText[name] && (
                        <Text element="p" variant="md">
                          <div
                            dangerouslySetInnerHTML={{
                              __html: categoryText[name],
                            }}
                          />
                        </Text>
                      )}
                      {Object.entries(
                        recommendations.reduce(
                          (acc, rec) => {
                            const group = rec.testArea || "Other";
                            acc[group] = acc[group] || [];
                            acc[group].push(rec);
                            return acc;
                          },
                          {} as Record<string, typeof recommendations>
                        )
                      ).map(([testArea, recs]) => (
                        <AdminPanel key={testArea}>
                          {testArea && testArea !== "Other" && (
                            <Text
                              element="h2"
                              variant="xs"
                              color={theme.colors.primary.purple.hex}
                              mb={theme.spacing.sm.px}
                            >
                              {camelCaseToSentence(testArea)}
                            </Text>
                          )}
                          <Box
                            display="flex"
                            flexDir="column"
                            gap={theme.spacing.lg.px}
                          >
                            {recs.map((rec) => (
                              <Recommendation
                                key={rec.id}
                                recommendation={rec}
                                recommendationIcons={recommendationIcons}
                                student={studentData}
                                feedback={reportData?.recommendations_feedback}
                                reportId={reportData?.id}
                                isAdmin={isAdmin}
                                scoringData={
                                  reportData?.result_data.cognitiveProfile
                                }
                              />
                            ))}
                          </Box>
                        </AdminPanel>
                      ))}
                    </Box>
                  </TabPanel>
                )
            )}
          </TabPanels>
        </Tabs>
      </Box>
    </Box>
  );
};
