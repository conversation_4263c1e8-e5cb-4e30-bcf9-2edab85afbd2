"use client";

import { Box } from "@chakra-ui/react";
import Image from "next/image";
import { useEffect, useRef, useState } from "react";

import { FeedbackThumbs } from "@/components/FeedbackThumbs/FeedbackThumbs";
import { Text } from "@/components/v2/Text/Text";
import { useUpdateRecommendationsFeedbackMutation } from "@/hooks/mutations/useUpdateRecommendationFeedback";
import { theme } from "@/styles/theme";
import { CognitiveProfile, RecommendationsFeedback } from "@/types/analysis";
import {
  RecommendationTypeIconsQuery,
  ReportRecommendationsQuery,
} from "@/types/graphql/graphql";
import { RecommendationCategory } from "@/types/recommendations";
import { camelCaseToSentence } from "@/utils/students/camelCaseToSentence";
import { renderTitleWithLinks } from "@/utils/text/render-title-with-links";

import { RenderLink } from "./components/RenderLink";

type RecommendationProps = {
  recommendation: ReportRecommendationsQuery["allRecommendations"][0];
  recommendationIcons: RecommendationTypeIconsQuery;
  student: any;
  feedback?: RecommendationsFeedback[];
  reportId?: number;
  isAdmin?: boolean;
  scoringData?: CognitiveProfile | null;
};

export const Recommendation: React.FC<RecommendationProps> = ({
  recommendation,
  recommendationIcons,
  student,
  feedback = [],
  reportId,
  isAdmin = false,
  scoringData,
}: RecommendationProps) => {
  const [isHovered, setIsHovered] = useState(false);
  const [_updatedFeedback, setUpdatedFeedback] = useState<
    RecommendationsFeedback[]
  >([]);

  useEffect(() => {
    setUpdatedFeedback(feedback);
  }, [feedback]);

  const { mutate: updateFeedback, isPending } =
    useUpdateRecommendationsFeedbackMutation();

  const updatedFeedbackRef = useRef<RecommendationsFeedback[]>(feedback);

  const handleFeedbackUpdate = (
    recommendationId: string,
    feedbackType: "positive" | "negative"
  ) => {
    const existingFeedbackIndex = updatedFeedbackRef.current.findIndex(
      (item) => item.recommendationId === recommendationId
    );

    if (
      existingFeedbackIndex !== -1 &&
      updatedFeedbackRef.current[existingFeedbackIndex].feedbackType ===
        feedbackType
    ) {
      updatedFeedbackRef.current.splice(existingFeedbackIndex, 1);
    } else if (existingFeedbackIndex !== -1) {
      updatedFeedbackRef.current[existingFeedbackIndex].feedbackType =
        feedbackType;
    } else {
      updatedFeedbackRef.current.push({ recommendationId, feedbackType });
    }

    setUpdatedFeedback([...updatedFeedbackRef.current]);
    updateFeedback({ reportId, feedback: updatedFeedbackRef.current });
  };
  const recommendationFeedback = updatedFeedbackRef.current.filter(
    (feedback) => feedback.recommendationId === recommendation.id
  )[0]?.feedbackType;

  const handlePositiveFeedback = () => {
    handleFeedbackUpdate(recommendation.id, "positive");
  };

  const handleNegativeFeedback = () => {
    handleFeedbackUpdate(recommendation.id, "negative");
  };

  const { title, description, primaryLink, secondaryContent } = recommendation;

  const recommendationIcon =
    recommendationIcons?.general?.recommendationTypeIcon.find(
      (icon) => icon.title === recommendation.recommendationType
    )?.url;

  const durationIcon =
    recommendationIcons?.general?.recommendationTypeIcon.find(
      (icon) => icon.title === "duration"
    )?.url;

  const testAreaMapping: Record<string, string> = {
    spelling: "spelling",
    readingSpeed: "readingSpeed",
    workingMemory: "workingMemory",
    processingSpeed: "processingSpeed",
    verbalReasoning: "verbalReasoning",
    visualReasoning: "visualReasoning",
    readingComprehension: "readingComprehension",
    phonologicalAwareness: "phonologicalAwareness",
  };

  const scoreRank =
    scoringData &&
    Object.fromEntries(
      Object.entries(scoringData).map(([key, value]) => [
        testAreaMapping[key] || key,
        value.composite.scoreRank,
      ])
    );

  const normalizedTestArea = recommendation.testArea || "";
  const scoreRanking = scoreRank && scoreRank[normalizedTestArea];

  // const isClassroom =
  //   recommendation.category === RecommendationCategory.Classroom;
  const isClassroom = false;

  return (
    <Box
      display="flex"
      gap={15}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <Box display="flex" flex={1} flexDir="column" gap={theme.spacing.xs.px}>
        <Box display="flex" gap={15} justifyContent="space-between" flex={1}>
          <Text
            element="h4"
            variant="mdmd"
            unbold={isClassroom}
            margin={0}
            color={theme.colors.primary.black.hex}
          >
            {renderTitleWithLinks(
              title.replace(
                /{{score}}/g,
                `<span style="font-weight: 600">${scoreRanking || ""}</span>`
              ),
              student.first_names
            )}
          </Text>

          <Box
            style={{
              opacity: isHovered ? 1 : 0,
              transition: "opacity 0.3s ease",
            }}
          >
            <FeedbackThumbs
              disabled={isPending || isAdmin}
              onPositiveClick={handlePositiveFeedback}
              onNegativeClick={handleNegativeFeedback}
              feedback={recommendationFeedback}
              hideLabel={
                recommendation.category === RecommendationCategory.Classroom
              }
            />
          </Box>
        </Box>
        <Box display="flex" alignItems="center" gap={theme.spacing.xs.px}>
          <Box
            display="flex"
            gap={theme.spacing.xs.px}
            backgroundColor={theme.colors.ui.grey_04.hex}
            px={theme.spacing.xxs.px}
            borderRadius={theme.border.radius.lg.px}
            alignItems="center"
          >
            {recommendationIcon && (
              <Box>
                <Image
                  src={recommendationIcon}
                  alt={camelCaseToSentence(recommendation.recommendationType)}
                  width={10}
                  height={10}
                />
              </Box>
            )}
            <Text element="p" variant="sm">
              <strong>Type: </strong>
              {camelCaseToSentence(recommendation.recommendationType)}
            </Text>
          </Box>
          {recommendation.duration ? (
            <Box
              display="flex"
              gap={theme.spacing.xs.px}
              backgroundColor={theme.colors.ui.grey_04.hex}
              px={theme.spacing.xxs.px}
              borderRadius={theme.border.radius.lg.px}
              alignItems="center"
            >
              <Image
                src={durationIcon || ""}
                alt={"Duration"}
                width={10}
                height={10}
              />
              <Text element="p" variant="sm">
                <strong>Duration: </strong>
                {recommendation.duration}
              </Text>
            </Box>
          ) : null}
        </Box>

        {description && (
          <Text element="p" variant="lg">
            <div
              dangerouslySetInnerHTML={{
                __html: description
                  .replace(/{{name}}/g, student.first_names)
                  .replace(
                    /{{score}}/g,
                    `<span style="font-weight: 600">${
                      scoreRanking || ""
                    }</span>`
                  ),
              }}
            />
          </Text>
        )}

        {primaryLink && (
          <RenderLink
            url={primaryLink.url}
            title={primaryLink.title}
            description={primaryLink.description?.replace(
              /{{score}}/g,
              `<span style="font-weight: 600">${scoreRanking || ""}</span>`
            )}
            studentName={student.first_names}
            isPrimary
          />
        )}

        {secondaryContent?.map((link, i) => (
          <RenderLink
            key={i}
            url={link.url as string}
            title={link.title}
            description={link.description?.replace(
              /{{score}}/g,
              `<span style="font-weight: 600">${scoreRanking || ""}</span>`
            )}
            studentName={student.first_names}
          />
        ))}
      </Box>
    </Box>
  );
};
