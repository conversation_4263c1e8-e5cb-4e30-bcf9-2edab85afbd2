"use client";

import { useToast } from "@chakra-ui/react";

import { Modal } from "@/components/v2/Modal/Modal";
import { useRemoveTestMutation } from "@/hooks/mutations/useRemoveTestMutation";
import { theme } from "@/styles/theme";

type RemoveTestProps = {
  studentIds: number[];
  schoolId: number;
  isModalOpen: boolean;
  setIsModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
  setRowSelection?: React.Dispatch<React.SetStateAction<object>>;
};

export const RemoveTest = ({
  isModalOpen,
  setIsModalOpen,
  studentIds,
  schoolId,
  setRowSelection,
}: RemoveTestProps) => {
  const toast = useToast();
  const { mutate: removeTest } = useRemoveTestMutation();
  const pluralSuffix = studentIds.length === 1 ? "" : "s";

  const handleRemoveTest = () => {
    removeTest({ studentIds, schoolId });
    toast({
      title: `Test removed from ${studentIds.length} student${pluralSuffix}`,
      status: "success",
      duration: 5000,
      isClosable: true,
    });
    setIsModalOpen(false);
    if (setRowSelection) {
      setRowSelection({});
    }
  };

  return (
    <Modal
      isOpen={isModalOpen}
      setIsOpen={setIsModalOpen}
      title="Are you sure?"
      description={`Removing ${studentIds.length} student${pluralSuffix} from the test will refund ${studentIds.length} credit${pluralSuffix} to your account.`}
      overlayBackground={`rgba(${theme.colors.secondary.purple_01.rgbRaw}, 0.9)`}
      showCloseBtn
      size="xl"
      primaryCtaText="Cancel"
      onPrimaryCtaClick={() => setIsModalOpen(false)}
      primaryCtaVariant="secondary"
      secondaryCtaText="Remove"
      secondaryCtaVariant="primary"
      onSecondaryCtaClick={handleRemoveTest}
      secondaryCtaColor="red"
    />
  );
};
