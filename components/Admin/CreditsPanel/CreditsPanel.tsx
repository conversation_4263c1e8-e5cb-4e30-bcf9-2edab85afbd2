"use client";

import { <PERSON>, Spinner } from "@chakra-ui/react";
import { useEffect, useState } from "react";

import { Button, Text } from "@/components/v2";
import { useSchoolsByIdQuery } from "@/hooks/queries/useSchoolsByIdQuery";
import { theme } from "@/styles/theme";

import AddCredits from "../AddCredits/AddCredits";
import RemoveCredits from "../AddCredits/RemoveCredits";
import { AdminPanel } from "../AdminPanel/AdminPanel";

export const CreditsPanel = ({ schoolId }: { schoolId: number }) => {
  const {
    data: school,
    isLoading,
    refetch,
  } = useSchoolsByIdQuery({ schoolId });
  const [isCreditsModalOpen, setIsCreditModalOpen] = useState(false);
  const [isCreditsRemovalModalOpen, setIsCreditRemovalModalOpen] =
    useState(false);

  useEffect(() => {
    refetch();
  }, []);

  if (isLoading || !school) {
    return (
      <AdminPanel>
        <Spinner />
      </AdminPanel>
    );
  }
  return (
    <Box mb={theme.spacing.lg.px}>
      <AdminPanel>
        <Box display="flex" justifyContent="space-between">
          <Text element="h4" variant="md" mb={theme.spacing.sm.px}>
            Credits
          </Text>
          <Button size="sm" onClick={() => setIsCreditModalOpen(true)}>
            Add credits
          </Button>
        </Box>
        <Box
          bgColor={theme.colors.ui.alert_green_01.rgb}
          display="inline-block"
          py={theme.spacing.sm.px}
          px={theme.spacing.sm.px}
          mb={theme.spacing.sm.px}
          borderRadius={theme.border.radius.sm.px}
        >
          <Text
            element="h4"
            variant="sm"
            color={theme.colors.primary.white.hex}
          >
            {school.unassigned_credits} credits remaining
          </Text>
        </Box>
        <Text element="h4" variant="sm">
          {school.redeemed_credits} credits used
        </Text>
        {school.unassigned_credits > 0 && (
          <Box mt={theme.spacing.sm.px}>
            <Text
              element="h4"
              variant="xs"
              textDecoration="underline"
              cursor="pointer"
              color={theme.colors.ui.alert_red_01.hex}
              onClick={() => setIsCreditRemovalModalOpen(true)}
            >
              Remove credits
            </Text>
          </Box>
        )}
        <AddCredits
          schoolId={school.school_id}
          orgId={school.org_id}
          isModalOpen={isCreditsModalOpen}
          setIsModalOpen={setIsCreditModalOpen}
        />

        <RemoveCredits
          schoolId={school.school_id}
          orgId={school.org_id}
          isModalOpen={isCreditsRemovalModalOpen}
          setIsModalOpen={setIsCreditRemovalModalOpen}
        />
      </AdminPanel>
    </Box>
  );
};
