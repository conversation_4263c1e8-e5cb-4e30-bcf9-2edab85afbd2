import { Box } from "@chakra-ui/react";
import React from "react";

import { theme } from "@/styles/theme";

type AdminPanelProps = {
  children: React.ReactNode;
};

export const AdminPanel = ({ children }: AdminPanelProps) => {
  return (
    <Box
      pos="relative"
      p={theme.spacing.lg.px}
      border="1px solid rgba(0, 0, 0, 0.10)"
      borderRadius={theme.border.radius.xxl.px}
    >
      {children}
    </Box>
  );
};
