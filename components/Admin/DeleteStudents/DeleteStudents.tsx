"use client";

import { Modal } from "@/components/v2/Modal/Modal";
import { useDeleteStudentsMutation } from "@/hooks/mutations/useDeleteStudentsMutation";
import { theme } from "@/styles/theme";

type DeleteStudentsProps = {
  studentIds: number[];
  isModalOpen: boolean;
  setIsModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
  setRowSelection?: React.Dispatch<React.SetStateAction<object>>;
};

export const DeleteStudents = ({
  isModalOpen,
  setIsModalOpen,
  studentIds,
  setRowSelection,
}: DeleteStudentsProps) => {
  const { mutate: deleteStudents } = useDeleteStudentsMutation();
  const deletionText =
    studentIds.length === 1 ? "this student" : "the selected students";

  const handleDeleteStudents = async () => {
    await deleteStudents({ studentIds });
    setIsModalOpen(false);
    if (setRowSelection) {
      setRowSelection({});
    }
  };
  return (
    <div>
      <Modal
        isOpen={isModalOpen}
        setIsOpen={setIsModalOpen}
        title="Are you sure?"
        description={`If you delete ${deletionText}, you will lose all of their data and will not be able to access again.`}
        overlayBackground={`rgba(${theme.colors.secondary.purple_01.rgbRaw}, 0.9)`}
        showCloseBtn
        size="xl"
        primaryCtaText="Cancel"
        onPrimaryCtaClick={() => setIsModalOpen(false)}
        primaryCtaVariant="secondary"
        secondaryCtaText="Delete"
        secondaryCtaVariant="primary"
        onSecondaryCtaClick={handleDeleteStudents}
        secondaryCtaColor="red"
      />
    </div>
  );
};
