"use client";
import { createClientComponentClient } from "@supabase/auth-helpers-nextjs";
import { format } from "date-fns";
import { useState } from "react";

import { Button } from "@/components/v2";

interface School {
  school_name: string;
  school_type: string;
}

interface Student {
  student_code: string;
}

interface Credit {
  status: string;
  redeemed_at: string | null;
  school: School | null;
  student: Student | null;
}

const supabase = createClientComponentClient();

const exportToCSV = async () => {
  const today = new Date();
  const { data, error } = (await supabase.from("credits").select(`
      status, 
    redeemed_at, 
    student:student_id ( student_code ), 
    school:school_id ( school_name, school_type )
    `)) as { data: Credit[]; error: any };

  if (error) {
    console.error("Error fetching data from Supabase", error);
    return;
  }

  if (!data || !Array.isArray(data)) {
    console.error("No data found or invalid format");
    return;
  }

  const { data: studentReportsData } = await supabase
    .from("studentReports")
    .select("student_code, hide_report");

  if (!studentReportsData) {
    console.error("No data found or invalid format");
    return;
  }

  const studentReportsMap = studentReportsData.reduce(
    (acc, report) => {
      acc[report.student_code] = report.hide_report;
      return acc;
    },
    {} as { [key: string]: boolean }
  );

  const csvData = data.map((credit: Credit) => {
    const school = credit.school || { school_name: "", school_type: "" };
    const student = credit.student || { student_code: "" };

    let hide_report;
    if (credit.status === "Redeemed") {
      const hide_report_value = studentReportsMap[student.student_code];

      if (hide_report_value === true) {
        hide_report = "Yes";
      } else if (hide_report_value === false) {
        hide_report = "No";
      }
    }

    return {
      school_name: school.school_name,
      school_type: school.school_type,
      student_code: student.student_code,
      status: credit.status || "",
      redeemed_at: credit.redeemed_at || "",
      hide_report: hide_report || "",
    };
  });

  const csvHeader = [
    "School Name",
    "School Type",
    "Student Code",
    "Status",
    "Redeemed At",
    "Report hidden?",
  ];

  const csvRows = csvData.map((row) =>
    [
      `"${row.school_name}"`,
      `"${row.school_type}"`,
      `"${row.student_code}"`,
      `"${row.status}"`,
      `"${row.redeemed_at}"`,
      `"${row.hide_report !== undefined ? row.hide_report : ""}"`,
    ].join(",")
  );

  const csvContent = [csvHeader.join(","), ...csvRows].join("\n");

  const blob = new Blob([csvContent], { type: "text/csv" });
  const url = window.URL.createObjectURL(blob);

  const link = document.createElement("a");
  link.href = url;
  link.setAttribute("download", `credits-${format(today, "dd-mm-yyyy")}.csv`);
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

const ExportButton = () => {
  const [isLoading, setIsLoading] = useState(false);

  const handleDownload = async () => {
    try {
      setIsLoading(true);
      await exportToCSV();
    } catch (error) {
      console.error("Error during CSV export", error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Button size="sm" onClick={handleDownload} disabled={isLoading}>
      {isLoading ? "Downloading..." : "Download Credits CSV"}
    </Button>
  );
};

export default ExportButton;
