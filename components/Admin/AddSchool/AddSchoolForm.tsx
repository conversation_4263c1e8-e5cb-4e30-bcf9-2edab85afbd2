"use client";

import {
  Box,
  FormLabel,
  HStack,
  Input,
  Select,
  useToast,
} from "@chakra-ui/react";
import { useQueryClient } from "@tanstack/react-query";
import { useFormik } from "formik";
import type { Asserts } from "yup";
import * as Yup from "yup";

import { Button } from "@/components/v2/Button/Button";
import { Modal } from "@/components/v2/Modal/Modal";
import { Text } from "@/components/v2/Text/Text";
import countiesData from "@/data/counties.json";
import { useOrgsQuery } from "@/hooks/queries/useOrgsQuery";
import { CACHE_KEYS } from "@/lib/cache";
import { addSchool } from "@/server/admin/addSchool";
import { theme } from "@/styles/theme";
import { County } from "@/types/admin";

type AddSchoolFormProps = {
  isModalOpen: boolean;
  setIsModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
};

const schoolValidation = Yup.object().shape({
  schoolName: Yup.string().required("Required"),
  contactName: Yup.string().required("Required"),
  email: Yup.string().email("Invalid email address").required("Required"),
  type: Yup.string().required("Required"),
  age: Yup.string().required("Required"),
  location: Yup.string().required("Required"),
  organisation: Yup.string(),
});

export type School = Asserts<typeof schoolValidation>;

const AddSchoolForm = ({ isModalOpen, setIsModalOpen }: AddSchoolFormProps) => {
  const { data: orgData } = useOrgsQuery();
  const queryClient = useQueryClient();
  const counties: County[] = countiesData;
  counties.sort((a, b) => a.name.localeCompare(b.name));

  const toast = useToast();
  const formik = useFormik({
    initialValues: {
      schoolName: "",
      contactName: "",
      email: "",
      type: "",
      age: "",
      location: "",
      organisation: "",
    },
    validationSchema: schoolValidation,
    onSubmit: async (values) => {
      const customerId = await addSchool(values);

      if (customerId) {
        queryClient.invalidateQueries({ queryKey: [CACHE_KEYS.SCHOOLS] });
        queryClient.invalidateQueries({ queryKey: [CACHE_KEYS.CREDITS] });

        setIsModalOpen(false);
        toast({
          title: "Customer added",
          description: "The customer will receive an invite to signup shortly.",
          status: "success",
          duration: 5000,
          isClosable: true,
        });

        formik.resetForm();
      }
    },
  });
  return (
    <div>
      <Modal
        isOpen={isModalOpen}
        setIsOpen={setIsModalOpen}
        title="Add customer"
        overlayBackground={`rgba(${theme.colors.secondary.purple_01.rgbRaw}, 0.9)`}
        showCloseBtn
        size="xl"
      >
        <form onSubmit={formik.handleSubmit}>
          <Box display="flex" flexDir="column" gap="24px">
            <HStack gap="20px">
              <Box flex="1">
                <HStack
                  alignItems="center"
                  justifyContent="space-between"
                  mb="10px"
                >
                  <FormLabel htmlFor="schoolName" m={0}>
                    School name
                  </FormLabel>{" "}
                  {formik.touched.schoolName && formik.errors.schoolName && (
                    <Text
                      element="p"
                      variant="xxs"
                      color={theme.colors.ui.alert_red_01.rgb}
                    >
                      {formik.errors.schoolName}
                    </Text>
                  )}
                </HStack>
                <Input
                  id="schoolName"
                  name="schoolName"
                  type="text"
                  onChange={formik.handleChange}
                  value={formik.values.schoolName}
                />
              </Box>
            </HStack>
            <Box>
              <HStack gap="20px">
                <Box flex="1">
                  <HStack
                    alignItems="center"
                    justifyContent="space-between"
                    mb="10px"
                  >
                    <FormLabel htmlFor="contactName" m={0}>
                      Primary contact name
                    </FormLabel>{" "}
                    {formik.touched.contactName &&
                      formik.errors.contactName && (
                        <Text
                          element="p"
                          variant="xxs"
                          color={theme.colors.ui.alert_red_01.rgb}
                        >
                          {formik.errors.contactName}
                        </Text>
                      )}
                  </HStack>
                  <Input
                    id="contactName"
                    name="contactName"
                    type="text"
                    onChange={formik.handleChange}
                    value={formik.values.contactName}
                  />
                </Box>
                <Box flex="1">
                  <HStack
                    alignItems="center"
                    justifyContent="space-between"
                    mb="10px"
                  >
                    <FormLabel htmlFor="email" m={0}>
                      Primary email
                    </FormLabel>{" "}
                    {formik.touched.email && formik.errors.email && (
                      <Text
                        element="p"
                        variant="xxs"
                        color={theme.colors.ui.alert_red_01.rgb}
                      >
                        {formik.errors.email}
                      </Text>
                    )}
                  </HStack>
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    onChange={formik.handleChange}
                    value={formik.values.email}
                    variant="outline"
                  />
                </Box>
              </HStack>
            </Box>
            <HStack gap="20px">
              <Box flex="1">
                <HStack
                  alignItems="center"
                  justifyContent="space-between"
                  mb="10px"
                >
                  <FormLabel htmlFor="type" m={0}>
                    School type
                  </FormLabel>{" "}
                  {formik.touched.type && formik.errors.type && (
                    <Text
                      element="p"
                      variant="xxs"
                      color={theme.colors.ui.alert_red_01.rgb}
                    >
                      {formik.errors.type}
                    </Text>
                  )}
                </HStack>
                <Select
                  id="type"
                  name="type"
                  placeholder="Select a type"
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  value={formik.values.type}
                >
                  <option value="State">State</option>
                  <option value="Private">Private</option>
                </Select>
              </Box>
              <Box flex="1">
                <HStack
                  alignItems="center"
                  justifyContent="space-between"
                  mb="10px"
                >
                  <FormLabel htmlFor="age" m={0}>
                    School age
                  </FormLabel>{" "}
                  {formik.touched.age && formik.errors.age && (
                    <Text
                      element="p"
                      variant="xxs"
                      color={theme.colors.ui.alert_red_01.rgb}
                    >
                      {formik.errors.age}
                    </Text>
                  )}
                </HStack>
                <Select
                  id="age"
                  name="age"
                  placeholder="Select age"
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  value={formik.values.age}
                >
                  <option value="Primary">Primary</option>
                  <option value="Secondary">Secondary</option>
                  <option value="All">All</option>
                </Select>
              </Box>
            </HStack>
            <HStack gap="20px">
              <Box flex="1">
                <HStack
                  alignItems="center"
                  justifyContent="space-between"
                  mb="10px"
                >
                  <FormLabel htmlFor="type" m={0}>
                    Location
                  </FormLabel>{" "}
                  {formik.touched.location && formik.errors.location && (
                    <Text
                      element="p"
                      variant="xxs"
                      color={theme.colors.ui.alert_red_01.rgb}
                    >
                      {formik.errors.location}
                    </Text>
                  )}
                </HStack>
                <Select
                  id="location"
                  name="location"
                  placeholder="Select a location"
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  value={formik.values.location}
                >
                  {counties.map((county) => (
                    <option key={county.abbreviation} value={county.name}>
                      {county.name}
                    </option>
                  ))}
                </Select>
              </Box>
              <Box flex="1">
                <HStack
                  alignItems="center"
                  justifyContent="space-between"
                  mb="10px"
                >
                  <FormLabel htmlFor="age" m={0}>
                    Organisation
                  </FormLabel>{" "}
                  {formik.touched.organisation &&
                    formik.errors.organisation && (
                      <Text
                        element="p"
                        variant="xxs"
                        color={theme.colors.ui.alert_red_01.rgb}
                      >
                        {formik.errors.organisation}
                      </Text>
                    )}
                </HStack>
                <Select
                  id="organisation"
                  name="organisation"
                  placeholder="Select organisation"
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  value={formik.values.organisation}
                >
                  {orgData?.map((org) => (
                    <option key={org.org_id} value={org.org_id}>
                      {org.org_name}
                    </option>
                  ))}
                </Select>
              </Box>
            </HStack>
          </Box>
          <Box mt="30px" display="flex" justifyContent="center">
            <Button type="submit">Add</Button>
          </Box>
        </form>
      </Modal>
    </div>
  );
};

export default AddSchoolForm;
