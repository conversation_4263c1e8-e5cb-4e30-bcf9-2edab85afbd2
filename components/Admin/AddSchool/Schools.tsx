"use server";

import { AdminPanel } from "../AdminPanel/AdminPanel";
import { SchoolsTable } from "../SchoolsTable/SchoolsTable";
import { AddSchool } from "./AddSchool";

type SchoolsProps = {
  numRows: number;
};
export const Schools = async ({ numRows }: SchoolsProps) => {
  return (
    <>
      {numRows > 0 ? (
        <AdminPanel>
          <SchoolsTable />
        </AdminPanel>
      ) : (
        <AddSchool />
      )}
    </>
  );
};
