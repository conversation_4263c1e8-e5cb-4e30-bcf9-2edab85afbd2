"use client";

import { Box } from "@chakra-ui/react";
import { useState } from "react";

import { Button } from "@/components/v2/Button/Button";
import { Text } from "@/components/v2/Text/Text";
import { theme } from "@/styles/theme";

import { AdminPanel } from "..";
import AddCustomerForm from "./AddSchoolForm";

export const AddSchool = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  return (
    <AdminPanel>
      <Box
        borderRadius={theme.border.radius.xxl.px}
        bgColor={theme.colors.secondary.purple_05.hex}
        display="flex"
        flexDir="column"
        alignItems="center"
        justifyContent="center"
        textAlign="center"
        minH="300px"
        gap="12px"
      >
        <Text element="h3" variant="sm">
          👋 Add our first school!
        </Text>
        <Button
          size="sm"
          onClick={() => {
            setIsModalOpen(true);
          }}
        >
          Add school
        </Button>
      </Box>
      <AddCustomerForm
        isModalOpen={isModalOpen}
        setIsModalOpen={setIsModalOpen}
      />
    </AdminPanel>
  );
};
