"use client";

import { <PERSON>, Spinner, Switch } from "@chakra-ui/react";
import {
  createColumnH<PERSON>per,
  RowSelectionState,
  SortingState,
} from "@tanstack/react-table";
import { format, parseISO } from "date-fns";
import Link from "next/link";
import { useState } from "react";

import { ScoreLevel } from "@/components/ScoreLevel/ScoreLevel";
import { Text } from "@/components/v2";
import { useToggleHideReportMutation } from "@/hooks/mutations/useToggleHideReportMutation";
import { useAnalysisBySchoolQuery } from "@/hooks/queries/useAnalysisBySchoolQuery";
import { theme } from "@/styles/theme";
import { AnalysisData } from "@/types/analysis";

import { AdminPanel } from "../AdminPanel/AdminPanel";
import { DataTable } from "../DataTable/DataTable";

export const StudentsPanel = ({ schoolId }: { schoolId: number }) => {
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});
  const [sorting, setSorting] = useState<SortingState>([]);

  const { mutate: toggleHideReport } = useToggleHideReportMutation();

  const { data: analysisResponse, isLoading: analysisLoading } =
    useAnalysisBySchoolQuery({ schoolId: schoolId });

  if (analysisLoading) {
    return (
      <AdminPanel>
        <Spinner />
      </AdminPanel>
    );
  }

  if (!analysisResponse) {
    return null;
  }

  const analysisData = analysisResponse.data;

  const handleToggle = (reportId: number, currentValue: boolean) => {
    toggleHideReport({ reportId, hideReport: !currentValue });
  };

  const columnHelper = createColumnHelper<AnalysisData>();

  const columns = [
    columnHelper.accessor("student", {
      cell: ({ cell, row }) => {
        const name = `${cell.getValue()["surname"]}, ${cell
          .getValue()
          ["first_names"].substring(1, 0)}`;
        return (
          <Link
            href={`/admin/school/${schoolId}/report/${row.original.student_code}`}
            className="blueLink"
          >
            <Text
              color={theme.colors.ui.link.hex}
              variant="sm"
              fontWeight={600}
              cursor="pointer"
              _hover={{ textDecoration: "underline" }}
            >
              {name}
            </Text>
          </Link>
        );
      },
      header: "Name",
    }),
    columnHelper.accessor("student.year", {
      cell: (info) => info.getValue(),
      header: "Year",
    }),
    columnHelper.accessor("analysis.levelOfNeedRisk", {
      cell: (info) => {
        const level = info.getValue();
        return <ScoreLevel level={level} type="text" />;
      },
      header: "Need level",
    }),
    columnHelper.accessor("analysis.dyslexiaRisk", {
      cell: (info) => {
        const level = info.getValue();
        return <ScoreLevel level={level} type="arrow" />;
      },
      header: "Dyslexia risk",
    }),
    columnHelper.accessor("assessment_meta.dateTaken", {
      cell: (info) => format(parseISO(info.getValue()), "dd/MM/yy"),
      header: "Date taken",
      sortingFn: "datetime",
    }),
    columnHelper.accessor("hide_report", {
      cell: ({ row }) => {
        const isVisible = !row.original.hide_report;
        return (
          <Switch
            isChecked={isVisible}
            onChange={() =>
              handleToggle(row.original.id, row.original.hide_report)
            }
          />
        );
      },
      header: "Visible?",
    }),
  ];

  return (
    <Box mb={theme.spacing.lg.px}>
      <AdminPanel>
        <Box display="flex" justifyContent="space-between">
          <Text element="h4" variant="md" mb={theme.spacing.sm.px}>
            Reports
          </Text>
        </Box>
        <Box overflow="scroll">
          <Box>
            <DataTable
              columns={columns}
              data={analysisData}
              rowSelection={rowSelection}
              setRowSelection={setRowSelection}
              sorting={sorting}
              setSorting={setSorting}
              hideCheckboxes
            />
          </Box>
        </Box>
      </AdminPanel>
    </Box>
  );
};
