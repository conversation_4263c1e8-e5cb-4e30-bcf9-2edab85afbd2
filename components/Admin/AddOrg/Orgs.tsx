"use server";

import { AdminPanel } from "../AdminPanel/AdminPanel";
import { OrgsTable } from "../OrgsTable/OrgsTable";
import { AddOrg } from "./AddOrg";

type OrgsProps = {
  numRows: number;
};
export const Orgs = async ({ numRows }: OrgsProps) => {
  return (
    <>
      {numRows > 0 ? (
        <AdminPanel>
          <OrgsTable />
        </AdminPanel>
      ) : (
        <AddOrg />
      )}
    </>
  );
};
