"use client";

import {
  Box,
  FormLabel,
  HStack,
  Input,
  Select,
  useToast,
} from "@chakra-ui/react";
import { useQueryClient } from "@tanstack/react-query";
import { useFormik } from "formik";
import type { Asserts } from "yup";
import * as Yup from "yup";

import { Button } from "@/components/v2/Button/Button";
import { Modal } from "@/components/v2/Modal/Modal";
import { Text } from "@/components/v2/Text/Text";
import { CACHE_KEYS } from "@/lib/cache";
import { addOrg } from "@/server/admin/addOrg";
import { theme } from "@/styles/theme";

type AddOrgFormProps = {
  isModalOpen: boolean;
  setIsModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
};

const orgValidation = Yup.object().shape({
  organisationName: Yup.string().required("Required"),
  contactName: Yup.string().required("Required"),
  email: Yup.string().email("Invalid email address").required("Required"),
});

export type Org = Asserts<typeof orgValidation>;

const AddOrgForm = ({ isModalOpen, setIsModalOpen }: AddOrgFormProps) => {
  const queryClient = useQueryClient();

  const toast = useToast();
  const formik = useFormik({
    initialValues: {
      organisationName: "",
      contactName: "",
      email: "",
    },
    validationSchema: orgValidation,
    onSubmit: async (values) => {
      const customerId = await addOrg(values);

      if (customerId) {
        queryClient.invalidateQueries({ queryKey: [CACHE_KEYS.ORGS] });

        setIsModalOpen(false);
        toast({
          title: "Organisation added",
          description:
            "The organisation will receive an invite to signup shortly.",
          status: "success",
          duration: 5000,
          isClosable: true,
        });
      }
    },
  });
  return (
    <div>
      <Modal
        isOpen={isModalOpen}
        setIsOpen={setIsModalOpen}
        title="Add organisation"
        overlayBackground={`rgba(${theme.colors.secondary.purple_01.rgbRaw}, 0.9)`}
        showCloseBtn
        size="xl"
      >
        <form onSubmit={formik.handleSubmit}>
          <Box display="flex" flexDir="column" gap="24px">
            <HStack gap="20px">
              <Box flex="1">
                <HStack
                  alignItems="center"
                  justifyContent="space-between"
                  mb="10px"
                >
                  <FormLabel htmlFor="organisationName" m={0}>
                    Organisation name
                  </FormLabel>{" "}
                  {formik.touched.organisationName &&
                    formik.errors.organisationName && (
                      <Text
                        element="p"
                        variant="xxs"
                        color={theme.colors.ui.alert_red_01.rgb}
                      >
                        {formik.errors.organisationName}
                      </Text>
                    )}
                </HStack>
                <Input
                  id="organisationName"
                  name="organisationName"
                  type="text"
                  onChange={formik.handleChange}
                  value={formik.values.organisationName}
                />
              </Box>
            </HStack>
            <Box>
              <HStack gap="20px">
                <Box flex="1">
                  <HStack
                    alignItems="center"
                    justifyContent="space-between"
                    mb="10px"
                  >
                    <FormLabel htmlFor="contactName" m={0}>
                      Primary contact name
                    </FormLabel>{" "}
                    {formik.touched.contactName &&
                      formik.errors.contactName && (
                        <Text
                          element="p"
                          variant="xxs"
                          color={theme.colors.ui.alert_red_01.rgb}
                        >
                          {formik.errors.contactName}
                        </Text>
                      )}
                  </HStack>
                  <Input
                    id="contactName"
                    name="contactName"
                    type="text"
                    onChange={formik.handleChange}
                    value={formik.values.contactName}
                  />
                </Box>
                <Box flex="1">
                  <HStack
                    alignItems="center"
                    justifyContent="space-between"
                    mb="10px"
                  >
                    <FormLabel htmlFor="email" m={0}>
                      Primary email
                    </FormLabel>{" "}
                    {formik.touched.email && formik.errors.email && (
                      <Text
                        element="p"
                        variant="xxs"
                        color={theme.colors.ui.alert_red_01.rgb}
                      >
                        {formik.errors.email}
                      </Text>
                    )}
                  </HStack>
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    onChange={formik.handleChange}
                    value={formik.values.email}
                    variant="outline"
                  />
                </Box>
              </HStack>
            </Box>
          </Box>
          <Box mt="30px" display="flex" justifyContent="center">
            <Button type="submit">Add</Button>
          </Box>
        </form>
      </Modal>
    </div>
  );
};

export default AddOrgForm;
