"use client";

import { Box } from "@chakra-ui/react";
import { ChevronLeftIcon } from "@heroicons/react/24/solid";
import Link from "next/link";
import { useRouter } from "next/navigation";
import React, { useState } from "react";

import { Button } from "@/components/v2/Button/Button";
import { theme } from "@/styles/theme";

import { Text } from "../../v2";
import AddOrgForm from "../AddOrg/AddOrgForm";
import AddSchoolForm from "../AddSchool/AddSchoolForm";
import { AddStudentsMenu } from "../AddStudentsMenu/AddStudentsMenu";

type AdminHeroProps = {
  heading: string;
  description?: string | React.ReactNode;
  belowDescription?: string | React.ReactNode;
  variant?: "addSchool" | "addOrg" | "test";
  backLink?: string;
  backLinkUrl?: string;
  numStudents?: number;
};

export const AdminHero = ({
  heading,
  description,
  belowDescription,
  variant,
  backLink,
  backLinkUrl,
  numStudents,
}: AdminHeroProps) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isOrgModalOpen, setIsOrgModalOpen] = useState(false);
  const router = useRouter();

  return (
    <>
      <Box minH={150} pb={theme.spacing.lg.px}>
        {backLink && backLinkUrl && (
          <Link href={backLinkUrl}>
            <Box display="flex" gap={theme.spacing.xs.px}>
              <ChevronLeftIcon width={18} height={18} color="white" />
              <Text
                variant="xs"
                mb={theme.spacing.lg.px}
                textDecoration="underline"
                fontWeight={600}
                color={theme.colors.primary.white.hex}
              >
                {backLink}
              </Text>
            </Box>
          </Link>
        )}
        <Text
          element="h1"
          fontSize={theme.v2Text.headings["2xl"].fontSize}
          fontWeight={theme.v2Text.headings.xl.fontWeight}
          color={theme.colors.primary.white.hex}
        >
          {heading}
        </Text>
        {description && (
          <Text
            element="h5"
            fontSize={theme.v2Text.headings.mdsm.fontSize}
            fontWeight={theme.v2Text.headings.xl.fontWeight}
            color={theme.colors.primary.white.hex}
            mt={theme.spacing.sm.px}
            maxW="600px"
            lineHeight="130%"
          >
            {description}
          </Text>
        )}
        {belowDescription && (
          <Box mt={theme.spacing.md.px}>{belowDescription}</Box>
        )}
        {variant === "addSchool" && (
          <Box mt="25px">
            <Button
              size="sm"
              onClick={() => {
                setIsModalOpen(true);
              }}
            >
              Add school
            </Button>
          </Box>
        )}
        {variant === "addOrg" && (
          <Box mt="25px">
            <Button
              size="sm"
              onClick={() => {
                setIsOrgModalOpen(true);
              }}
            >
              Add organisation
            </Button>
          </Box>
        )}
        {variant === "test" && numStudents && numStudents > 0 && (
          <Box mt="25px">
            <AddStudentsMenu numStudents={numStudents} />
          </Box>
        )}
      </Box>
      {variant === "addSchool" && (
        <AddSchoolForm
          isModalOpen={isModalOpen}
          setIsModalOpen={setIsModalOpen}
        />
      )}

      {variant === "addOrg" && (
        <AddOrgForm
          isModalOpen={isOrgModalOpen}
          setIsModalOpen={setIsOrgModalOpen}
        />
      )}
    </>
  );
};
