import { Box } from "@chakra-ui/react";
import Image from "next/image";

import { Text } from "@/components/v2/Text/Text";
import { theme } from "@/styles/theme";
import { CognitiveProfile } from "@/types/analysis";
import {
  AreasOfNeedFallbackQuery,
  AreasOfNeedQuery,
} from "@/types/graphql/graphql";

import { RenderLink } from "../Recommendations/components/RenderLink";

type AreasOfNeedProps = {
  scoringData: CognitiveProfile | null;
  areasOfNeed: AreasOfNeedQuery;
  areasOfNeedFallback: AreasOfNeedFallbackQuery;
  student: any;
};

export const AreasOfNeed = ({
  scoringData,
  areasOfNeed,
  areasOfNeedFallback,
  student,
}: AreasOfNeedProps) => {
  if (!scoringData) {
    return null;
  }

  const fallback = areasOfNeedFallback.general;
  const firstName = student.first_names || "";

  const testAreaMapping: Record<string, string> = {
    spelling: "spelling",
    readingSpeed: "reading_speed",
    workingMemory: "memory",
    processingSpeed: "speed",
    verbalReasoning: "verbal",
    visualReasoning: "visual",
    readingComprehension: "reading_comprehension",
    phonologicalAwareness: "phonological",
  };

  const levelColorMapping: Record<string, string> = {
    red: theme.colors.ui.alert_red_02.hex,
    orange: theme.colors.ui.alert_orange_03.hex,
    yellow: theme.colors.tertiary.yellow_03.hex,
    green: theme.colors.ui.alert_green_02.hex,
  };

  const scores = Object.fromEntries(
    Object.entries(scoringData).map(([key, value]) => [
      testAreaMapping[key] || key,
      Number(value.composite.standardScore),
    ])
  );

  const scoreRank = Object.fromEntries(
    Object.entries(scoringData).map(([key, value]) => [
      testAreaMapping[key] || key,
      value.composite.scoreRank,
    ])
  );

  const relevantAreas = areasOfNeed.allAreaOfNeeds
    .filter((area) => {
      const normalizedTestArea = area.testArea.toLowerCase();
      const actualScore = Number(scores[normalizedTestArea]);
      const minScore = Number(area.minimumScore);
      const maxScore = Number(area.maximumScore);

      const pass = actualScore >= minScore && actualScore < maxScore;

      return pass;
    })
    .sort((a, b) => {
      const scoreA = Number(scores[a.testArea.toLowerCase()]);
      const scoreB = Number(scores[b.testArea.toLowerCase()]);
      return scoreA - scoreB;
    });

  return (
    <Box
      display="flex"
      flexDir="column"
      gap={theme.spacing.md.px}
      mt={theme.spacing.xl.px}
    >
      <Text element="h4" variant="xl">
        Strengths and challenges
      </Text>
      {relevantAreas.length > 0 ? (
        relevantAreas.map((area) => {
          const normalizedTestArea = area.testArea.toLowerCase();
          const scoreRanking = scoreRank[normalizedTestArea];

          const scoreDescription = area.description
            .replace(
              /{{score}}/g,
              `<strong>${scoreRanking.toString()}</strong>`
            )
            .replace(/{{name}}/g, firstName);

          const scoreTitle = area.title
            .replace(
              /{{score}}/g,
              `<strong>${scoreRanking.toString()}</strong>`
            )
            .replace(/{{name}}/g, firstName);

          return (
            <Box
              key={area.id}
              display="flex"
              flexDirection="column"
              padding={theme.spacing.lg.px}
              borderRadius={theme.border.radius.xxl.px}
              gap={"12px"}
              border="1px solid rgba(0, 0, 0, 0.10)"
            >
              <Box display="flex" alignItems="center" gap={theme.spacing.sm.px}>
                <Box
                  display="inline-flex"
                  alignItems="center"
                  bgColor={levelColorMapping[area.flagColour || ""]}
                  borderRadius="40px"
                >
                  {area.icon.url && (
                    <Image
                      width={40}
                      height={40}
                      src={area.icon.url}
                      alt={scoreTitle || ""}
                    />
                  )}
                  <Text
                    element="h4"
                    variant="md"
                    mx={theme.spacing.md.px}
                    color={theme.colors.primary.black.hex}
                  >
                    {scoreTitle}
                  </Text>
                </Box>
              </Box>
              <Text
                element="p"
                variant="lg"
                color={theme.colors.primary.black.hex}
              >
                <div dangerouslySetInnerHTML={{ __html: scoreDescription }} />
              </Text>
              {area.linkTitle && area.linkUrl && (
                <RenderLink
                  url={area.linkUrl}
                  title={area.linkTitle}
                  description=""
                  studentName=""
                  isPrimary
                />
              )}
            </Box>
          );
        })
      ) : (
        <Box
          display="flex"
          flexDirection="column"
          padding={theme.spacing.lg.px}
          borderRadius={theme.border.radius.xxl.px}
          gap={"12px"}
          border="1px solid rgba(0, 0, 0, 0.10)"
        >
          <Box display="flex" alignItems="center" gap={theme.spacing.sm.px}>
            <Box
              display="inline-flex"
              alignItems="center"
              bgColor={theme.colors.ui.alert_green_02.hex}
              borderRadius="40px"
            >
              {areasOfNeedFallback && (
                <Image
                  width={40}
                  height={40}
                  src={fallback?.aonFallbackIcon.url || ""}
                  alt={fallback?.aonFallbackTitle || ""}
                />
              )}
              <Text
                element="h4"
                variant="md"
                mx={theme.spacing.md.px}
                color={theme.colors.primary.black.hex}
              >
                {fallback?.aonFallbackTitle}
              </Text>
            </Box>
          </Box>
          <Text element="p" variant="lg" color={theme.colors.primary.black.hex}>
            <div
              dangerouslySetInnerHTML={{
                __html:
                  fallback?.aonFallbackDescription.replace(
                    /{{name}}/g,
                    firstName
                  ) || "",
              }}
            />
          </Text>
        </Box>
      )}
    </Box>
  );
};
