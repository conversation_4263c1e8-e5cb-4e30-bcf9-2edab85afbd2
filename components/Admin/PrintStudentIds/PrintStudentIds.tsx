import { Document, Page, StyleSheet, Text, View } from "@react-pdf/renderer";
import React from "react";

const styles = StyleSheet.create({
  page: {
    padding: 20,
    backgroundColor: "#FFFFFF",
  },
  row: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 10,
    break: "avoid",
  },
  section: {
    border: "1px solid #000000",
    borderRadius: 10,
    padding: 10,
    width: "48%",
  },
  header: {
    fontSize: 12,
    marginBottom: 5,
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
  },
  website: {
    fontSize: 10,
    color: "#555555",
    marginTop: 10,
  },
  link: {
    fontSize: 12,
    color: "#000000",
    marginTop: 5,
  },
  code: {
    fontSize: 16,
    marginTop: 5,
    fontWeight: "bold",
  },
});

export const PrintableIds = ({ data }) => {
  const groupedData = data.reduce((rows, student, index) => {
    if (index % 2 === 0) rows.push([student]);
    else rows[rows.length - 1].push(student);
    return rows;
  }, []);

  return (
    <Document>
      <Page size="A4" style={styles.page}>
        {groupedData.map((group, rowIndex) => (
          <View key={`row-${rowIndex}`} style={styles.row}>
            {group.map((student) => (
              <View key={student.student_id} style={styles.section}>
                <View style={styles.header}>
                  <Text>{`${student.surname}, ${student.first_names}`}</Text>
                  <Text>Year {student.year}</Text>
                </View>
                <Text style={styles.website}>Go to this website</Text>
                <Text style={styles.link}>https://test.talamo.co.uk</Text>
                <Text style={styles.website}>Enter this code</Text>
                <Text style={styles.code}>{student.student_code}</Text>
              </View>
            ))}
          </View>
        ))}
      </Page>
    </Document>
  );
};
