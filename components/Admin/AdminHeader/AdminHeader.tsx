"use client";

import { Box } from "@chakra-ui/react";
import {
  ArrowRightStartOnRectangleIcon,
  BeakerIcon,
  BoltIcon,
  Cog6ToothIcon,
} from "@heroicons/react/24/solid";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";

import { Button } from "@/components/v2/Button/Button";
import { Logo } from "@/components/v2/Logo/Logo";
import { Text } from "@/components/v2/Text/Text";
import { theme } from "@/styles/theme";

type AdminHeaderProps = {
  type?: "admin" | "school";
};

export const AdminHeader = ({ type = "admin" }: AdminHeaderProps) => {
  const router = useRouter();
  const handleCloseButtonClick = () => router.push("/logout");
  const pathname = usePathname();
  const isOrgActive = pathname?.includes("organisations");
  const isTestsActive = pathname === "/school";
  const isAnalysisActive = pathname?.includes("/school/analysis");
  const isSchoolActive = pathname?.includes("school") || pathname === "/admin";

  const isAccountActive =
    pathname?.includes("/school/account") || pathname === "/account";

  if (type === "school") {
    return (
      <Box
        px={theme.spacing.ml.px}
        py={theme.spacing.sm.px}
        borderBottom="1px solid rgba(255,255,255, 0.1)"
        display="flex"
        justifyContent="space-between"
        alignItems="center"
      >
        <Link href="/school">
          <Logo color={theme.colors.primary.white.hex} />
        </Link>
        <Box display="flex" alignItems="center" gap={theme.spacing.md.px}>
          <Link href="/school">
            <Text
              color={theme.colors.primary.white.hex}
              variant="sm"
              fontWeight={isTestsActive ? 600 : 400}
              display="flex"
              alignContent="center"
              gap={theme.spacing.xxs.px}
              opacity={isTestsActive ? 1 : 0.7}
            >
              <BeakerIcon width={20} height={20} />
              Test
            </Text>
          </Link>
          <Link href="/school/analysis">
            <Text
              color={theme.colors.primary.white.hex}
              variant="sm"
              fontWeight={isAnalysisActive ? 600 : 400}
              display="flex"
              alignContent="center"
              gap={theme.spacing.xxs.px}
              opacity={isAnalysisActive ? 1 : 0.7}
            >
              <BoltIcon width={20} height={20} />
              Analysis
            </Text>
          </Link>
          <Link href="/school/account">
            <Text
              color={theme.colors.primary.white.hex}
              variant="sm"
              fontWeight={isAccountActive ? 600 : 400}
              display="flex"
              alignContent="center"
              gap={theme.spacing.xxs.px}
              opacity={isAccountActive ? 1 : 0.7}
            >
              <Cog6ToothIcon width={20} height={20} />
              Account
            </Text>
          </Link>
          <Button
            variant="secondary"
            size="xs"
            color="white"
            iconRight={
              <ArrowRightStartOnRectangleIcon width={20} height={20} />
            }
            onClick={handleCloseButtonClick}
          >
            Log-out
          </Button>
        </Box>
      </Box>
    );
  }

  return (
    <Box
      px={theme.spacing.ml.px}
      py={theme.spacing.sm.px}
      borderBottom="1px solid rgba(0, 0, 0, 0.05)"
      display="flex"
      justifyContent="space-between"
    >
      <Box
        px={theme.spacing.sm.px}
        py={theme.spacing.xxs.px}
        bgColor={theme.colors.ui.alert_red_01.hover.hex}
        display="inline-flex"
        borderRadius={theme.border.radius.sm.px}
        color={theme.colors.primary.white.hex}
        fontWeight="700"
        fontSize="12px"
        alignItems="center"
        gap="6px"
      >
        <svg
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M23.3746 12.0005C24.3909 13.6988 24.1675 15.9323 22.7044 17.3954C21.5843 18.5156 20.0127 18.9091 18.5753 18.576C18.9083 20.0133 18.5148 21.5848 17.3947 22.7049C15.9316 24.1681 13.6982 24.3915 11.9999 23.3751C10.3016 24.3913 8.06835 24.1678 6.6053 22.7048C5.48521 21.5846 5.09168 20.0131 5.42473 18.5757C3.98729 18.909 2.41551 18.5155 1.29528 17.3952C-0.167855 15.9321 -0.391227 13.6986 0.625167 12.0002C-0.391476 10.3018 -0.168174 8.06804 1.29507 6.60476C1.99516 5.90465 2.8716 5.48839 3.7814 5.35598C4.03012 5.31978 4.28134 5.3048 4.53212 5.31103C4.83175 5.31847 5.13076 5.35619 5.42417 5.42419C5.09117 3.98688 5.4847 2.41536 6.60477 1.29527C8.06792 -0.167928 10.3014 -0.391298 11.9997 0.62516C13.6981 -0.391482 15.9317 -0.168168 17.3949 1.2951C18.515 2.41523 18.9085 3.98679 18.5755 5.42413C20.0129 5.09085 21.5847 5.48435 22.7049 6.60461C24.1683 8.06802 24.3915 10.302 23.3746 12.0005ZM12 12.944L7.54894 17.3951C6.34336 18.6007 6.34336 20.5554 7.54895 21.7611C8.75453 22.9667 10.7092 22.9667 11.9148 21.7611L16.3658 17.3099L12 12.944ZM17.3949 16.4517C17.6963 16.7531 18.0445 16.9792 18.4162 17.1299C19.531 17.582 20.8566 17.3559 21.7608 16.4517C22.6617 15.5508 22.8894 14.2315 22.4438 13.1191C22.3117 12.7895 22.1205 12.478 21.8702 12.2009C21.8349 12.1618 21.7984 12.1234 21.7608 12.0858L17.3094 7.63431L17.3092 7.63411L12.9434 12.0001L12.9436 12.0003L17.3949 16.4517ZM18.428 6.86556L22.444 10.8817C22.89 9.76913 22.6624 8.44945 21.7613 7.54829C20.8602 6.64713 19.5405 6.41955 18.428 6.86556ZM13.1188 22.4444C14.2311 22.8898 15.5502 22.6621 16.4511 21.7612C17.3519 20.8603 17.5796 19.5412 17.1342 18.4288L13.1188 22.4444ZM2.23893 16.4516C1.338 15.5506 1.11033 14.2313 1.55592 13.1189L5.57145 17.1346C4.4591 17.5802 3.13986 17.3525 2.23893 16.4516ZM11.056 12L6.69017 16.366L2.23874 11.9144C1.03315 10.7088 1.03315 8.75406 2.23874 7.54843C2.682 7.10516 3.22652 6.82486 3.79784 6.70755C4.00346 6.66533 4.21254 6.64422 4.42163 6.64422C5.21169 6.64422 6.00175 6.94562 6.60454 7.54844L11.056 12ZM11.9997 11.0564L7.63391 6.69045L12.0854 2.23878C13.291 1.03316 15.2457 1.03316 16.4513 2.23878C17.6569 3.44441 17.6569 5.39912 16.4513 6.60474L11.9997 11.0564ZM7.54842 2.23894C8.44936 1.33797 9.76862 1.1103 10.881 1.55593L6.86542 5.57161C6.41981 4.45922 6.64747 3.13991 7.54842 2.23894Z"
            fill="white"
          />
        </svg>
        <span>talamo/admin</span>
      </Box>
      <Box display="flex" alignItems="center" gap={theme.spacing.sm.px}>
        <Link href="/admin">
          <Text
            variant="sm"
            fontWeight={isSchoolActive ? 600 : 400}
            color={theme.colors.primary.white.hex}
          >
            Schools
          </Text>
        </Link>
        <Link href="/admin/organisations">
          <Text
            variant="sm"
            fontWeight={isOrgActive ? 600 : 400}
            color={theme.colors.primary.white.hex}
          >
            Organisations
          </Text>
        </Link>
        <Button
          variant="secondary"
          size="xs"
          color="white"
          iconRight={<ArrowRightStartOnRectangleIcon width={20} height={20} />}
          onClick={handleCloseButtonClick}
        >
          Log-out
        </Button>
      </Box>
    </Box>
  );
};
