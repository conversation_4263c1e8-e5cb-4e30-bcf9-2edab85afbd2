/* eslint-disable react/no-unescaped-entities */
"use client";

import {
  IconButton,
  Menu,
  MenuButton,
  MenuItem,
  MenuList,
  useToast,
} from "@chakra-ui/react";
import {
  ArrowDownTrayIcon,
  EllipsisHorizontalIcon,
} from "@heroicons/react/24/solid";

import { Button, Text } from "@/components/v2";
import { useAnalysisByIdQuery } from "@/hooks/queries/useAnalysisByIdQuery";
import { PDFType } from "@/types/analysis";
import { printReport } from "@/utils/admin/print-report";

type PDFDownloadMenuProps = {
  studentCode: string;
  type?: "button" | "icon";
  isAdminView?: boolean;
};

const iconSize = 17;

export const PDFDownloadMenu = ({
  studentCode,
  type = "button",
  isAdminView = false,
}: PDFDownloadMenuProps) => {
  const toast = useToast();
  const { data: reportData } = useAnalysisByIdQuery({ studentCode });

  if (reportData?.hide_report === true && type == "icon") {
    return <div style={{ width: "40px", height: "40px" }} />;
  }
  if (!reportData || (reportData?.hide_report === true && !isAdminView)) {
    return null;
  }

  const printPdfClickHandler = (pdfType: PDFType) => {
    printReport({ studentCode, type: pdfType });
    toast({
      title: "Generating report!",
      description: "The report will download shortly",
      status: "success",
      duration: 5000,
      isClosable: true,
    });
  };

  return (
    <>
      <Menu>
        <MenuButton>
          {type === "button" ? (
            <Button
              size="sm"
              iconRight={<ArrowDownTrayIcon width={20} height={20} />}
            >
              Download report
            </Button>
          ) : (
            <MenuButton
              as={IconButton}
              aria-label="Options"
              icon={<EllipsisHorizontalIcon width={iconSize} />}
              variant="outline"
            />
          )}
        </MenuButton>
        <MenuList zIndex={10}>
          <MenuItem
            icon={<ArrowDownTrayIcon width={iconSize} />}
            onClick={() => printPdfClickHandler(PDFType.Full)}
          >
            <Text element="h6" variant="xs">
              Download Full Report
            </Text>
          </MenuItem>
          <MenuItem
            icon={<ArrowDownTrayIcon width={iconSize} />}
            onClick={() => printPdfClickHandler(PDFType.Teacher)}
          >
            <Text element="h6" variant="xs">
              Download Teacher's Report
            </Text>
          </MenuItem>

          <MenuItem
            icon={<ArrowDownTrayIcon width={iconSize} />}
            onClick={() => printPdfClickHandler(PDFType.Parent)}
          >
            <Text element="h6" variant="xs">
              Download Parent's Report
            </Text>
          </MenuItem>
        </MenuList>
      </Menu>
    </>
  );
};
