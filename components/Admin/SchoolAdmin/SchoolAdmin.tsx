"use client";

import "@/styles/globals.css";

import { Box, Spinner } from "@chakra-ui/react";
import { createClientComponentClient } from "@supabase/auth-helpers-nextjs";
import Link from "next/link";
import { useEffect, useState } from "react";

import { FormLabel } from "@/components/FormLabel";
import { InputField } from "@/components/InputField/InputField";
import { Button } from "@/components/v2/Button/Button";
import { Panel } from "@/components/v2/Panel/Panel";
import { Text } from "@/components/v2/Text/Text";
import { useSchoolsByIdQuery } from "@/hooks/queries/useSchoolsByIdQuery";
import { theme } from "@/styles/theme";

export const SchoolAdmin = ({ user }: { user: any }) => {
  const supabase = createClientComponentClient();
  const [passwordResetSent, setPasswordResetSent] = useState(false);
  const [domain, setDomain] = useState("");
  const redirectUrl = `${domain}/login/reset-password`;

  const schoolId = user.data.user.user_metadata.orgId;
  const { data: schoolData, isLoading: schoolsLoading } = useSchoolsByIdQuery({
    schoolId: schoolId,
  });

  useEffect(() => {
    setDomain(window.location.origin);
  }, []);

  if (schoolsLoading) {
    return <Spinner />;
  }

  if (!schoolData) {
    return null;
  }

  const sendPasswordResetEmail = async () => {
    const { error } = await supabase.auth.resetPasswordForEmail(
      schoolData.admin_email,
      {
        redirectTo: redirectUrl,
      }
    );
    if (error) {
      setPasswordResetSent(false);
    } else {
      setPasswordResetSent(true);
    }
  };

  return (
    <>
      <Box display="flex" flexDirection="column" gap={theme.spacing.md.px}>
        <Panel preset="outline">
          <Text element="h4" variant="md">
            Credits
          </Text>
          <Box
            w="525px"
            h="73px"
            display="block"
            pos="relative"
            borderRadius="10px"
            bgColor="rgba(0,0,0,0.02)"
            boxShadow="inset 0 0 0 1px rgba(0, 0, 0, 0.1)"
            overflow="hidden"
            my={theme.spacing.sm.px}
          >
            <Box
              w={`100%`}
              h="73px"
              pos="absolute"
              top={0}
              left={0}
              borderRadius="10px"
              bgColor={
                schoolData.unassigned_credits > 1
                  ? "#42B22D"
                  : theme.colors.ui.alert_red_01.hex
              }
              transition="all 0.3s ease-in"
            ></Box>
            <Box
              pos="absolute"
              top="50%"
              left="50%"
              transform="auto"
              translateX="-50%"
              translateY="-50%"
            >
              <Text
                element="h4"
                variant="md"
                color={theme.colors.primary.white.hex}
                whiteSpace="nowrap"
              >
                {schoolData.unassigned_credits} credits remaining
              </Text>
            </Box>
            <Box
              width="100%"
              height="100%"
              pos="absolute"
              top={0}
              left={0}
              zIndex={10}
              backgroundImage="url('/images/assessment-progress/progress-stripes.svg')"
              backgroundSize="auto 100%"
            />
          </Box>
          <Text element="p" variant="md">
            If you would like more credits, send us a message at{" "}
            <a
              href="mailto:<EMAIL>"
              color={theme.colors.primary.black.hex}
              style={{ fontWeight: "500" }}
            >
              <EMAIL>
            </a>
          </Text>
        </Panel>

        <Panel preset="outline">
          <Text element="h4" variant="md">
            School details
          </Text>
          <Box mb={theme.spacing.md.px}>
            <FormLabel>School name</FormLabel>
            <InputField
              value={schoolData.school_name}
              name="name"
              type="text"
              variant="outline"
              disabled
            />
          </Box>
          <Box mb={theme.spacing.md.px}>
            <FormLabel>Primary contact</FormLabel>
            <InputField
              value={schoolData.admin_name}
              name="primary-contact"
              type="text"
              variant="outline"
              disabled
            />
          </Box>
          <Box mb={theme.spacing.md.px}>
            <FormLabel>Primary email</FormLabel>
            <InputField
              value={schoolData.admin_email}
              name="primary-email"
              type="email"
              variant="outline"
              disabled
            />
            <Text element="p" variant="md" mt={theme.spacing.lg.px}>
              If you want to change your school name, your name, or email,
              contact us on{" "}
              <a
                href="mailto:<EMAIL>"
                color={theme.colors.primary.black.hex}
                style={{ fontWeight: "500" }}
              >
                <EMAIL>
              </a>
            </Text>
          </Box>
          <Box mb={theme.spacing.md.px}>
            <FormLabel>Password</FormLabel>
            <InputField
              value="xxxxxxxxxxxxxxx"
              name="primary-contact"
              type="password"
              variant="outline"
              disabled
            />
          </Box>
          <Box mb={theme.spacing.md.px}>
            <Button
              size="md"
              mt={theme.spacing.md.px}
              onClick={sendPasswordResetEmail}
              disabled={passwordResetSent}
            >
              {passwordResetSent
                ? "Password reset sent"
                : "Send reset password link"}
            </Button>
            {passwordResetSent ? (
              <Text element="p" variant="md" mt={theme.spacing.md.px}>
                We&apos;ve sent an email to {schoolData.admin_email} for you to
                reset your password
              </Text>
            ) : (
              <Text element="p" variant="md" mt={theme.spacing.md.px}>
                Click this link and we’ll send an email for you to reset your
                password
              </Text>
            )}
          </Box>
        </Panel>

        <Panel preset="outline">
          <Text element="h4" variant="md">
            Data
          </Text>
          <Text element="p" variant="md" mt={theme.spacing.md.px}>
            You can request either a copy of your data, or for us to delete your
            data at any point. To do either of these, get in touch with us at{" "}
            <a
              href="mailto:<EMAIL>"
              color={theme.colors.primary.black.hex}
              style={{ fontWeight: "500" }}
            >
              <EMAIL>
            </a>
            . You can also learn more about our Data Protection policies{" "}
            <Link href="https://www.talamo.co.uk/data-policy" target="_blank">
              here
            </Link>
            .
          </Text>
          <Text
            element="p"
            variant="md"
            color={theme.colors.ui.alert_red_01.hex}
            mt={theme.spacing.md.px}
            fontWeight="600"
          >
            Please note, if we delete your data, we cannot undo this action!
          </Text>
        </Panel>
      </Box>
    </>
  );
};
