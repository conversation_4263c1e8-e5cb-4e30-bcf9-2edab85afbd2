"use client";

import { Box, Card, Container, VStack } from "@chakra-ui/react";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import React, { useEffect, useState } from "react";

import { InputField } from "@/components/InputField/InputField";
import { StarsBg } from "@/components/StarsBg/StarsBg";
import { Button } from "@/components/v2/Button/Button";
import { Logo } from "@/components/v2/Logo/Logo";
import { Text } from "@/components/v2/Text/Text";
import { theme } from "@/styles/theme";
import { InviteStatus } from "@/types/admin";
import { createClient } from "@/utils/supabase/client";

export const VerifyForm = ({
  searchParams,
}: {
  searchParams?: { message?: string; email?: string };
}) => {
  const router = useRouter();
  const [step, setStep] = useState(1);
  const [email, setEmail] = useState(searchParams?.email || "");
  const [loginCode, setLoginCode] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [verificationError, setVerificationError] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [inviteStatus, setInviteStatus] = useState<string | null>(null);
  const [isCheckingInviteStatus, setIsCheckingInviteStatus] = useState(
    !!searchParams?.email
  );
  const [isSubmitting, setIsSubmitting] = useState(false);

  const supabase = createClient();

  useEffect(() => {
    if (searchParams?.email) {
      checkInviteStatus(searchParams.email);
    }
  }, [searchParams?.email]);

  const checkInviteStatus = async (emailToCheck: string) => {
    setIsCheckingInviteStatus(true);
    try {
      const { data, error } = await supabase
        .from("admins")
        .select("invite_status")
        .eq("email", emailToCheck)
        .single();

      if (error) {
        console.error("Error fetching invite status:", error);
        setVerificationError("Error verifying your email");
        return;
      }

      setInviteStatus(data.invite_status);

      if (data.invite_status === InviteStatus.Accepted) {
        return;
      }
    } catch (error) {
      console.error("Unexpected error:", error);
      setVerificationError("Error verifying your email.");
    } finally {
      setIsCheckingInviteStatus(false);
    }
  };

  const handleVerifyCode = async (e: React.FormEvent) => {
    e.preventDefault();

    setIsSubmitting(true);

    if (!inviteStatus) {
      await checkInviteStatus(email);
    }

    if (inviteStatus === InviteStatus.Accepted) {
      setIsSubmitting(false);
      return;
    }

    try {
      const response = await fetch("/api/verify/verify-user", {
        method: "POST",
        body: JSON.stringify({ email, loginCode }),
        headers: { "Content-Type": "application/json" },
      });

      const result = await response.json();

      if (response.ok) {
        setStep(2);
      } else {
        setVerificationError(result.message);
        setInviteStatus(null);
      }
    } catch (error) {
      setVerificationError("Please enter a valid code");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSetPassword = async (e: React.FormEvent) => {
    e.preventDefault();

    setIsLoading(true);
    setVerificationError("");

    if (password !== confirmPassword) {
      setVerificationError("The passwords do not match");
      setIsLoading(false);
      return;
    }

    try {
      const response = await fetch("/api/verify/set-password", {
        method: "POST",
        body: JSON.stringify({ email, password }),
        headers: { "Content-Type": "application/json" },
      });

      const result = await response.json();

      if (!response.ok) {
        setVerificationError(result.message);
        setIsLoading(false);
        return;
      }

      const { error: loginError, data } =
        await supabase.auth.signInWithPassword({
          email,
          password,
        });

      if (loginError) {
        setVerificationError(loginError.message);
        setIsLoading(false);
        return;
      }

      await supabase
        .from("admins")
        .update({
          invite_status: InviteStatus.Accepted,
        })
        .eq("user_id", data.user.id);

      await fetch("/api/brevo/send-email", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "x-internal": "true",
        },
        body: JSON.stringify({
          type: "schoolWelcome",
          email: email,
        }),
      });

      router.push("/school");
    } catch (error) {
      setVerificationError("An error occurred while setting the password.");
      console.error("Unexpected error:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const renderCardContent = () => {
    if (inviteStatus === InviteStatus.Accepted && email) {
      return (
        <>
          <Text
            variant="xl"
            element="h2"
            mb={theme.spacing.sm.px}
            textAlign="center"
          >
            Account already set up!
          </Text>
          <Text
            element="p"
            size="lg"
            mb={theme.spacing.sm.px}
            textAlign="center"
          >
            It looks like an account has already been setup with this email
            address and login code. You can try logging in below or if you’re
            still having issues, contact us at{" "}
            <strong><EMAIL></strong> and we’ll help get you in.
          </Text>
          <Button
            size="md"
            my={theme.spacing.md.px}
            onClick={() => router.push("/login")}
          >
            Go to login
          </Button>
        </>
      );
    }

    return (
      <>
        <Text
          variant="2xl"
          element="h2"
          mb={theme.spacing.sm.px}
          textAlign="center"
        >
          {step === 1 ? "Account setup" : "Set password"}
        </Text>

        <form onSubmit={step === 1 ? handleVerifyCode : handleSetPassword}>
          {step === 1 && (
            <>
              <Text
                element="h3"
                variant="xs"
                textAlign="left"
                alignSelf="flex-start"
                m={0}
                mt={theme.spacing.sm.px}
              >
                Email
              </Text>
              <InputField
                name="email"
                type="email"
                variant="outline"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                disabled={!!searchParams?.email}
              />
              <Text
                element="h3"
                variant="xs"
                textAlign="left"
                alignSelf="flex-start"
                m={0}
                mt={theme.spacing.sm.px}
              >
                Login code
              </Text>
              <InputField
                name="loginCode"
                type="text"
                variant="outline"
                value={loginCode}
                onChange={(e) => setLoginCode(e.target.value)}
              />
            </>
          )}

          {step === 2 && (
            <>
              <Text
                element="h3"
                variant="xs"
                textAlign="left"
                alignSelf="flex-start"
                m={0}
                mt={theme.spacing.sm.px}
              >
                Password
              </Text>
              <InputField
                name="password"
                type="password"
                variant="outline"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
              />
              <Text
                element="h3"
                variant="xs"
                textAlign="left"
                alignSelf="flex-start"
                m={0}
                mt={theme.spacing.sm.px}
              >
                Re-type password
              </Text>
              <InputField
                name="confirmPassword"
                type="password"
                variant="outline"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
              />
            </>
          )}

          {verificationError && (
            <Text color="red.500">{verificationError}</Text>
          )}

          <Box display="flex" justifyContent="center">
            <Button
              type="submit"
              size="md"
              my={theme.spacing.md.px}
              disabled={isSubmitting || isLoading || isCheckingInviteStatus}
            >
              {isSubmitting || isCheckingInviteStatus
                ? "Checking..."
                : step === 1
                  ? "Continue"
                  : isLoading
                    ? "Loading..."
                    : "Complete setup"}
            </Button>
          </Box>
        </form>
      </>
    );
  };

  return (
    <>
      <Box top="20px" position="absolute" w="100%" left={0}>
        <Box
          data-testid="primary-navigation"
          py={theme.spacing.sm.px}
          zIndex={500}
          width="100%"
          pos="relative"
        >
          <Container width={"100%"} px={30} maxW={"none"}>
            <Link href="/">
              <Logo color={theme.colors.primary.white.hex} />
            </Link>
          </Container>
        </Box>
      </Box>
      <VStack
        justifyContent={"center"}
        alignContent={"center"}
        w="100vw"
        h="100vh"
        gap={6}
      >
        <Box>
          <Card
            w={{
              sm: "100%",
              md: inviteStatus === InviteStatus.Accepted ? "596px" : "460px",
            }}
            border="none"
            borderRadius={theme.border.radius.md.px}
            bg={theme.colors.primary.white.hex}
            flex={1}
            alignItems={inviteStatus === InviteStatus.Accepted ? "center" : ""}
            overflow="hidden"
            variant="outline"
            p={"32px"}
            css={{
              boxShadow: theme.shadow.box,
            }}
          >
            {renderCardContent()}
          </Card>
        </Box>
      </VStack>
      <StarsBg zIndex={-1} />
      <Box
        position="absolute"
        bottom={0}
        right={0}
        display={{ base: "none", md: "block" }}
      >
        <Image
          src="/images/auth/sign-up.svg"
          width={400}
          height={400}
          priority
          alt=""
        />
      </Box>
    </>
  );
};
