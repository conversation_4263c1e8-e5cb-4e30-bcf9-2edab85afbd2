"use client";

import { Box } from "@chakra-ui/react";
import { useState } from "react";

import { Button } from "@/components/v2";
import { Loader } from "@/components/v2/Loader/Loader";
import { Text } from "@/components/v2/Text/Text";
import { useStudentByCodeQuery } from "@/hooks/queries/useStudentByCodeQuery";
import { theme } from "@/styles/theme";

import { AdminPanel } from "../AdminPanel/AdminPanel";
import { DeleteStudents } from "../DeleteStudents/DeleteStudents";

export const StudentDeletion: React.FC<{ studentCode: string }> = ({
  studentCode,
}) => {
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const { data: studentData } = useStudentByCodeQuery({ studentCode });

  if (!studentData) return <Loader />;

  return (
    <AdminPanel>
      <Box
        display="flex"
        flexDir="column"
        alignItems="flex-start"
        gap={theme.spacing.xs.px}
      >
        <Text element="h4" variant="xl">
          Delete student
        </Text>
        <Text element="p" variant="xl">
          This will remove the student from Talamo entirely. You will not be
          able to undo this action so be careful when clicking the button below!
        </Text>
        <Button
          size="sm"
          color="red"
          onClick={() => {
            setIsDeleteModalOpen(true);
          }}
        >
          Delete student
        </Button>
      </Box>
      <DeleteStudents
        isModalOpen={isDeleteModalOpen}
        setIsModalOpen={setIsDeleteModalOpen}
        studentIds={[studentData.student_id]}
      />
    </AdminPanel>
  );
};
