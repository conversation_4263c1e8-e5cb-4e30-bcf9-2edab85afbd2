"use client";

import "@/styles/globals.css";

import { Box, Spinner } from "@chakra-ui/react";
import { CheckCircleIcon, ClockIcon } from "@heroicons/react/24/solid";
import {
  createColumnHelper,
  RowSelectionState,
  SortingState,
} from "@tanstack/react-table";
import Link from "next/link";
import { useState } from "react";

import { Text } from "@/components/v2/Text/Text";
import {
  SchoolDetailView,
  useSchoolsQuery,
} from "@/hooks/queries/useSchoolsQuery";
import { theme } from "@/styles/theme";
import { InviteStatus } from "@/types/admin";

import { DataTable } from "../DataTable/DataTable";
import { SchoolsActions } from "./SchoolsActions";

export const SchoolsTable = () => {
  const { data: schools, isLoading: schoolLoading } = useSchoolsQuery();
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});
  const [sorting, setSorting] = useState<SortingState>([]);

  if (schoolLoading || !schools) {
    return <Spinner />;
  }

  const columnHelper = createColumnHelper<SchoolDetailView>();

  const columns = [
    columnHelper.accessor("school_name", {
      cell: ({ row }) => (
        <Link href={`/admin/school/${row.original.school_id}`}>
          <Text textDecoration="underline" variant="sm" fontWeight={600}>
            {row.original.school_name}
          </Text>
        </Link>
      ),
      header: "School name",
      meta: {
        class: "sticky-left",
      },
    }),
    columnHelper.accessor("location", {
      cell: (info) => info.getValue(),
      header: "Location",
    }),
    columnHelper.accessor("school_type", {
      cell: (info) => info.getValue(),
      header: "Type",
    }),
    columnHelper.accessor("org_name", {
      cell: (info) => info.getValue(),
      header: "Org",
    }),
    columnHelper.accessor("student_count", {
      cell: (info) => info.getValue(),
      header: "Students",
    }),
    columnHelper.accessor("redeemed_credits", {
      cell: (info) => info.getValue(),
      header: "Used credits",
    }),
    columnHelper.accessor("total_credits", {
      cell: ({ row }) =>
        row.original.total_credits - row.original.redeemed_credits,
      header: "Avail. credits",
      meta: {
        width: "10px",
      },
    }),
    columnHelper.accessor("invite_status", {
      cell: ({ row }) =>
        row.original.invite_status === InviteStatus.Pending ? (
          <ClockIcon
            width={16}
            height={16}
            color={theme.colors.ui.alert_orange_01.hex}
          />
        ) : (
          <CheckCircleIcon
            width={16}
            height={16}
            color={theme.colors.ui.alert_green_01.hex}
          />
        ),
      header: "Status",
    }),
    columnHelper.accessor("school_id", {
      cell: ({ row }) => (
        <SchoolsActions
          schoolId={row.original.school_id}
          orgId={row.original.org_id}
          email={row.original.admin_email}
          inviteStatus={row.original.invite_status}
        />
      ),
      header: "",
      meta: {
        class: "sticky-right",
      },
    }),
  ];

  return (
    <Box overflow="scroll">
      <Box>
        <DataTable
          columns={columns}
          data={schools}
          rowSelection={rowSelection}
          setRowSelection={setRowSelection}
          sorting={sorting}
          setSorting={setSorting}
        />
      </Box>
    </Box>
  );
};
