"use client";

import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>u<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
} from "@chakra-ui/react";
import {
  CheckCircleIcon,
  Cog6ToothIcon,
  CurrencyPoundIcon,
  EnvelopeIcon,
  TrashIcon,
} from "@heroicons/react/24/solid";
import { startTransition, useState } from "react";

import { Text } from "@/components/v2";
import { resendInvite } from "@/server/admin/resendInvite";
import { theme } from "@/styles/theme";
import { InviteStatus } from "@/types/admin";

import AddCredits from "../AddCredits/AddCredits";
import RemoveCredits from "../AddCredits/RemoveCredits";

type SchoolsActionsProps = {
  schoolId: number;
  orgId?: number;
  email: string;
  inviteStatus: InviteStatus;
};

const iconSize = 17;

export const SchoolsActions = ({
  email,
  schoolId,
  orgId,
  inviteStatus,
}: SchoolsActionsProps) => {
  const [isCreditsModalOpen, setIsCreditModalOpen] = useState(false);
  const [isCreditsRemovalModalOpen, setIsCreditRemovalModalOpen] =
    useState(false);
  const [inviteResendStatus, setInviteResendStatus] = useState("Resend invite");

  const handleResendInvite = () => {
    setInviteResendStatus("Sending");
    startTransition(() => {
      resendInvite({ email }).then(() => {
        setInviteResendStatus("Sent");
      });
    });
  };

  return (
    <>
      <Menu>
        <MenuButton
          as={IconButton}
          aria-label="Options"
          icon={<Cog6ToothIcon width={iconSize} />}
          variant="outline"
        />
        <MenuList>
          {inviteStatus === InviteStatus.Pending && (
            <MenuItem
              icon={
                inviteResendStatus === "Sending" ? (
                  <Spinner size="xs" />
                ) : inviteResendStatus === "Sent" ? (
                  <CheckCircleIcon width={iconSize} />
                ) : (
                  <EnvelopeIcon width={iconSize} />
                )
              }
              closeOnSelect={false}
              onClick={handleResendInvite}
            >
              <Text element="h6" variant="xs">
                {" "}
                {inviteResendStatus}
              </Text>
            </MenuItem>
          )}
          <MenuItem
            icon={<CurrencyPoundIcon width={iconSize} />}
            onClick={() => setIsCreditModalOpen(true)}
          >
            <Text element="h6" variant="xs">
              Add credits
            </Text>
          </MenuItem>
          <MenuItem
            icon={
              <TrashIcon
                width={iconSize}
                color={theme.colors.ui.alert_red_01.hex}
              />
            }
            onClick={() => setIsCreditRemovalModalOpen(true)}
          >
            <Text
              element="h6"
              variant="xs"
              color={theme.colors.ui.alert_red_01.hex}
            >
              Remove credits
            </Text>
          </MenuItem>
        </MenuList>
      </Menu>

      <AddCredits
        schoolId={schoolId}
        orgId={orgId}
        isModalOpen={isCreditsModalOpen}
        setIsModalOpen={setIsCreditModalOpen}
      />

      <RemoveCredits
        schoolId={schoolId}
        orgId={orgId}
        isModalOpen={isCreditsRemovalModalOpen}
        setIsModalOpen={setIsCreditRemovalModalOpen}
      />
    </>
  );
};
