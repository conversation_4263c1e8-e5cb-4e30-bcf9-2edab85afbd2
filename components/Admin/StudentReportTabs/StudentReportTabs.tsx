"use client";

import { Box } from "@chakra-ui/react";
import Image from "next/image";
import Link from "next/link";
import React from "react";

import { Text } from "@/components/v2/Text/Text";
import { theme } from "@/styles/theme";

type StudentReportTabsProps = {
  currentTab: string;
  studentCode: string;
  isAdminView?: boolean;
  schoolId?: string;
};

export const StudentReportTabs = ({
  currentTab,
  studentCode,
  isAdminView = false,
  schoolId,
}: StudentReportTabsProps) => {
  const tabs = [
    {
      key: "results",
      title: "Results",
      link: isAdminView
        ? `/admin/school/${schoolId}/report/${studentCode}`
        : `/school/analysis/report/${studentCode}`,
    },
    {
      key: "support",
      title: "How to support",
      link: isAdminView
        ? `/admin/school/${schoolId}/support/${studentCode}`
        : `/school/analysis/support/${studentCode}`,
    },
    {
      key: "about",
      title: "About",
      link: isAdminView
        ? `/admin/school/${schoolId}/student/${studentCode}`
        : `/school/student/${studentCode}`,
    },
  ];
  return (
    <Box display="flex" alignItems="flex-end" ml={theme.spacing.ml.px}>
      {tabs.map((tab) => {
        const bg =
          tab.key === currentTab
            ? theme.colors.primary.white.hex
            : theme.colors.secondary.purple_05.hex;
        return (
          <Link key={tab.key} href={tab.link} scroll={false}>
            <Box
              display="flex"
              gap={theme.spacing.xs.px}
              bgColor={bg}
              p="8px 12px"
              borderRadius="8px 8px 0 0"
              opacity={tab.key === currentTab ? 1 : 0.95}
            >
              <Image
                src={`/images/icons/${tab.key}.svg`}
                width={24}
                height={24}
                alt=""
              />
              <Text element="h6" variant="sm">
                {tab.title}
              </Text>
            </Box>
          </Link>
        );
      })}
    </Box>
  );
};
