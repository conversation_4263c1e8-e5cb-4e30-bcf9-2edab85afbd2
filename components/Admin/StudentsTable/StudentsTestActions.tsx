"use client";

import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>u<PERSON><PERSON>on,
  <PERSON>u<PERSON><PERSON>,
  <PERSON>u<PERSON><PERSON>,
  Spinner,
  useToast,
} from "@chakra-ui/react";
import {
  BeakerIcon,
  EllipsisHorizontalIcon,
  PencilSquareIcon,
  PrinterIcon,
  XMarkIcon,
} from "@heroicons/react/24/solid";
import { useState } from "react";

import { Text } from "@/components/v2";
import { useCreditsBySchoolQuery } from "@/hooks/queries/useCreditsBySchoolId";
import { theme } from "@/styles/theme";
import { printLoginCodes } from "@/utils/admin/print-login-codes";

import { AssignTest } from "../AssignTest/AssignTest";
import { DeleteStudents } from "../DeleteStudents/DeleteStudents";
import { RemoveTest } from "../RemoveTest/RemoveTest";

type StudentsActionsProps = {
  studentId: number;
  schoolId: number;
};

const iconSize = 17;

export const StudentsTestActions = ({
  studentId,
  schoolId,
}: StudentsActionsProps) => {
  const toast = useToast();
  const [isAssignModalOpen, setIsAssignModalOpen] = useState(false);
  const [isRemoveModalOpen, setIsRemoveModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);

  const { data: creditData, isLoading: creditDataLoading } =
    useCreditsBySchoolQuery({ schoolId });

  if (creditDataLoading) {
    return <Spinner />;
  }

  if (!creditData) {
    return null;
  }

  const studentCredit = creditData.filter(
    (credit) => credit.student_id === studentId
  );

  const handlePrintStudentIds = () => {
    printLoginCodes([studentId]);
    toast({
      title: "Login IDs downloaded!",
      description: "Please open the pdf you've downloaded to print",
      status: "success",
      duration: 5000,
      isClosable: true,
    });
  };

  return (
    <>
      <Menu>
        <MenuButton
          as={IconButton}
          aria-label="Options"
          icon={<EllipsisHorizontalIcon width={iconSize} />}
          variant="outline"
        />
        <MenuList>
          {studentCredit.length === 0 && (
            <MenuItem
              icon={<BeakerIcon width={iconSize} />}
              onClick={() => setIsAssignModalOpen(true)}
            >
              <Text element="h6" variant="xs">
                Assign test
              </Text>
            </MenuItem>
          )}
          {studentCredit.length > 0 &&
            studentCredit[0].status === "Assigned" && (
              <MenuItem
                icon={<BeakerIcon width={iconSize} />}
                onClick={() => setIsRemoveModalOpen(true)}
              >
                <Text element="h6" variant="xs">
                  Remove test
                </Text>
              </MenuItem>
            )}

          <MenuItem
            icon={<PrinterIcon width={iconSize} />}
            onClick={handlePrintStudentIds}
          >
            <Text element="h6" variant="xs">
              Print login ID
            </Text>
          </MenuItem>
          {/* 
          <MenuItem
            icon={<PencilSquareIcon width={iconSize} />}
            onClick={() => alert("Coming soon")}
          >
            <Text element="h6" variant="xs">
              Edit student
            </Text>
          </MenuItem> */}
          <MenuItem
            icon={
              <XMarkIcon
                width={iconSize}
                color={theme.colors.ui.alert_red_01.hex}
              />
            }
            onClick={() => setIsDeleteModalOpen(true)}
          >
            <Text
              element="h6"
              variant="xs"
              color={theme.colors.ui.alert_red_01.hex}
            >
              Delete student
            </Text>
            <DeleteStudents
              isModalOpen={isDeleteModalOpen}
              setIsModalOpen={setIsDeleteModalOpen}
              studentIds={[studentId]}
            />
          </MenuItem>
        </MenuList>
      </Menu>
      <AssignTest
        isModalOpen={isAssignModalOpen}
        setIsModalOpen={setIsAssignModalOpen}
        studentIds={[studentId]}
        schoolId={schoolId}
      />
      <RemoveTest
        isModalOpen={isRemoveModalOpen}
        setIsModalOpen={setIsRemoveModalOpen}
        studentIds={[studentId]}
        schoolId={schoolId}
      />
    </>
  );
};
