"use client";

import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>u<PERSON>utton,
  <PERSON>u<PERSON><PERSON>,
  Menu<PERSON>ist,
  useToast,
} from "@chakra-ui/react";
import {
  EllipsisHorizontalIcon,
  PencilSquareIcon,
  PrinterIcon,
  WrenchIcon,
  XMarkIcon,
} from "@heroicons/react/24/solid";
import { useState } from "react";

import { Text } from "@/components/v2";
import { theme } from "@/styles/theme";
import { printLoginCodes } from "@/utils/admin/print-login-codes";

import { DeleteStudents } from "../DeleteStudents/DeleteStudents";

type StudentsActionsProps = {
  studentId: number;
};

const iconSize = 17;

export const StudentsActions = ({ studentId }: StudentsActionsProps) => {
  const toast = useToast();

  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);

  const handlePrintStudentIds = () => {
    printLoginCodes([studentId]);
    toast({
      title: "Login IDs downloaded!",
      description: "Please open the pdf you've downloaded to print",
      status: "success",
      duration: 5000,
      isClosable: true,
    });
  };
  return (
    <>
      <Menu>
        <MenuButton
          as={IconButton}
          aria-label="Options"
          icon={<EllipsisHorizontalIcon width={iconSize} />}
          variant="outline"
        />
        <MenuList>
          <MenuItem
            icon={<PrinterIcon width={iconSize} />}
            onClick={() => handlePrintStudentIds()}
          >
            <Text element="h6" variant="xs">
              Print login ID
            </Text>
          </MenuItem>
          {/* <MenuItem
            icon={<PencilSquareIcon width={iconSize} />}
            onClick={() => alert("Coming soon")}
          >
            <Text element="h6" variant="xs">
              Edit student
            </Text>
          </MenuItem> */}
          <MenuItem
            icon={
              <XMarkIcon
                width={iconSize}
                color={theme.colors.ui.alert_red_01.hex}
              />
            }
            onClick={() => setIsDeleteModalOpen(true)}
          >
            <Text
              element="h6"
              variant="xs"
              color={theme.colors.ui.alert_red_01.hex}
            >
              Delete student
            </Text>
            <DeleteStudents
              isModalOpen={isDeleteModalOpen}
              setIsModalOpen={setIsDeleteModalOpen}
              studentIds={[studentId]}
            />
          </MenuItem>
        </MenuList>
      </Menu>
    </>
  );
};
