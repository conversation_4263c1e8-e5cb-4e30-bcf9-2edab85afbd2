"use client";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>u<PERSON><PERSON>,
  useToast,
} from "@chakra-ui/react";
import { BeakerIcon, PrinterIcon, XMarkIcon } from "@heroicons/react/24/solid";
import { useState } from "react";

import { <PERSON><PERSON>, Text } from "@/components/v2";
import { theme } from "@/styles/theme";
import { printLoginCodes } from "@/utils/admin/print-login-codes";

import { AssignTest } from "../AssignTest/AssignTest";
import { DeleteStudents } from "../DeleteStudents/DeleteStudents";
import { RemoveTest } from "../RemoveTest/RemoveTest";

type StudentsActionsProps = {
  studentIds: number[];
  schoolId: number;
  setRowSelection: React.Dispatch<React.SetStateAction<object>>;
};

const iconSize = 17;

export const StudentsBulkActions = ({
  studentIds,
  schoolId,
  setRowSelection,
}: StudentsActionsProps) => {
  const toast = useToast();
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isAssignModalOpen, setIsAssignModalOpen] = useState(false);
  const [isRemoveModalOpen, setIsRemoveModalOpen] = useState(false);

  const singlePlural = studentIds.length > 1 ? "s" : "";

  const handlePrintStudentIds = () => {
    printLoginCodes(studentIds);
    toast({
      title: "Login IDs downloaded!",
      description: "Please open the pdf you've downloaded to print",
      status: "success",
      duration: 5000,
      isClosable: true,
    });
  };

  return (
    <>
      <Menu>
        <MenuButton>
          <Button size="sm">Actions</Button>
        </MenuButton>
        <MenuList zIndex={10}>
          <MenuItem
            icon={<BeakerIcon width={iconSize} />}
            onClick={() => setIsAssignModalOpen(true)}
          >
            <Text element="h6" variant="xs">
              Assign test
            </Text>
          </MenuItem>
          <MenuItem
            icon={<BeakerIcon width={iconSize} />}
            onClick={() => setIsRemoveModalOpen(true)}
          >
            <Text element="h6" variant="xs">
              Remove test
            </Text>
          </MenuItem>

          <MenuItem
            icon={<PrinterIcon width={iconSize} />}
            onClick={handlePrintStudentIds}
          >
            <Text element="h6" variant="xs">
              Print selected login id{singlePlural}
            </Text>
          </MenuItem>
          <MenuItem
            icon={
              <XMarkIcon
                width={iconSize}
                color={theme.colors.ui.alert_red_01.hex}
              />
            }
            onClick={() => setIsDeleteModalOpen(true)}
          >
            <Text
              element="h6"
              variant="xs"
              color={theme.colors.ui.alert_red_01.hex}
            >
              Delete selected student{singlePlural}
            </Text>
            <DeleteStudents
              isModalOpen={isDeleteModalOpen}
              setIsModalOpen={setIsDeleteModalOpen}
              setRowSelection={setRowSelection}
              studentIds={studentIds}
            />
          </MenuItem>
        </MenuList>
      </Menu>
      <AssignTest
        isModalOpen={isAssignModalOpen}
        setIsModalOpen={setIsAssignModalOpen}
        studentIds={studentIds}
        schoolId={schoolId}
      />
      <RemoveTest
        isModalOpen={isRemoveModalOpen}
        setIsModalOpen={setIsRemoveModalOpen}
        studentIds={studentIds}
        schoolId={schoolId}
      />
    </>
  );
};
