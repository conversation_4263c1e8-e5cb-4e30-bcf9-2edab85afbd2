"use client";

import "@/styles/globals.css";

import { Box, Spinner } from "@chakra-ui/react";
import { createColumnHelper, SortingState } from "@tanstack/react-table";
import { format, parseISO } from "date-fns";
import Link from "next/link";
import { useRouter } from "next/navigation";
import React, { useEffect, useMemo, useState } from "react";

import { Panel } from "@/components/v2";
import { Button } from "@/components/v2/Button/Button";
import { Text } from "@/components/v2/Text/Text";
import { SCHOOL_TEST_NAME } from "@/constants/constants";
import { useSchoolUser } from "@/context/school-context";
import { useCreditsBySchoolQuery } from "@/hooks/queries/useCreditsBySchoolId";
import {
  StudentView,
  useInfiniteStudentsBySchoolQuery,
} from "@/hooks/queries/useStudentsBySchoolQuery";
import { theme } from "@/styles/theme";
import { CreditStatus } from "@/types/admin";

import { DataTable } from "../DataTable/DataTable";
import { PDFDownloadMenu } from "../PDFMenu/PDFMenu";
import { StudentsBulkActions } from "./StudentBulkActions";
import { StudentsTestActions } from "./StudentsTestActions";

interface CreditInfo {
  creditStatus: string | null;
  creditAssessmentId: string | null;
  redeemed?: string | null;
}

type CombinedStudentData = StudentView & CreditInfo;

const PAGE_SIZE = 10;

export const StudentsTestTable = () => {
  const router = useRouter();
  const { user } = useSchoolUser();

  const [sorting, setSorting] = useState<SortingState>([
    { id: "surname", desc: false },
  ]);

  const schoolId = user?.orgId || 0;
  const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading: studentsLoading,
  } = useInfiniteStudentsBySchoolQuery({
    schoolId,
    pageSize: PAGE_SIZE,
  });

  const allStudents = useMemo(() => {
    return data?.pages.flatMap((page) => page.data) ?? [];
  }, [data?.pages]);

  const totalCount = data?.pages[0]?.totalCount ?? 0;

  const { data: creditData, isLoading: creditDataLoading } =
    useCreditsBySchoolQuery({ schoolId });

  const [rowSelection, setRowSelection] = useState({});

  const combinedData = useMemo(() => {
    return allStudents.map((student) => {
      const additionalInfo = creditData?.find(
        (info) => info.student_id === student.student_id
      );
      return {
        ...student,
        creditStatus: additionalInfo ? additionalInfo.status : null,
        redeemed: additionalInfo ? additionalInfo.redeemed_at : null,
        creditAssessmentId: additionalInfo
          ? (additionalInfo.assessment_id ?? null)
          : null,
      };
    });
  }, [allStudents, creditData]);

  const columnHelper = createColumnHelper<CombinedStudentData>();

  const columns = useMemo(() => {
    return [
      columnHelper.accessor("surname", {
        id: "surname",
        cell: ({ row }) => {
          const name = `${row.original.surname}, ${row.original.first_names.charAt(0)}`;
          return (
            <Link
              href={`/school/analysis/report/${row.original.student_code}`}
              className="blueLink"
            >
              <Text
                color={theme.colors.ui.link.hex}
                variant="sm"
                fontWeight={600}
                cursor="pointer"
                _hover={{ textDecoration: "underline" }}
              >
                {name}
              </Text>
            </Link>
          );
        },
        header: "Name",
        sortingFn: (rowA, rowB) => {
          const surnameA = rowA.original.surname.toLowerCase();
          const surnameB = rowB.original.surname.toLowerCase();
          return surnameA.localeCompare(surnameB);
        },
      }),
      columnHelper.accessor("year", {
        cell: (info) => (
          <Text variant="sm" fontWeight={600}>
            {info.getValue()}
          </Text>
        ),
        header: "Year group",
      }),
      columnHelper.accessor("creditStatus", {
        cell: (info) => {
          const creditStatus = info.getValue();
          let creditBadge = (
            <>
              <Box
                w="8px"
                h="8px"
                borderRadius="8px"
                bgColor={theme.colors.ui.grey_02.hex}
              />
              <Box>Unassigned</Box>
            </>
          );

          if (creditStatus === CreditStatus.InProgress) {
            creditBadge = (
              <>
                <Box
                  w="8px"
                  h="8px"
                  borderRadius="8px"
                  bgColor={theme.colors.ui.alert_orange_01.hover.hex}
                />
                <Box>In progress</Box>
              </>
            );
          }

          if (creditStatus === CreditStatus.Assigned) {
            creditBadge = (
              <>
                <Box
                  w="8px"
                  h="8px"
                  borderRadius="8px"
                  bgColor={theme.colors.primary.purple.hex}
                />
                <Box>Assigned</Box>
              </>
            );
          }
          if (creditStatus === CreditStatus.Redeemed) {
            creditBadge = (
              <>
                <Box
                  w="8px"
                  h="8px"
                  borderRadius="8px"
                  bgColor={theme.colors.ui.alert_green_01.hex}
                />
                <Box>
                  Completed:{" "}
                  {info.row.original.redeemed &&
                    format(parseISO(info.row.original.redeemed), "dd/MM/yyyy")}
                </Box>
              </>
            );
          }
          return (
            <>
              <Box
                display="flex"
                alignItems="center"
                gap={theme.spacing.xs.px}
                fontWeight={400}
              >
                {creditBadge}
              </Box>
            </>
          );
        },
        header: SCHOOL_TEST_NAME,
      }),
      columnHelper.accessor("student_code", {
        cell: (info) => (
          <Text variant="sm" fontWeight={600}>
            {info.getValue()}
          </Text>
        ),
        header: "Login ID",
      }),
      columnHelper.accessor("student_id", {
        cell: ({ row }) => {
          const { creditStatus, student_id, student_code } = row.original;

          if (creditStatus === "Redeemed") {
            return <PDFDownloadMenu type="icon" studentCode={student_code} />;
          }

          if (creditStatus !== "Expired") {
            return (
              <StudentsTestActions studentId={student_id} schoolId={schoolId} />
            );
          }

          return <Box w="40px" h="40px" />;
        },
        header: "",
      }),
    ];
  }, [schoolId]);

  const isInitialLoad = studentsLoading && allStudents.length === 0;

  if (isInitialLoad || creditDataLoading) {
    return <Spinner />;
  }

  if (!data || !creditData) {
    return null;
  }

  if (allStudents.length === 0) {
    return (
      <Panel preset="panel">
        <Box
          maxW="600px"
          textAlign="center"
          display="flex"
          flexDir="column"
          alignItems="center"
        >
          <Text element="h4" variant="md">
            👋 Add your first student!
          </Text>
          <Text element="p" variant="md" mt={theme.spacing.md.px}>
            To add your students, you can use our import tool.{" "}
            <Link
              href="https://www.talamo.co.uk/wiki/using-talamo/adding-students"
              target="_blank"
            >
              <Text
                element="span"
                variant="md"
                mb={theme.spacing.lg.px}
                color={theme.colors.ui.link.hex}
                cursor="pointer"
                _hover={{ textDecoration: "underline" }}
              >
                Read the instructions here and download our template
              </Text>
            </Link>{" "}
            to get started. Once you&apos;ve filled it in, come back here and
            click Add students.
          </Text>
          <Box
            mt={theme.spacing.md.px}
            display="flex"
            gap={theme.spacing.md.px}
          >
            <Button
              size="sm"
              onClick={() => router.push("/school/upload-single-student")}
            >
              Add 1 student
            </Button>
            <Button
              size="sm"
              onClick={() => router.push("/school/bulk-upload-students")}
            >
              Add multiple students
            </Button>
          </Box>
        </Box>
      </Panel>
    );
  }

  const selectedStudentIds = Object.keys(rowSelection)
    .filter((rowId) => rowSelection[rowId])
    .map((rowId) => allStudents[rowId].student_id);

  return (
    <>
      <Box display="flex" h="40px" justifyContent="flex-end">
        {selectedStudentIds.length > 0 && (
          <Box
            display="inline-flex"
            alignItems="center"
            gap={theme.spacing.md.px}
            alignSelf="flex-end"
          >
            <Text variant="lg" fontWeight={700}>
              {selectedStudentIds.length} selected
            </Text>
            <StudentsBulkActions
              studentIds={selectedStudentIds}
              schoolId={schoolId}
              setRowSelection={setRowSelection}
            />
          </Box>
        )}
      </Box>
      <Box overflow="scroll">
        <Box>
          <DataTable
            columns={columns}
            data={combinedData}
            rowSelection={rowSelection}
            setRowSelection={setRowSelection}
            sorting={sorting}
            setSorting={setSorting}
          />
          <Box textAlign="center" mt="6">
            <Text fontWeight="bold">
              Showing {allStudents.length}/{totalCount}
            </Text>
            {hasNextPage && (
              <Button
                mt="4"
                onClick={() => fetchNextPage()}
                isLoading={isFetchingNextPage}
                mx="auto"
              >
                Load more
              </Button>
            )}
          </Box>
        </Box>
      </Box>
    </>
  );
};
