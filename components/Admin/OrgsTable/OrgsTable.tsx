"use client";

import { Spinner } from "@chakra-ui/react";
import { CheckCircleIcon, ClockIcon } from "@heroicons/react/24/solid";
import {
  createColumnHelper,
  RowSelectionState,
  SortingState,
} from "@tanstack/react-table";
import { useState } from "react";

import { OrgDetailView, useOrgsQuery } from "@/hooks/queries/useOrgsQuery";
import { theme } from "@/styles/theme";
import { InviteStatus } from "@/types/admin";

import { DataTable } from "../DataTable/DataTable";
import { OrgsActions } from "./OrgsActions";

export const OrgsTable = () => {
  const { data: orgs, isLoading: orgLoading } = useOrgsQuery();
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});
  const [sorting, setSorting] = useState<SortingState>([]);

  if (orgLoading || !orgs) {
    return <Spinner />;
  }

  const columnHelper = createColumnHelper<OrgDetailView>();

  const columns = [
    columnHelper.accessor("org_name", {
      cell: (info) => info.getValue(),
      header: "Org name",
    }),
    columnHelper.accessor("admin_name", {
      cell: (info) => info.getValue(),
      header: "Primary contact",
    }),
    columnHelper.accessor("admin_email", {
      cell: (info) => info.getValue(),
      header: "Email",
    }),
    columnHelper.accessor("school_count", {
      cell: (info) => info.getValue(),
      header: "Schools",
    }),

    columnHelper.accessor("invite_status", {
      cell: ({ row }) =>
        row.original.invite_status === InviteStatus.Pending ? (
          <ClockIcon
            width={16}
            height={16}
            color={theme.colors.ui.alert_orange_01.hex}
          />
        ) : (
          <CheckCircleIcon
            width={16}
            height={16}
            color={theme.colors.ui.alert_green_01.hex}
          />
        ),
      header: "Status",
    }),
    columnHelper.accessor("org_id", {
      cell: ({ row }) => (
        <OrgsActions
          email={row.original.admin_email}
          inviteStatus={row.original.invite_status}
        />
      ),
      header: "",
    }),
  ];

  return (
    <DataTable
      columns={columns}
      data={orgs}
      rowSelection={rowSelection}
      setRowSelection={setRowSelection}
      sorting={sorting}
      setSorting={setSorting}
    />
  );
};
