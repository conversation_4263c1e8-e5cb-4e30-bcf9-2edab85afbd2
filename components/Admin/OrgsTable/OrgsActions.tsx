"use client";

import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
} from "@chakra-ui/react";
import {
  CheckCircleIcon,
  Cog6ToothIcon,
  EnvelopeIcon,
  WrenchIcon,
} from "@heroicons/react/24/solid";
import { startTransition, useState } from "react";

import { Text } from "@/components/v2";
import { resendInvite } from "@/server/admin/resendInvite";
import { InviteStatus } from "@/types/admin";

type OrgsActionsProps = {
  email: string;
  inviteStatus: InviteStatus;
};

const iconSize = 17;

export const OrgsActions = ({ email, inviteStatus }: OrgsActionsProps) => {
  const [inviteResendStatus, setInviteResendStatus] = useState("Resend invite");

  const handleResendInvite = () => {
    setInviteResendStatus("Sending");
    startTransition(() => {
      resendInvite({ email }).then(() => {
        setInviteResendStatus("Sent");
      });
    });
  };

  return (
    <>
      <Menu>
        <MenuButton
          as={IconButton}
          aria-label="Options"
          icon={<Cog6ToothIcon width={iconSize} />}
          variant="outline"
        />
        <MenuList>
          {inviteStatus === InviteStatus.Pending && (
            <MenuItem
              icon={
                inviteResendStatus === "Sending" ? (
                  <Spinner size="xs" />
                ) : inviteResendStatus === "Sent" ? (
                  <CheckCircleIcon width={iconSize} />
                ) : (
                  <EnvelopeIcon width={iconSize} />
                )
              }
              closeOnSelect={false}
              onClick={handleResendInvite}
            >
              <Text element="h6" variant="xs">
                {" "}
                {inviteResendStatus}
              </Text>
            </MenuItem>
          )}
          <MenuItem
            icon={<WrenchIcon width={iconSize} />}
            onClick={() => alert("Coming soon")}
          >
            <Text element="h6" variant="xs">
              Edit details
            </Text>
          </MenuItem>
        </MenuList>
      </Menu>
    </>
  );
};
