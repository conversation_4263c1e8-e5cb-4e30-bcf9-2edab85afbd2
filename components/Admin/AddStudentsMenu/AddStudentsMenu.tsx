"use client";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>u<PERSON><PERSON> } from "@chakra-ui/react";
import { UserGroupIcon, UserIcon } from "@heroicons/react/24/solid";
import { useRouter } from "next/navigation";

import { Button, Text } from "@/components/v2";
import { theme } from "@/styles/theme";

type AddStudentsMenuProps = {
  numStudents: number;
};

const iconSize = 17;

export const AddStudentsMenu = ({ numStudents }: AddStudentsMenuProps) => {
  const router = useRouter();
  return (
    <>
      <Menu>
        <MenuButton>
          <Button size="sm">Add more students</Button>
        </MenuButton>
        <MenuList zIndex={10}>
          <MenuItem
            icon={
              <UserIcon
                width={iconSize}
                color={theme.colors.primary.black.hex}
              />
            }
            onClick={() => {
              router.push("/school/upload-single-student");
            }}
          >
            <Text element="h6" variant="xs">
              Add 1 student
            </Text>
          </MenuItem>
          <MenuItem
            icon={
              <UserGroupIcon
                width={iconSize}
                color={theme.colors.primary.black.hex}
              />
            }
            onClick={() => {
              router.push("/school/bulk-upload-students");
            }}
          >
            <Text element="h6" variant="xs">
              Add multiple students
            </Text>
          </MenuItem>
        </MenuList>
      </Menu>
    </>
  );
};
