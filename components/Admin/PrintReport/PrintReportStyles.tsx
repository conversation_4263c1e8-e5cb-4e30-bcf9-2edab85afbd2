import { StyleSheet } from "@react-pdf/renderer";

import { theme } from "@/styles/theme";

export const styles = StyleSheet.create({
  page: {
    fontFamily: "Objectivity",
    backgroundColor: "#ffffff",
    padding: "48",
    color: theme.colors.primary.black.hex,
  },
  verticalCenter: {
    display: "flex",
    flex: 1,
    justifyContent: "center",
  },
  row: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
  },
  recRow: {
    display: "flex",
    flexDirection: "row",
    alignItems: "flex-start",
    marginBottom: 10,
    marginRight: 18,
    flexWrap: "nowrap",
    break: "avoid",
  },
  headingRow: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 20,
  },
  riskHeadingRow: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 10,
    justifyContent: "space-between",
  },
  logo: {
    width: 50,
    height: "auto",
    marginBottom: 20,
    position: "absolute",
    top: 40,
    left: 40,
  },
  squareIcon: {
    width: 30,
    height: 30,
    marginRight: 10,
  },
  squareIconSmall: {
    width: 20,
    height: 20,
    marginRight: 10,
  },
  recIconSmall: {
    width: 20,
    height: 20,
    marginRight: 10,
  },
  recIconXSmall: {
    width: 10,
    height: 10,
    marginRight: 6,
  },
  recTextSpacing: {
    paddingTop: 5,
    flex: 1,
  },
  headingRowSpacing: {
    paddingTop: 8,
  },
  title: {
    fontSize: 36,
    fontWeight: 700,
  },
  subTitle: {
    fontSize: 18,
    fontWeight: 700,
  },
  heading3: {
    fontSize: 15,
    fontWeight: 700,
  },
  heading4: {
    fontSize: 13,
    fontWeight: 700,
  },
  testAreaHeading: {
    fontSize: 15,
    fontWeight: 700,
  },
  heading5: {
    fontSize: 12,
    fontWeight: 700,
    marginTop: 5,
    marginBottom: 3,
  },
  heading5Link: {
    fontSize: 12,
    fontWeight: 700,
    marginTop: 5,
    marginBottom: 3,
    color: theme.colors.ui.link.hex,
  },
  heading6: {
    fontSize: 11,
    fontWeight: 700,
    marginTop: 5,
    marginBottom: 3,
  },
  recommendationTitle: {
    fontSize: 23,
    fontWeight: 700,
  },
  riskLevelHeading: {
    fontSize: 13,
    fontWeight: 700,
  },
  riskLink: {
    fontSize: 12,
    fontWeight: 700,
    color: theme.colors.ui.link.hex,
    textDecoration: "underline",
  },
  heading6Link: {
    fontSize: 11,
    fontWeight: 700,
    marginTop: 5,
    marginBottom: 3,
    color: theme.colors.ui.link.hex,
  },
  badge: {
    padding: "10px",
    borderRadius: 4,
    fontSize: 10,
    alignSelf: "flex-start",
    marginBottom: 20,
    fontWeight: 700,
  },
  content: {
    fontSize: 11,
    lineHeight: 1.4,
  },
  areaOfNeedContent: {
    fontSize: 11,
    lineHeight: 1.4,
  },
  disclaimer: {
    fontSize: 11,
    lineHeight: 1.4,
    marginTop: 60,
    fontStyle: "italic",
  },
  date: {
    marginTop: 14,
    fontSize: 12,
  },
  spacer: {
    marginTop: 20,
  },
  bold: {
    fontWeight: 700,
  },
  footer: {
    position: "absolute",
    bottom: 40,
    left: 40,
    right: 40,
    fontSize: 9,
    color: theme.colors.tertiary.grey_01.hex,
    textAlign: "left",
  },
  pageNum: {
    position: "absolute",
    bottom: 40,
    right: 40,
    fontSize: 9,
    color: theme.colors.tertiary.grey_01.hex,
    textAlign: "right",
  },
  catImage: {
    position: "absolute",
    bottom: 0,
    right: 0,
    width: 320,
    height: "auto",
  },
  contentSection: {
    marginTop: 28,
  },
  contentSectionInner: {
    marginTop: 18,
    flexWrap: "nowrap",
    break: "avoid",
  },
  testAreaHeadingRow: {
    marginTop: 18,
    flexWrap: "nowrap",
    break: "avoid",
    marginBottom: 5,
  },
  testAreaDescriptionRow: {
    marginBottom: 18,
    flexWrap: "nowrap",
    break: "avoid",
  },
  contentSectionRisk: {
    marginBottom: 18,
    flexWrap: "nowrap",
    break: "avoid",
  },
  riskTitle: {
    display: "flex",
    alignItems: "center",
    textAlign: "left",
    flexDirection: "row",
    gap: 6,
    borderRadius: 14,
    width: "auto",
    marginBottom: 14,
  },
  aonRow: {
    display: "flex",
    alignItems: "center",
    textAlign: "left",
    justifyContent: "space-between",
    flexDirection: "row",
    gap: 6,
    width: "100%",
  },
  aonSection: {
    flexWrap: "nowrap",
    break: "avoid",
  },
  mlAuto: {
    marginLeft: "auto",
  },
  mr: {
    marginRight: 10,
  },
  riskImage: {
    width: 30,
  },
  highPriorityWrap: {
    backgroundColor: theme.colors.secondary.purple_05.hex,
    borderRadius: 6,
    marginBottom: 8,
    paddingVertical: 12,
    paddingHorizontal: 22,
  },
  highPriorityWrapGreen: {
    backgroundColor: theme.colors.ui.alert_green_02.hex,
    borderRadius: 6,
    marginBottom: 8,
    paddingVertical: 12,
    paddingHorizontal: 22,
  },
  highPriorityHeading: {
    fontSize: 14,
    fontWeight: 700,
    color: theme.colors.secondary.purple_01.hex,
    marginVertical: 5,
  },
  highPriorityHeadingGreen: {
    fontSize: 14,
    fontWeight: 700,
    color: theme.colors.ui.alert_green_01.hex,
    marginVertical: 5,
  },
  highPriorityRow: {
    display: "flex",
    alignItems: "center",
    textAlign: "left",
    flexDirection: "row",
    gap: 6,
  },
  highPriorityIcon: {
    width: 38,
    height: 38,
    marginRight: 10,
  },
  typeRow: {
    display: "flex",
    alignItems: "center",
    textAlign: "left",
    flexDirection: "row",
    gap: 6,
    width: "auto",
    marginBottom: 10,
  },
  typeRowItem: {
    display: "flex",
    alignItems: "center",
    textAlign: "left",
    flexDirection: "row",
    gap: 3,
    width: "auto",
    backgroundColor: theme.colors.ui.grey_04.hex,
    paddingHorizontal: 6,
    paddingVertical: 4,
    borderRadius: 14,
  },
  typeLabelRow: {
    display: "flex",
    alignItems: "center",
    textAlign: "left",
    flexDirection: "row",
    gap: 6,
    width: "auto",
  },
  typeLabel: {
    fontSize: 9,
  },
  typeLabelBold: {
    fontSize: 9,
    fontWeight: 700,
  },
});

export const htmlStyles = StyleSheet.create({
  p: {
    fontSize: 11,
    lineHeight: 1.4,
    marginBottom: 4,
    marginTop: 0,
  },
  ul: {
    width: "100%",
  },
  ol: {
    width: "100%",
  },
  li: {
    fontSize: 11,
    lineHeight: 1.4,
    marginBottom: 4,
    paddingLeft: 7,
    width: "100%",
  },
  a: {
    color: theme.colors.ui.link.hex,
  },
});

export const htmlStylesSub = StyleSheet.create({
  p: {
    fontSize: 10,
    lineHeight: 1.3,
    marginBottom: 4,
    marginTop: 4,
  },
  ul: {
    width: "100%",
  },
  ol: {
    width: "100%",
  },
  li: {
    fontSize: 10,
    lineHeight: 1.3,
    marginBottom: 4,
    paddingLeft: 7,
    width: "100%",
  },
  a: {
    color: theme.colors.ui.link.hex,
  },
});

export const htmlStylesTitle = StyleSheet.create({
  p: {
    fontSize: 12,
    lineHeight: 1.4,
    marginBottom: 4,
    marginTop: 4,
    fontWeight: "bold",
  },
  li: {
    fontSize: 11,
    lineHeight: 1.4,
    marginBottom: 4,
    paddingLeft: 7,
  },
  a: {
    color: theme.colors.ui.link.hex,
  },
});

export const htmlStylesAppendix = StyleSheet.create({
  p: {
    fontSize: 11,
    lineHeight: 1.4,
    marginBottom: 4,
  },
  li: {
    fontSize: 11,
    lineHeight: 1.4,
    marginBottom: 4,
    paddingLeft: 7,
  },
  a: {
    color: theme.colors.ui.link.hex,
    fontWeight: "bold",
  },
});
