import { Table, TD, TH, TR } from "@ag-media/react-pdf-table";

import { theme } from "@/styles/theme";

interface PDFTableProps {
  sortedCategories: Array<any>;
  formatCategoryName: (name: string) => string;
  formatConfidenceInterval: (confidenceInterval: any) => string;
}

export const PDFTable = ({
  sortedCategories,
  formatCategoryName,
  formatConfidenceInterval,
}: PDFTableProps) => {
  return (
    <Table
      style={{
        border: `1px solid ${theme.colors.ui.grey_03.hex}`,
        fontSize: 9,
      }}
      tdStyle={{
        paddingVertical: "8px",
        paddingHorizontal: "4px",
      }}
      weightings={[0.3, 0.175, 0.175, 0.175, 0.175]}
    >
      <TH>
        <TD
          style={{
            borderTop: 0,
            borderRight: 0,
            borderLeft: 0,
            textAlign: "center",
          }}
        ></TD>
        <TD
          style={{
            borderTop: 0,
            borderRight: 0,
            borderLeft: 0,
            textAlign: "center",
          }}
        >
          Standard score
        </TD>
        <TD
          style={{
            borderTop: 0,
            borderRight: 0,
            borderLeft: 0,
            textAlign: "center",
          }}
        >
          Score rank
        </TD>
        <TD
          style={{
            borderTop: 0,
            borderRight: 0,
            borderLeft: 0,
            textAlign: "center",
          }}
        >
          Percentile rank
        </TD>
        <TD
          style={{
            borderTop: 0,
            borderRight: 0,
            borderLeft: 0,
            textAlign: "center",
          }}
        >
          95% conf. int
        </TD>
      </TH>
      {sortedCategories.map(([category, data], index) => {
        const { composite } = data;

        return (
          <TR key={index}>
            <TD
              style={{
                borderRight: 0,
                borderBottom: index === data.length - 1 ? 0 : undefined,
                borderLeft: 0,
                fontWeight: "bold",
              }}
            >
              {formatCategoryName(category)}
            </TD>
            <TD
              style={{
                borderRight: 0,
                borderBottom: index === data.length - 1 ? 0 : undefined,
                borderLeft: 0,
                textAlign: "center",
              }}
            >
              {composite.standardScore}
            </TD>
            <TD
              style={{
                borderRight: 0,
                borderBottom: index === data.length - 1 ? 0 : undefined,
                borderLeft: 0,
                textAlign: "center",
              }}
            >
              {composite.scoreRank}
            </TD>
            <TD
              style={{
                borderRight: 0,
                borderBottom: index === data.length - 1 ? 0 : undefined,
                borderLeft: 0,
                textAlign: "center",
              }}
            >
              {composite.percentileRank}
            </TD>
            <TD
              style={{
                borderRight: 0,
                borderBottom: index === data.length - 1 ? 0 : undefined,
                borderLeft: 0,
                textAlign: "center",
              }}
            >
              {formatConfidenceInterval(composite.confidenceInterval)}
            </TD>
          </TR>
        );
      })}
    </Table>
  );
};
