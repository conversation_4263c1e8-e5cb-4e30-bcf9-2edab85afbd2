import { theme } from "@/styles/theme";
import { PDFType } from "@/types/analysis";

import { styles } from "../PrintReportStyles";

type PdfBadgeProps = {
  type: PDFType;
};

export const PdfBadge = ({ type }: PdfBadgeProps) => {
  return {
    ...styles.badge,
    backgroundColor:
      type === PDFType.Teacher
        ? theme.colors.ui.alert_red_02.hex
        : type === PDFType.Parent
          ? theme.colors.ui.alert_green_02.hex
          : theme.colors.secondary.purple_04.hex,
    color:
      type === PDFType.Teacher
        ? theme.colors.ui.alert_red_01.hex
        : type === PDFType.Parent
          ? theme.colors.ui.alert_green_01.hex
          : theme.colors.primary.purple.hex,
  };
};
