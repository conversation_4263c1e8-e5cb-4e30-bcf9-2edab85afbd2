/* eslint-disable jsx-a11y/alt-text */
"use client";

import {
  Document,
  Font,
  Image,
  Link,
  Page,
  Text,
  View,
} from "@react-pdf/renderer";
import { format, parseISO } from "date-fns";
import Html from "react-pdf-html";

import { cardStyles } from "@/components/RiskCard/RiskCard";
import { theme } from "@/styles/theme";
import { AnalysisData, PDFType } from "@/types/analysis";
import {
  AreasOfNeedFallbackQuery,
  AreasOfNeedQuery,
  RecommendationCopyQuery,
  RecommendationTypeIconsQuery,
  ReportRecommendationsQuery,
  RiskLevelsQuery,
} from "@/types/graphql/graphql";
import { RecommendationCategory } from "@/types/recommendations";
import {
  getPDFContent,
  getRiskLevelContent,
  parseText,
  PDFContent,
} from "@/utils/admin/pdf-resource";
import { replaceString } from "@/utils/replacement/replacement";
import { calculateAge } from "@/utils/students/calculateAge";
import { camelCaseToSentence } from "@/utils/students/camelCaseToSentence";

import { getFilteredRecommendations } from "../Recommendations/RecommendationsTabs";
import { PdfBadge } from "./components/PdfBadge";
import { PDFTable } from "./components/PdfTable";
import { TalamoPdfLogo } from "./components/TalamoPdfLogo";
import {
  htmlStyles,
  htmlStylesSub,
  htmlStylesTitle,
  styles,
} from "./PrintReportStyles";

interface IPdfReportProps {
  studentCode: string;
  studentData: AnalysisData | null;
  type: PDFType;
  pdfResources: PDFContent[];
  chartBase64: string;
  riskLevelResources: RiskLevelsQuery;
  recommendationCopy: RecommendationCopyQuery;
  recommendations: ReportRecommendationsQuery["allRecommendations"];
  recommendationIcons: RecommendationTypeIconsQuery;
  areasOfNeed: AreasOfNeedQuery;
  areasOfNeedFallback: AreasOfNeedFallbackQuery;
}

export const PrintReportPDF = ({
  studentData,
  type,
  pdfResources: resources,
  chartBase64,
  riskLevelResources,
  recommendationCopy,
  recommendations,
  recommendationIcons,
  areasOfNeed,
  areasOfNeedFallback,
}: IPdfReportProps) => {
  if (!studentData || !recommendationCopy) {
    return null;
  }

  const badgeStyle = PdfBadge({ type });
  const dateExported = format(new Date(), "dd/MM/yy");
  const dateTaken = format(
    parseISO(studentData.assessment_meta.dateTaken || ""),
    "dd/MM/yy"
  );
  const recIcons = recommendationIcons.general?.recommendationTypeIcon;
  const durationIcon =
    recommendationIcons?.general?.recommendationTypeIcon.find(
      (icon) => icon.title === "duration"
    )?.url;

  //Student Data
  const firstName = studentData.student.first_names || "";
  const fullName = `${studentData.student.first_names} ${studentData.student.surname}`;
  const repl = {
    name: firstName,
    fullName: fullName,
  };

  //Risk levels
  const levelOfNeedRisk = studentData.analysis.levelOfNeedRisk;
  const dyslexiaRisk = studentData.analysis.dyslexiaRisk;

  const levelOfNeedCards = riskLevelResources.allRiskLevels.filter(
    ({ riskType, requiredRiskLevel }) =>
      riskType === "levelOfNeed" && requiredRiskLevel === levelOfNeedRisk
  );
  const dyslexiaCards = riskLevelResources.allRiskLevels.filter(
    ({ riskType, requiredRiskLevel }) =>
      riskType === "dyslexia" && requiredRiskLevel === dyslexiaRisk
  );

  //Support
  const studentAge = calculateAge(studentData.student.date_of_birth);
  const filteredRecommendations = getFilteredRecommendations(
    studentAge,
    studentData,
    recommendations
  );

  const categoryText = {
    "Extra Support": replaceString(
      recommendationCopy.general?.extraSupportCopy || "",
      { name: firstName }
    ),
    Classroom: replaceString(recommendationCopy.general?.classroomCopy || "", {
      name: firstName,
    }),
    "At Home": replaceString(recommendationCopy.general?.atHomeCopy || "", {
      name: firstName,
    }),
    "High Priority": replaceString(
      recommendationCopy.general?.highPriorityCopy || "",
      { name: firstName }
    ),
  };

  const replaceFirstName = (text) => {
    if (!text) {
      return "";
    }
    return text
      .replace(
        /{{name}}'s/g,
        `${firstName}${firstName.endsWith("s") ? "'" : "'s"}`
      )
      .replace(
        /{{name}}'/g,
        `${firstName}${firstName.endsWith("s") ? "'" : "'s"}`
      )
      .replace(/{{name}}/g, firstName)
      .replace("&rsquo;", "'");
  };

  const hasExtraSupport = filteredRecommendations.some(
    (rec) => rec.category === RecommendationCategory.ExtraSupport
  );
  const hasAtHome = filteredRecommendations.some(
    (rec) => rec.category === RecommendationCategory.AtHome
  );
  const hasClassroom = filteredRecommendations.some(
    (rec) => rec.category === RecommendationCategory.Classroom
  );

  //Level of need
  const formattedLevelOfNeedCards = getRiskLevelContent(
    levelOfNeedCards,
    type,
    repl
  );
  const formattedDyslexiaCards = getRiskLevelContent(dyslexiaCards, type, repl);

  //Resources
  const globalPrivacyLabel = `Private & confidential | Report generated ${dateExported}`;
  const coverType = getPDFContent(resources, "cover.type", type);
  const coverIntro = getPDFContent(resources, "cover.intro", type, repl);
  const coverImage = getPDFContent(resources, "cover.image", type);
  const coverImageUrl = parseText(coverImage) || "";
  const contentsIntro = getPDFContent(resources, "contents.intro", type);
  const contentsResults = getPDFContent(
    resources,
    "contents.results.body",
    type,
    repl
  );
  const contentsSupport = getPDFContent(
    resources,
    "contents.support.body",
    type,
    repl
  );
  const contentsInsights = getPDFContent(
    resources,
    "contents.insights.body",
    type,
    repl
  );
  const contentsDisclaimer = getPDFContent(
    resources,
    "contents.disclaimer",
    type
  );
  const resultsChartDescription = getPDFContent(
    resources,
    "results.chart.description",
    type,
    repl
  );
  const resultsTableDescription = getPDFContent(
    resources,
    "results.table.description",
    type,
    repl
  );

  const supportIntro = getPDFContent(resources, "support.intro", type, repl);
  const risksLinkText = getPDFContent(
    resources,
    "results.risks.link.text",
    type
  );
  const risksLinkUrl = getPDFContent(resources, "results.risks.link.url", type);
  const risksLinkUrlParsed = parseText(risksLinkUrl?.trim() || "");

  //Analysis table
  const formatConfidenceInterval = (confidenceInterval) => {
    const { lowerBound, upperBound } = confidenceInterval;
    return lowerBound && upperBound ? `${lowerBound} - ${upperBound}` : "-";
  };

  const formatCategoryName = (name: string) => {
    const sentenceCase = name
      .replace(/([A-Z])/g, " $1")
      .trim()
      .toLowerCase();
    return sentenceCase.charAt(0).toUpperCase() + sentenceCase.slice(1);
  };

  const cognitiveProfile = studentData.result_data.cognitiveProfile;

  const orderedCategories = [
    "visualReasoning",
    "verbalReasoning",
    "phonologicalAwareness",
    "processingSpeed",
    "workingMemory",
    "readingSpeed",
    "readingComprehension",
    "spelling",
  ];

  const sortedCategories = orderedCategories
    .filter((category) => cognitiveProfile[category])
    .map((category) => [category, cognitiveProfile[category]]);

  const fallback = areasOfNeedFallback.general;

  const testAreaMapping: Record<string, string> = {
    spelling: "spelling",
    readingSpeed: "reading_speed",
    workingMemory: "memory",
    processingSpeed: "speed",
    verbalReasoning: "verbal",
    visualReasoning: "visual",
    readingComprehension: "reading_comprehension",
    phonologicalAwareness: "phonological",
  };

  const testAreaMappingRecs: Record<string, string> = {
    spelling: "spelling",
    readingSpeed: "readingSpeed",
    workingMemory: "workingMemory",
    processingSpeed: "processingSpeed",
    verbalReasoning: "verbalReasoning",
    visualReasoning: "visualReasoning",
    readingComprehension: "readingComprehension",
    phonologicalAwareness: "phonologicalAwareness",
  };

  const levelColorMapping: Record<string, string> = {
    red: theme.colors.ui.alert_red_02.hex,
    orange: theme.colors.ui.alert_orange_03.hex,
    yellow: theme.colors.tertiary.yellow_03.hex,
    green: theme.colors.ui.alert_green_02.hex,
  };

  const scores = Object.fromEntries(
    Object.entries(cognitiveProfile).map(([key, value]) => [
      testAreaMapping[key] || key,
      Number(value.composite.standardScore),
    ])
  );
  const scoreRank = Object.fromEntries(
    Object.entries(cognitiveProfile).map(([key, value]) => [
      testAreaMapping[key] || key,
      value.composite.scoreRank,
    ])
  );
  const scoreRankRecs = Object.fromEntries(
    Object.entries(cognitiveProfile).map(([key, value]) => [
      testAreaMappingRecs[key] || key,
      value.composite.scoreRank,
    ])
  );

  const relevantAreas = areasOfNeed.allAreaOfNeeds
    .filter((area) => {
      const normalizedTestArea = area.testArea.toLowerCase();
      const actualScore = Number(scores[normalizedTestArea]);
      const minScore = Number(area.minimumScore);
      const maxScore = Number(area.maximumScore);

      const pass = actualScore >= minScore && actualScore < maxScore;

      return pass;
    })
    .sort((a, b) => {
      const scoreA = Number(scores[a.testArea.toLowerCase()]);
      const scoreB = Number(scores[b.testArea.toLowerCase()]);
      return scoreA - scoreB;
    });

  return (
    <Document>
      {/* Cover page */}
      <Page size="A4" style={styles.page}>
        <View style={styles.logo}>
          <TalamoPdfLogo />
        </View>
        <View style={styles.verticalCenter}>
          <Text style={badgeStyle}>{parseText(coverType)}</Text>
          <Text style={styles.title}>{`${fullName}`}</Text>
          <View style={styles.contentSection}>
            <Text style={styles.content}>{parseText(coverIntro)}</Text>
          </View>
          <Text style={styles.date}>
            Assessment completed on <Text style={styles.bold}>{dateTaken}</Text>
          </Text>
        </View>
        <Text style={styles.footer} fixed>
          {globalPrivacyLabel}
        </Text>
        <Image src={coverImageUrl} style={styles.catImage} />
      </Page>

      {/* Overview page */}
      <Page size="A4" style={styles.page}>
        <Text style={styles.title}>In this report</Text>
        <Text style={styles.content}>{parseText(contentsIntro)}</Text>
        <View style={styles.contentSection}>
          <View style={styles.headingRow}>
            <View style={styles.squareIcon}>
              <Image src="/images/pdf/results.png" />
            </View>
            <View style={styles.headingRowSpacing}>
              <Text style={styles.subTitle}>Results</Text>
            </View>
          </View>
          <Text style={styles.content}>{parseText(contentsResults)}</Text>
        </View>
        <View style={styles.contentSection}>
          <View style={styles.headingRow}>
            <View style={styles.squareIcon}>
              <Image src="/images/pdf/appendix.png" />
            </View>
            <View style={styles.headingRowSpacing}>
              <Text style={styles.subTitle}>Insights</Text>
            </View>
          </View>
          <Text style={styles.content}>{parseText(contentsInsights)}</Text>
        </View>
        <View style={styles.contentSection}>
          <View style={styles.headingRow}>
            <View style={styles.squareIcon}>
              <Image src="/images/pdf/support.png" />
            </View>
            <Text style={styles.subTitle}>How to support</Text>
          </View>
          <Text style={styles.content}>{parseText(contentsSupport)}</Text>
        </View>
        <Text style={styles.disclaimer}>{parseText(contentsDisclaimer)}</Text>
        <Text style={styles.footer} fixed>
          {globalPrivacyLabel}
        </Text>
        <Text
          style={styles.pageNum}
          render={({ pageNumber }) => `${pageNumber}`}
          fixed
        />
      </Page>

      {/* Results page */}
      <Page size="A4" style={styles.page}>
        <View style={styles.headingRow}>
          <View style={styles.squareIcon}>
            <Image src="/images/pdf/results.png" />
          </View>
          <View style={styles.headingRowSpacing}>
            <Text style={styles.title}>Results</Text>
          </View>
        </View>

        <View>
          <Text style={styles.heading3}>Cognitive profile</Text>
          <Image src={chartBase64} />
        </View>
        <View style={styles.contentSection}>
          <Text style={styles.content}>
            {parseText(resultsChartDescription)}
          </Text>
        </View>

        <Text style={styles.footer} fixed>
          {globalPrivacyLabel}
        </Text>
        <Text
          style={styles.pageNum}
          render={({ pageNumber }) => `${pageNumber}`}
          fixed
        />
      </Page>

      {/* Results page 2 */}
      <Page size="A4" style={styles.page}>
        <View style={styles.headingRow}>
          <View style={styles.squareIconSmall}>
            <Image src="/images/pdf/results.png" />
          </View>
          <Text style={styles.heading3}>Results</Text>
        </View>
        <Text style={styles.heading3}>Scores</Text>
        <View>
          <PDFTable
            sortedCategories={sortedCategories}
            formatCategoryName={formatCategoryName}
            formatConfidenceInterval={formatConfidenceInterval}
          />
        </View>
        <View style={styles.contentSection}>
          <Html stylesheet={htmlStyles} resetStyles={true}>
            {resultsTableDescription as string}
          </Html>
        </View>
        <Text style={styles.footer} fixed>
          {globalPrivacyLabel}
        </Text>
        <Text
          style={styles.pageNum}
          render={({ pageNumber }) => `${pageNumber}`}
          fixed
        />
      </Page>

      {/* Insights page */}
      <Page size="A4" style={styles.page}>
        <View style={styles.headingRow}>
          <View style={styles.squareIcon}>
            <Image src="/images/pdf/appendix.png" />
          </View>
          <View style={styles.headingRowSpacing}>
            <Text style={styles.title}>Insights</Text>
          </View>
        </View>

        <View style={styles.riskHeadingRow}>
          <Text style={styles.subTitle}>Risks</Text>
          <Link href={risksLinkUrlParsed} style={styles.riskLink}>
            {parseText(risksLinkText)}
          </Link>
        </View>
        {formattedLevelOfNeedCards.length > 0 && (
          <View style={styles.contentSectionRisk}></View>
        )}
        {formattedLevelOfNeedCards.map((card) => {
          const arrayPos = card.requiredRiskLevel - 1;
          return (
            <View key={card.id}>
              <View
                style={{
                  ...styles.riskTitle,
                  backgroundColor: cardStyles[arrayPos].bgColor,
                }}
              >
                <Image
                  style={styles.riskImage}
                  src={`/images/icons/pdf/risk-${card.requiredRiskLevel}.png`}
                />
                <Text style={styles.heading4}>{card.title}</Text>
              </View>
              <Text style={styles.content}>
                {parseText(card.description.replace(/\n/g, " ")).trimStart()}
              </Text>
            </View>
          );
        })}
        {formattedDyslexiaCards.length > 0 && (
          <View style={styles.contentSectionRisk}></View>
        )}
        {formattedDyslexiaCards.map((card) => {
          const arrayPos = card.requiredRiskLevel - 1;
          return (
            <View key={card.id}>
              <View
                style={{
                  ...styles.riskTitle,
                  backgroundColor: cardStyles[arrayPos].bgColor,
                }}
              >
                <Image
                  style={styles.riskImage}
                  src={`/images/icons/pdf/risk-${card.requiredRiskLevel}.png`}
                />
                <Text style={styles.heading4}>{card.title}</Text>
              </View>
              <Text style={styles.content}>
                {parseText(card.description.replace(/\n/g, " ")).trimStart()}
              </Text>
            </View>
          );
        })}
        <View style={styles.spacer}>
          <View style={styles.riskHeadingRow}>
            <Text style={styles.subTitle}>Strengths and challenges</Text>
          </View>
        </View>
        {relevantAreas.length > 0 ? (
          relevantAreas.map((area) => {
            const normalizedTestArea = area.testArea.toLowerCase();
            const actualScore = scoreRank[normalizedTestArea];

            const scoreDescription = area.description.replace(
              /{{score}}/g,
              `<strong>${actualScore}</strong>`
            );

            return (
              <View key={area.id} wrap={false} style={styles.spacer}>
                <View style={styles.aonSection}>
                  <View wrap={false} style={styles.aonRow}>
                    <View
                      style={{
                        ...styles.riskTitle,
                        backgroundColor:
                          levelColorMapping[area.flagColour || ""],
                      }}
                    >
                      <Image
                        style={styles.riskImage}
                        src={`${area.icon.url}?fm=png320&w=140`}
                      />
                      <View style={styles.mr}>
                        <Text style={styles.heading4}>{area.title}</Text>
                      </View>
                    </View>
                    <View style={styles.mlAuto}>
                      <Link
                        href={area.linkUrl || ""}
                        style={styles.heading6Link}
                      >
                        {area.linkTitle}
                      </Link>
                    </View>
                  </View>
                  <Text style={styles.areaOfNeedContent}>
                    <Html stylesheet={htmlStyles} resetStyles={true}>
                      {replaceFirstName(scoreDescription)}
                    </Html>
                  </Text>
                </View>
              </View>
            );
          })
        ) : (
          <View>
            <View style={styles.aonRow}>
              <View
                style={{
                  ...styles.riskTitle,
                  backgroundColor: theme.colors.ui.alert_green_02.hex,
                }}
              >
                <Image
                  style={styles.riskImage}
                  src={`${fallback?.aonFallbackIcon.url}?fm=png320&w=140`}
                />
                <View style={styles.mr}>
                  <Text style={styles.heading4}>
                    {replaceFirstName(fallback?.aonFallbackTitle)}
                  </Text>
                </View>
              </View>
            </View>
            <Text style={styles.areaOfNeedContent}>
              {parseText(replaceFirstName(fallback?.aonFallbackDescription))}
            </Text>
          </View>
        )}

        <Text style={styles.footer} fixed>
          {globalPrivacyLabel}
        </Text>
        <Text
          style={styles.pageNum}
          render={({ pageNumber }) => `${pageNumber}`}
          fixed
        />
      </Page>

      {/* How to support - In the classroom */}
      {hasClassroom && type !== PDFType.Parent && (
        <Page size="A4" style={styles.page}>
          <View style={styles.headingRow}>
            <View style={styles.squareIcon}>
              <Image src="/images/pdf/support.png" />
            </View>
            <View style={styles.headingRowSpacing}>
              <Text style={styles.title}>How to support</Text>
            </View>
          </View>
          <Text style={styles.content}>{parseText(supportIntro)}</Text>
          <View style={styles.contentSection}>
            <Text style={styles.recommendationTitle}>In the classroom</Text>
          </View>
          <View style={styles.contentSectionInner}>
            <Text style={styles.content}>
              {parseText(categoryText["Classroom"])}
            </Text>
          </View>
          <View>
            {Object.entries(
              filteredRecommendations
                .filter((rec) => rec.category === "Classroom")
                .reduce(
                  (acc, rec) => {
                    const group = rec.testArea || "Other";
                    acc[group] = acc[group] || [];
                    acc[group].push(rec);
                    return acc;
                  },
                  {} as Record<string, typeof recommendations>
                )
            ).map(([testArea, recs]) => {
              return (
                <View key={testArea}>
                  {testArea && testArea !== "Other" && (
                    <View style={styles.testAreaHeadingRow}>
                      <Text style={styles.testAreaHeading}>
                        {camelCaseToSentence(testArea)}
                      </Text>
                    </View>
                  )}
                  <View style={styles.testAreaDescriptionRow}>
                    {recs.map((recommendation) => {
                      const iconUrl = recIcons?.find(
                        (icon) =>
                          icon.title === recommendation.recommendationType
                      );
                      const hasSecondaryContent =
                        recommendation.secondaryContent.length > 0;

                      const normalizedTestArea = recommendation.testArea || "";
                      const scoreRanking =
                        scoreRankRecs && scoreRankRecs[normalizedTestArea];
                      const mainDescription =
                        recommendation.description?.replace(
                          /{{score}}/g,
                          `<span style="font-weight: 600">${
                            scoreRanking || ""
                          }</span>`
                        );
                      const mainTitle = recommendation.title?.replace(
                        /{{score}}/g,
                        `<span style="font-weight: 600">${
                          scoreRanking || ""
                        }</span>`
                      );
                      const primaryLinkDescription =
                        recommendation.primaryLink?.description?.replace(
                          /{{score}}/g,
                          `<span style="font-weight: 600">${
                            scoreRanking || ""
                          }</span>`
                        );

                      return (
                        <View
                          key={recommendation.id}
                          wrap={false}
                          style={styles.recRow}
                        >
                          <View style={styles.recTextSpacing}>
                            <Html
                              stylesheet={htmlStylesTitle}
                              resetStyles={true}
                            >
                              {replaceFirstName(mainTitle)}
                            </Html>
                            <View style={styles.typeRow}>
                              <View style={styles.typeRowItem}>
                                <View style={styles.recIconXSmall}>
                                  <Image
                                    src={`${iconUrl?.url}?fm=png32&w=300`}
                                  />
                                </View>
                                <View style={styles.typeLabelRow}>
                                  <Text style={styles.typeLabelBold}>
                                    Type:
                                  </Text>
                                  <Text style={styles.typeLabel}>
                                    {camelCaseToSentence(
                                      recommendation.recommendationType
                                    )}
                                  </Text>
                                </View>
                              </View>
                              {recommendation.duration ? (
                                <View style={styles.typeRowItem}>
                                  <View style={styles.recIconXSmall}>
                                    <Image
                                      src={`${durationIcon}?fm=png32&w=300`}
                                    />
                                  </View>
                                  <View style={styles.typeLabelRow}>
                                    <Text style={styles.typeLabelBold}>
                                      Duration:
                                    </Text>
                                    <Text style={styles.typeLabel}>
                                      {recommendation.duration}
                                    </Text>
                                  </View>
                                </View>
                              ) : null}
                            </View>
                            <Html stylesheet={htmlStyles}>
                              {replaceFirstName(mainDescription)}
                            </Html>
                            {recommendation.primaryLink ? (
                              <View>
                                {recommendation.primaryLink.url ? (
                                  <Link
                                    href={recommendation.primaryLink.url}
                                    style={styles.heading6Link}
                                  >
                                    {replaceFirstName(
                                      recommendation.primaryLink.title
                                    )}
                                  </Link>
                                ) : (
                                  <Text style={styles.heading6}>
                                    {replaceFirstName(
                                      recommendation.primaryLink.title
                                    )}
                                  </Text>
                                )}
                                <Html stylesheet={htmlStylesSub}>
                                  {replaceFirstName(primaryLinkDescription)}
                                </Html>
                              </View>
                            ) : null}
                            {hasSecondaryContent
                              ? recommendation.secondaryContent.map(
                                  (content) => {
                                    const secondaryContentDescription =
                                      content?.description?.replace(
                                        /{{score}}/g,
                                        `<span style="font-weight: 600">${
                                          scoreRanking || ""
                                        }</span>`
                                      );
                                    return (
                                      <View key={content.id}>
                                        {content.url ? (
                                          <Link
                                            href={content.url}
                                            style={styles.heading6Link}
                                          >
                                            {replaceFirstName(content.title)}
                                          </Link>
                                        ) : (
                                          <Text style={styles.heading6}>
                                            {replaceFirstName(content.title)}
                                          </Text>
                                        )}
                                        <Html stylesheet={htmlStylesSub}>
                                          {replaceFirstName(
                                            secondaryContentDescription
                                          )}
                                        </Html>
                                      </View>
                                    );
                                  }
                                )
                              : null}
                          </View>
                        </View>
                      );
                    })}
                  </View>
                </View>
              );
            })}
          </View>
          <Text style={styles.footer} fixed>
            {globalPrivacyLabel}
          </Text>
          <Text
            style={styles.pageNum}
            render={({ pageNumber }) => `${pageNumber}`}
            fixed
          />
        </Page>
      )}

      {/* How to support - Extra support */}
      {hasExtraSupport && type === PDFType.Full && (
        <Page size="A4" style={styles.page}>
          <View style={styles.headingRow}>
            <View style={styles.squareIconSmall}>
              <Image src="/images/pdf/support.png" />
            </View>
            <View style={styles.recTextSpacing}>
              <Text style={styles.heading3}>How to support</Text>
            </View>
          </View>
          <Text style={styles.recommendationTitle}>Extra support</Text>
          <View style={styles.contentSectionInner}>
            <Text style={styles.content}>
              {parseText(categoryText["Extra Support"])}
            </Text>
          </View>
          <View>
            {Object.entries(
              filteredRecommendations
                .filter((rec) => rec.category === "ExtraSupport")
                .reduce(
                  (acc, rec) => {
                    const group = rec.testArea || "Other";
                    acc[group] = acc[group] || [];
                    acc[group].push(rec);
                    return acc;
                  },
                  {} as Record<string, typeof recommendations>
                )
            ).map(([testArea, recs]) => {
              return (
                <View key={testArea}>
                  {testArea && testArea !== "Other" && (
                    <View style={styles.testAreaHeadingRow}>
                      <Text style={styles.testAreaHeading}>
                        {camelCaseToSentence(testArea)}
                      </Text>
                    </View>
                  )}
                  <View style={styles.testAreaDescriptionRow}>
                    {recs.map((recommendation) => {
                      const iconUrl = recIcons?.find(
                        (icon) =>
                          icon.title === recommendation.recommendationType
                      );
                      const hasSecondaryContent =
                        recommendation.secondaryContent.length > 0;

                      const normalizedTestArea = recommendation.testArea || "";
                      const scoreRanking =
                        scoreRankRecs && scoreRankRecs[normalizedTestArea];
                      const mainDescription =
                        recommendation.description?.replace(
                          /{{score}}/g,
                          `<span style="font-weight: 600">${
                            scoreRanking || ""
                          }</span>`
                        );
                      const mainTitle = recommendation.title?.replace(
                        /{{score}}/g,
                        `<span style="font-weight: 600">${
                          scoreRanking || ""
                        }</span>`
                      );
                      const primaryLinkDescription =
                        recommendation.primaryLink?.description?.replace(
                          /{{score}}/g,
                          `<span style="font-weight: 600">${
                            scoreRanking || ""
                          }</span>`
                        );

                      return (
                        <View
                          key={recommendation.id}
                          wrap={false}
                          style={styles.recRow}
                        >
                          <View style={styles.recTextSpacing}>
                            <Html
                              stylesheet={htmlStylesTitle}
                              resetStyles={true}
                            >
                              {replaceFirstName(mainTitle)}
                            </Html>
                            <View style={styles.typeRow}>
                              <View style={styles.typeRowItem}>
                                <View style={styles.recIconXSmall}>
                                  <Image
                                    src={`${iconUrl?.url}?fm=png32&w=300`}
                                  />
                                </View>
                                <View style={styles.typeLabelRow}>
                                  <Text style={styles.typeLabelBold}>
                                    Type:
                                  </Text>
                                  <Text style={styles.typeLabel}>
                                    {camelCaseToSentence(
                                      recommendation.recommendationType
                                    )}
                                  </Text>
                                </View>
                              </View>
                              {recommendation.duration ? (
                                <View style={styles.typeRowItem}>
                                  <View style={styles.recIconXSmall}>
                                    <Image
                                      src={`${durationIcon}?fm=png32&w=300`}
                                    />
                                  </View>
                                  <View style={styles.typeLabelRow}>
                                    <Text style={styles.typeLabelBold}>
                                      Duration:
                                    </Text>
                                    <Text style={styles.typeLabel}>
                                      {recommendation.duration}
                                    </Text>
                                  </View>
                                </View>
                              ) : null}
                            </View>
                            <Html stylesheet={htmlStyles}>
                              {replaceFirstName(mainDescription)}
                            </Html>
                            {recommendation.primaryLink ? (
                              <View>
                                {recommendation.primaryLink.url ? (
                                  <Link
                                    href={recommendation.primaryLink.url}
                                    style={styles.heading6Link}
                                  >
                                    {replaceFirstName(
                                      recommendation.primaryLink.title
                                    )}
                                  </Link>
                                ) : (
                                  <Text style={styles.heading6}>
                                    {replaceFirstName(
                                      recommendation.primaryLink.title
                                    )}
                                  </Text>
                                )}
                                <Html stylesheet={htmlStylesSub}>
                                  {replaceFirstName(primaryLinkDescription)}
                                </Html>
                              </View>
                            ) : null}
                            {hasSecondaryContent
                              ? recommendation.secondaryContent.map(
                                  (content) => {
                                    const secondaryContentDescription =
                                      content.description?.replace(
                                        /{{score}}/g,
                                        `<span style="font-weight: 600">${
                                          scoreRanking || ""
                                        }</span>`
                                      );
                                    return (
                                      <View key={content.id}>
                                        {content.url ? (
                                          <Link
                                            href={content.url}
                                            style={styles.heading6Link}
                                          >
                                            {replaceFirstName(content.title)}
                                          </Link>
                                        ) : (
                                          <Text style={styles.heading6}>
                                            {replaceFirstName(content.title)}
                                          </Text>
                                        )}
                                        <Html stylesheet={htmlStylesSub}>
                                          {replaceFirstName(
                                            secondaryContentDescription
                                          )}
                                        </Html>
                                      </View>
                                    );
                                  }
                                )
                              : null}
                          </View>
                        </View>
                      );
                    })}
                  </View>
                </View>
              );
            })}
          </View>
          <Text style={styles.footer} fixed>
            {globalPrivacyLabel}
          </Text>
          <Text
            style={styles.pageNum}
            render={({ pageNumber }) => `${pageNumber}`}
            fixed
          />
        </Page>
      )}

      {/* How to support - How to support */}
      {hasAtHome && type !== PDFType.Teacher && (
        <Page size="A4" style={styles.page}>
          <View style={styles.headingRow}>
            <View style={styles.squareIconSmall}>
              <Image src="/images/pdf/support.png" />
            </View>
            <View style={styles.recTextSpacing}>
              <Text style={styles.heading3}>How to support</Text>
            </View>
          </View>
          <Text style={styles.recommendationTitle}>At home</Text>
          <View style={styles.contentSectionInner}>
            <Text style={styles.content}>
              {parseText(categoryText["At Home"])}
            </Text>
          </View>
          <View>
            {Object.entries(
              filteredRecommendations
                .filter((rec) => rec.category === "AtHome")
                .reduce(
                  (acc, rec) => {
                    const group = rec.testArea || "Other";
                    acc[group] = acc[group] || [];
                    acc[group].push(rec);
                    return acc;
                  },
                  {} as Record<string, typeof recommendations>
                )
            ).map(([testArea, recs]) => {
              return (
                <View key={testArea}>
                  {testArea && testArea !== "Other" && (
                    <View style={styles.testAreaHeadingRow}>
                      <Text style={styles.testAreaHeading}>
                        {camelCaseToSentence(testArea)}
                      </Text>
                    </View>
                  )}
                  <View style={styles.testAreaDescriptionRow}>
                    {recs.map((recommendation) => {
                      const iconUrl = recIcons?.find(
                        (icon) =>
                          icon.title === recommendation.recommendationType
                      );
                      const hasSecondaryContent =
                        recommendation.secondaryContent.length > 0;

                      const normalizedTestArea = recommendation.testArea || "";
                      const scoreRanking =
                        scoreRankRecs && scoreRankRecs[normalizedTestArea];
                      const mainDescription =
                        recommendation.description?.replace(
                          /{{score}}/g,
                          `<span style="font-weight: 600">${
                            scoreRanking || ""
                          }</span>`
                        );
                      const mainTitle = recommendation.title?.replace(
                        /{{score}}/g,
                        `<span style="font-weight: 600">${
                          scoreRanking || ""
                        }</span>`
                      );
                      const primaryLinkDescription =
                        recommendation.primaryLink?.description?.replace(
                          /{{score}}/g,
                          `<span style="font-weight: 600">${
                            scoreRanking || ""
                          }</span>`
                        );

                      return (
                        <View
                          key={recommendation.id}
                          wrap={false}
                          style={styles.recRow}
                        >
                          <View style={styles.recTextSpacing}>
                            <Html
                              stylesheet={htmlStylesTitle}
                              resetStyles={true}
                            >
                              {replaceFirstName(mainTitle)}
                            </Html>
                            <View style={styles.typeRow}>
                              <View style={styles.typeRowItem}>
                                <View style={styles.recIconXSmall}>
                                  <Image
                                    src={`${iconUrl?.url}?fm=png32&w=300`}
                                  />
                                </View>
                                <View style={styles.typeLabelRow}>
                                  <Text style={styles.typeLabelBold}>
                                    Type:
                                  </Text>
                                  <Text style={styles.typeLabel}>
                                    {camelCaseToSentence(
                                      recommendation.recommendationType
                                    )}
                                  </Text>
                                </View>
                              </View>
                              {recommendation.duration ? (
                                <View style={styles.typeRowItem}>
                                  <View style={styles.recIconXSmall}>
                                    <Image
                                      src={`${durationIcon}?fm=png32&w=300`}
                                    />
                                  </View>
                                  <View style={styles.typeLabelRow}>
                                    <Text style={styles.typeLabelBold}>
                                      Duration:
                                    </Text>
                                    <Text style={styles.typeLabel}>
                                      {recommendation.duration}
                                    </Text>
                                  </View>
                                </View>
                              ) : null}
                            </View>
                            <Html stylesheet={htmlStyles}>
                              {replaceFirstName(mainDescription)}
                            </Html>
                            {recommendation.primaryLink ? (
                              <View>
                                {recommendation.primaryLink.url ? (
                                  <Link
                                    href={recommendation.primaryLink.url}
                                    style={styles.heading6Link}
                                  >
                                    {replaceFirstName(
                                      recommendation.primaryLink.title
                                    )}
                                  </Link>
                                ) : (
                                  <Text style={styles.heading6}>
                                    {replaceFirstName(
                                      recommendation.primaryLink.title
                                    )}
                                  </Text>
                                )}
                                <Html stylesheet={htmlStylesSub}>
                                  {replaceFirstName(primaryLinkDescription)}
                                </Html>
                              </View>
                            ) : null}
                            {hasSecondaryContent
                              ? recommendation.secondaryContent.map(
                                  (content) => {
                                    const secondaryContentDescription =
                                      content.description?.replace(
                                        /{{score}}/g,
                                        `<span style="font-weight: 600">${
                                          scoreRanking || ""
                                        }</span>`
                                      );
                                    return (
                                      <View key={content.id}>
                                        {content.url ? (
                                          <Link
                                            href={content.url}
                                            style={styles.heading6Link}
                                          >
                                            {replaceFirstName(content.title)}
                                          </Link>
                                        ) : (
                                          <Text style={styles.heading6}>
                                            {replaceFirstName(content.title)}
                                          </Text>
                                        )}
                                        <Html stylesheet={htmlStylesSub}>
                                          {replaceFirstName(
                                            secondaryContentDescription
                                          )}
                                        </Html>
                                      </View>
                                    );
                                  }
                                )
                              : null}
                          </View>
                        </View>
                      );
                    })}
                  </View>
                </View>
              );
            })}
          </View>
          <Text style={styles.footer} fixed>
            {globalPrivacyLabel}
          </Text>
          <Text
            style={styles.pageNum}
            render={({ pageNumber }) => `${pageNumber}`}
            fixed
          />
        </Page>
      )}
    </Document>
  );
};

Font.register({
  family: "Objectivity",
  fonts: [
    { src: "/assets/Objectivity-Regular.woff" },
    { src: "/assets/Objectivity-Bold.woff", fontWeight: 700 },
    { src: "/assets/Objectivity-RegularSlanted.woff", fontStyle: "italic" },
  ],
});
Font.registerHyphenationCallback((word) => [word]);
