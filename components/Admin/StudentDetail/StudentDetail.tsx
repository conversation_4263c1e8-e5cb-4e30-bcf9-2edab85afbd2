"use client";

import { Box } from "@chakra-ui/react";
import { format, parseISO } from "date-fns";

import { Loader } from "@/components/v2/Loader/Loader";
import { Text } from "@/components/v2/Text/Text";
import { useStudentByCodeQuery } from "@/hooks/queries/useStudentByCodeQuery";
import { theme } from "@/styles/theme";

import { AdminPanel } from "../AdminPanel/AdminPanel";

const LabelValuePair: React.FC<{
  label: string;
  value: string | number | null;
}> = ({ label, value }) => {
  return (
    <Box display="flex" gap={theme.spacing.sm.px} alignItems="center">
      <Text element="h5" variant="sm">
        {label}:
      </Text>
      <Text element="p" variant="lg">
        {value ?? "Don't know"}
      </Text>
    </Box>
  );
};

const Section: React.FC<{
  title: string;
  children: React.ReactNode;
}> = ({ title, children }) => (
  <Box
    mb={theme.spacing.xs.px}
    display="flex"
    flexDir="column"
    gap={theme.spacing.xs.px}
  >
    <Text element="h5" variant="md">
      {title}
    </Text>
    {children}
  </Box>
);

export const StudentDetail: React.FC<{ studentCode: string }> = ({
  studentCode,
}) => {
  const { data: studentData } = useStudentByCodeQuery({ studentCode });

  if (!studentData) return <Loader />;

  return (
    <Box marginTop={theme.spacing.lg.px} marginBottom={theme.spacing.md.px}>
      <AdminPanel>
        <Box
          display="flex"
          justifyContent="space-between"
          marginBottom={theme.spacing.md.px}
        >
          <Text element="h4" variant="xl">
            Student information
          </Text>
        </Box>
        <Box display="flex" flexDir="column" gap={theme.spacing.sm.px}>
          <Section title="Basics">
            <LabelValuePair
              label="First names"
              value={studentData.first_names}
            />
            <LabelValuePair label="Surname" value={studentData.surname} />
            <LabelValuePair
              label="Date of birth"
              value={format(parseISO(studentData.date_of_birth), "dd/MM/yyyy")}
            />
            <LabelValuePair label="Year" value={studentData.year} />
          </Section>

          <Section title="Reported neurodiversity">
            <LabelValuePair
              label="Dyslexia status"
              value={studentData.dyslexia}
            />
            <LabelValuePair label="ADHD status" value={studentData.adhd} />
            <LabelValuePair
              label="Other conditions"
              value={studentData.other_conditions}
            />
          </Section>

          <Section title="Reported performance">
            <LabelValuePair
              label="Reading ability"
              value={studentData.reading_ability}
            />
            <LabelValuePair
              label="Spelling ability"
              value={studentData.spelling_ability}
            />
            <LabelValuePair
              label="Processing speed"
              value={studentData.processing_speed}
            />
            <LabelValuePair
              label="Academic ability"
              value={studentData.academic_ability}
            />
          </Section>
        </Box>
      </AdminPanel>
    </Box>
  );
};
