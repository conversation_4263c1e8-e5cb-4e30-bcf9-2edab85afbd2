"use client";

import { useToast } from "@chakra-ui/react";

import { Modal } from "@/components/v2/Modal/Modal";
import { useAssignTestMutation } from "@/hooks/mutations/useAssignTestMutation";
import { useSchoolsByIdQuery } from "@/hooks/queries/useSchoolsByIdQuery";
import { theme } from "@/styles/theme";

type AssignTestProps = {
  studentIds: number[];
  schoolId: number;
  isModalOpen: boolean;
  setIsModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
  setRowSelection?: React.Dispatch<React.SetStateAction<object>>;
};

export const AssignTest = ({
  isModalOpen,
  setIsModalOpen,
  studentIds,
  schoolId,
  setRowSelection,
}: AssignTestProps) => {
  const toast = useToast();
  const { data: schoolData } = useSchoolsByIdQuery({
    schoolId: schoolId,
  });

  const { mutate: AssignTest } = useAssignTestMutation();

  const additionText =
    studentIds.length === 1 ? "this student" : "the selected students";
  const pluralSuffix = studentIds.length === 1 ? "" : "s";

  const creditsRemaining = schoolData?.unassigned_credits || 0;

  const handleAssignTest = () => {
    AssignTest({ studentIds, schoolId });
    toast({
      title: `Test assigned to ${studentIds.length} student${pluralSuffix}`,
      status: "success",
      duration: 5000,
      isClosable: true,
    });
    setIsModalOpen(false);
    if (setRowSelection) {
      setRowSelection({});
    }
  };

  if (creditsRemaining < studentIds.length) {
    return (
      <Modal
        isOpen={isModalOpen}
        setIsOpen={setIsModalOpen}
        title="Not enough credits"
        description={`You do not have enough credits to complete this action. Either contact us to add more credits, or reduce the amount of students you want to add to the test.`}
        overlayBackground={`rgba(${theme.colors.secondary.purple_01.rgbRaw}, 0.9)`}
        showCloseBtn
        size="xl"
        primaryCtaText="Cancel"
        onPrimaryCtaClick={() => setIsModalOpen(false)}
        primaryCtaVariant="secondary"
      />
    );
  }

  return (
    <Modal
      isOpen={isModalOpen}
      setIsOpen={setIsModalOpen}
      title="Confirm assigning test"
      description={`You are about to assign the test to ${additionText}. This will use ${studentIds.length} credit${pluralSuffix} on completion of the test.`}
      overlayBackground={`rgba(${theme.colors.secondary.purple_01.rgbRaw}, 0.9)`}
      showCloseBtn
      size="xl"
      primaryCtaText="Cancel"
      onPrimaryCtaClick={() => setIsModalOpen(false)}
      primaryCtaVariant="secondary"
      secondaryCtaText="Confirm"
      secondaryCtaVariant="primary"
      onSecondaryCtaClick={handleAssignTest}
      secondaryCtaColor="purple"
    />
  );
};
