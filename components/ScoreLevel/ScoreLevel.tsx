import { Box } from "@chakra-ui/react";
import {
  ArrowDownIcon,
  ArrowRightIcon,
  ArrowUpIcon,
  ArrowUpRightIcon,
} from "@heroicons/react/24/solid";

import { theme } from "@/styles/theme";

import { Text } from "../v2";

type ScoreLevelProps = {
  type: "text" | "arrow";
  level: number;
};

const ICON_SIZE = 16;

const levelStyles = {
  4: {
    textColor: theme.colors.ui.alert_red_01.hex,
    bgColor: theme.colors.ui.alert_red_02.hex,
    text: "Very high",
    icon: <ArrowUpIcon width={ICON_SIZE} height={ICON_SIZE} />,
  },
  3: {
    textColor: theme.colors.ui.alert_orange_01.hex,
    bgColor: theme.colors.ui.alert_orange_03.hex,
    text: "High",
    icon: <ArrowUpRightIcon width={ICON_SIZE} height={ICON_SIZE} />,
  },
  2: {
    textColor: theme.colors.primary.yellow.hex,
    bgColor: theme.colors.tertiary.yellow_03.hex,
    text: "Medium",
    icon: <ArrowRightIcon width={ICON_SIZE} height={ICON_SIZE} />,
  },
  1: {
    textColor: theme.colors.ui.alert_green_01.hex,
    bgColor: theme.colors.ui.alert_green_02.hex,
    text: "Low",
    icon: <ArrowDownIcon width={ICON_SIZE} height={ICON_SIZE} />,
  },
  0: {
    textColor: theme.colors.ui.grey_02.hex,
    bgColor: theme.colors.ui.grey_04.hex,
    text: "-",
    icon: <ArrowDownIcon width={ICON_SIZE} height={ICON_SIZE} />,
  },
};

export const ScoreLevel = ({ type, level }: ScoreLevelProps) => {
  const indexedLevel = level - 1;
  if (![0, 1, 2, 3, 4].includes(indexedLevel)) {
    console.error(
      `Invalid level: ${indexedLevel}. Expected one of 0, 1, 2, 3, or 4.`
    );
    return null;
  }

  const { textColor, bgColor, text, icon } =
    levelStyles[indexedLevel as 1 | 2 | 3 | 4];

  return (
    <>
      {type === "text" ? (
        <Box
          py={theme.spacing.xxxs.px}
          px="10px"
          borderRadius="500px"
          bgColor={bgColor}
          display="inline-flex"
          alignItems="center"
          justifyContent="center"
          minW="90px"
        >
          <Text element="h3" variant="xs" color={textColor}>
            {text}
          </Text>
        </Box>
      ) : (
        <Box
          color={textColor}
          bgColor={bgColor}
          w="24px"
          h="24px"
          display="inline-flex"
          alignItems="center"
          justifyContent="center"
          borderRadius="500px"
        >
          <div className="icon-2">{indexedLevel === 0 ? "-" : icon}</div>
        </Box>
      )}
    </>
  );
};
