import { Button } from "@chakra-ui/react";
import { useState } from "react";

import { theme } from "@/styles/theme";

export function MultipleChoiceButton({ disabled, label, onClickFn }) {
  const [isClicked, setIsClicked] = useState(false);

  if (disabled) {
    return (
      <Button
        disabled={disabled}
        minW="128px"
        minH="80px"
        flex={{ base: 1, md: "unset" }}
        w={{ base: "100%", sm: "unset" }}
        p={{ base: theme.spacing.xs.rem, sm: theme.spacing.sm.rem }}
        variant="secondary"
        color={theme.colors.primary.black.hex}
        border={`2px solid ${theme.colors.ui.grey_03.hex}`}
        background={theme.colors.ui.grey_04.hex}
      >
        {label}
      </Button>
    );
  }

  return (
    <Button
      disabled={disabled}
      minW="128px"
      minH="80px"
      w={{ base: "100%", md: "unset" }}
      flex={{ base: 1, md: "unset" }}
      p={{ base: theme.spacing.xs.rem, sm: theme.spacing.sm.rem }}
      variant="secondary"
      color={theme.colors.primary.black.hex}
      border={
        isClicked
          ? `2px solid ${theme.colors.primary.purple.hex}`
          : `2px solid ${theme.colors.ui.grey_03.hex}`
      }
      css={{
        background: isClicked
          ? theme.colors.secondary.purple_03.hex
          : theme.colors.ui.grey_04.hex,
        ":hover": {
          color: theme.colors.primary.white.hex,
          border: `2px solid ${theme.colors.primary.purple.hex}`,
          background: theme.colors.primary.purple.hex,
        },
      }}
      onClick={() => {
        onClickFn();
        setIsClicked(!isClicked);
      }}
    >
      {label}
    </Button>
  );
}
