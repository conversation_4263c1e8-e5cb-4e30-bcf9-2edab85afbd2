import { Button as ChakraButton, ButtonProps } from "@chakra-ui/react";

import { theme } from "@/styles/theme";

type ButtonVariantType = "tertiary" | "danger" | "secondary" | "primary" | null;
function getBtnVariantStyles(btnVariant?: ButtonVariantType) {
  const defaultStyles = {
    bg: theme.colors.primary.purple.hex,
    outline: false,
    color: theme.colors.primary.white.hex,
  };
  const btnStyles = defaultStyles;
  // Default
  if (!btnVariant) {
    return btnStyles;
  }
  if (btnVariant === "tertiary") {
    return {
      outline: false,
      bg: "none",
      color: theme.colors.ui.grey_01.hex,
    };
  }
  // Secondary
  if (btnVariant === "secondary") {
    return {
      outline: true,
      bg: "none",
      color: theme.colors.primary.black.hex,
    };
  }
  // Danger
  if (btnVariant === "danger") {
    return {
      outline: false,
      bg: theme.colors.ui.alert_red_01.hex,
      color: theme.colors.primary.white.hex,
    };
  }
}

type IButtonProps = ButtonProps & {
  variant?: ButtonVariantType;
  children?: React.ReactNode;
  leftIcon?: JSX.Element;
  rightIcon?: JSX.Element;
  isLoading?: boolean;
};
export function Button(props: IButtonProps) {
  // Return the correct button color
  const btnStyles = getBtnVariantStyles(props?.variant);
  if (props?.disabled) {
    return (
      <ChakraButton
        px={{ base: theme.spacing.xs.rem, md: theme.spacing.sm.rem }}
        py={{ base: theme.spacing.xs.rem, md: theme.spacing.xs.rem }}
        display={{ sm: "flex", md: "inline-flex" }}
        alignItems={"center"}
        css={{
          opacity: 0.5,
          background: theme.colors.ui.grey_03.rgb,
          color: theme.colors.ui.grey_01.rgb,
          borderRadius: theme.border.radius.button.rem,
          textAlign: "center",
          ":hover": {
            cursor: "default",
            background: theme.colors.ui.grey_03.rgb,
            color: theme.colors.ui.grey_01.rgb,
          },
        }}
        disabled
      >
        {props?.children}
      </ChakraButton>
    );
  }
  return (
    <ChakraButton
      px={{ base: theme.spacing.xs.rem, md: theme.spacing.sm.rem }}
      py={{ base: theme.spacing.xs.rem, md: theme.spacing.xs.rem }}
      display={{ sm: "flex", md: "inline" }}
      fontSize={{ sm: "md", md: props?.variant === "tertiary" ? "md" : "lg" }}
      bg={btnStyles?.bg || theme.colors.primary.purple.hex}
      color={btnStyles?.color || theme.colors.primary.white.hex}
      borderRadius={theme.border.radius.button.rem}
      isLoading={props?.isLoading}
      leftIcon={props?.leftIcon}
      rightIcon={props?.rightIcon}
      justifyContent={{ sm: "center" }}
      w={{ sm: "100%", md: "auto" }}
      fontWeight={props?.variant === "tertiary" ? "400" : "500"}
      css={{
        ":disabled": {
          background: "grey",
          opacity: 0.5,
        },
        border: btnStyles?.outline
          ? "2px solid " + theme.colors.primary.black.hex
          : "none",
        ":focus": {
          borderColor: theme.colors.primary.purple.hex,
        },
        ":hover": {
          border: btnStyles?.outline
            ? "2px solid " + theme.colors.ui.grey_01.hex
            : "none",
          background:
            props?.variant === "tertiary"
              ? "none"
              : theme.colors.primary.black.hex,
          color:
            props?.variant === "tertiary"
              ? theme.colors.primary.black.hex
              : theme.colors.primary.white.hex,
        },
        ":active": {
          border: btnStyles?.outline
            ? "2px solid " + theme.colors.ui.grey_01.hex
            : "none",
          background: theme.colors.ui.grey_01.hex,
        },
      }}
      {...props}
    >
      {props?.children}
    </ChakraButton>
  );
}
