import { Box, Button } from "@chakra-ui/react";
import styled from "@emotion/styled";

import { theme } from "@/styles/theme";

const ScaleItemControls = styled.div`
  flex-basis: 100%;
  padding: ${theme.spacing.sm.rem};
`;

export function ContinueButton({ onContinue, isDisabled = false }) {
  return (
    <ScaleItemControls>
      <Button
        mt={theme.spacing.lg.rem}
        css={{
          background: theme.colors.ui.grey_04.hex,
          ":hover": {
            color: theme.colors.primary.white.hex,
            background: theme.colors.primary.black.hex,
          },
        }}
        float={{ base: "unset", md: "right" }}
        variant="ghost"
        w={{ base: "100%", md: "auto" }}
        mr={theme.spacing.sm.rem}
        mb={theme.spacing.sm.rem}
        onClick={onContinue}
        isDisabled={isDisabled}
      >
        Continue
      </Button>
    </ScaleItemControls>
  );
}
