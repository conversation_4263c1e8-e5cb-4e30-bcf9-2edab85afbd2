import { Button as ChakraButton, ButtonProps } from "@chakra-ui/react";
import styled from "@emotion/styled";
import Image from "next/image";

import { theme } from "@/styles/theme";

// Service button (template)
function ServiceButton(props: ButtonProps) {
  return (
    <ChakraButton
      borderRadius={theme.border.radius.button.rem}
      variant={"outline"}
      justifyContent={{ sm: "center", md: "flex-start" }}
      w={{ sm: "100%", md: "auto" }}
      css={{
        ":focus": {
          borderColor: theme.colors.primary.purple.hex,
        },
        ":hover": {
          background: theme.colors.ui.grey_01.hex,
          color: theme.colors.primary.white.hex,
        },
      }}
      {...props}
    >
      {props?.children}
    </ChakraButton>
  );
}

// Provider Button (component)
const ProviderButtonLabel = styled.span`
  text-align: "center";
  font-weight: 500;
  opacity: 0.8;
  flex: 1;
`;
interface IProviderButtonProps {
  icon: string;
  label: string;
}
export function ProviderButton(props: IProviderButtonProps & ButtonProps) {
  return (
    <ServiceButton
      w={"100%"}
      justifyContent={"space-between"}
      leftIcon={
        <Image src={props?.icon} width="14" height="14" alt={props?.label} />
      }
      {...props}
    >
      {props?.isLoading ? (
        <></>
      ) : (
        <ProviderButtonLabel>{props?.label}</ProviderButtonLabel>
      )}
    </ServiceButton>
  );
}
