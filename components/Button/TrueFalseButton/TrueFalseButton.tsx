import { Box } from "@chakra-ui/react";
import styled from "@emotion/styled";
import { useState } from "react";

import { Text } from "@/components";
import { theme } from "@/styles/theme";

type TrueFalseButtonProps = {
  label: string;
  onClick: (args?: any) => any;
  clicked?: boolean;
};
export const TrueFalseButton = ({
  label,
  onClick,
  clicked,
}: TrueFalseButtonProps) => {
  type ButtonStyleProps = {
    bgColor: string;
    clicked?: boolean;
  };
  const ButtonStyles = styled.button<ButtonStyleProps>`
    display: inline;
    height: 128px;
    min-width: 128px;
    border-radius: 10px;
    padding: 2rem 1.5rem;
    border: thin solid lightgrey;
    cursor: pointer;
    background: ${({ bgColor }) => bgColor};
    &:nth-of-type(2) {
      margin-left: 24px;
    }
    @media (max-width: ${theme.breakpoints.portable}) {
      &:nth-of-type(2) {
        margin-left: 8px;
      }
      min-width: 96px;
      height: 96px;
    }
  `;
  const [hoverStyles, setHoverStyles] = useState(false);

  function getButtonColor(clicked, hover) {
    if (!!clicked) {
      return {
        bg: theme.colors.secondary.purple_03.hex,
        text: theme.colors.primary.purple.hex,
      };
    }
    if (!clicked && hover) {
      return {
        bg: theme.colors.primary.purple.hex,
        text: theme.colors.primary.white.hex,
      };
    }
    return {
      bg: theme.colors.primary.white.hex,
      text: theme.colors.primary.purple.hex,
    };
  }

  return (
    <ButtonStyles
      bgColor={getButtonColor(clicked, hoverStyles).bg}
      onClick={onClick}
      clicked={clicked}
      onMouseOver={() => setHoverStyles(true)}
      onMouseOut={() => setHoverStyles(false)}
    >
      <Box justifyContent={"center"}>
        <Text.LargeLabel
          fontWeight={500}
          color={getButtonColor(clicked, hoverStyles).text}
        >
          {label}
        </Text.LargeLabel>
      </Box>
    </ButtonStyles>
  );
};
