import { ChakraProps, Text as ChakraText, TextProps } from "@chakra-ui/react";
import { CSSProperties } from "react";

import { theme } from "@/styles/theme";

const commonStyles = (css?: CSSProperties) => ({
  ":first-of-type": {
    paddingLeft: 0,
    marginLeft: 0,
    ...(css as CSSProperties),
  },
  ...(css as CSSProperties),
});
type TextTypographyProps = ChakraProps &
  TextProps & {
    children?: React.ReactNode;
    regular?: boolean;
  } & {
    fontWeight?: number;
    css?: CSSProperties;
  };

function LargeLabel(props?: TextTypographyProps) {
  return (
    <ChakraText
      color={props?.color || theme.colors.primary.black.hex}
      fontSize={{
        base: theme.text.paragraph_regular.fontSize,
        md: theme.text.paragraph_huge.fontSize,
      }}
      fontWeight={{
        base:
          props?.css?.["fontWeight"] ||
          props?.fontWeight ||
          theme.text.paragraph_huge.fontWeight,
      }}
      lineHeight={{
        base: theme.text.paragraph_huge.lineHeight,
      }}
      as={props?.as || "span"}
      css={commonStyles(props?.css)}
    >
      {props?.children}
    </ChakraText>
  );
}

function P_Hero(props?: TextTypographyProps) {
  return (
    <ChakraText
      color={props?.color || theme.colors.primary.black.hex}
      fontSize={{
        base: theme.text.paragraph_huge.fontSize,
      }}
      fontWeight={{
        base:
          props?.css?.["fontWeight"] ||
          props?.fontWeight ||
          theme.text.paragraph_huge.fontWeight,
      }}
      lineHeight={{
        base: theme.text.paragraph_huge.lineHeight,
      }}
      as={props?.as || "p"}
      css={commonStyles(props?.css)}
    >
      {props?.children}
    </ChakraText>
  );
}
function P_LG(props?: TextTypographyProps) {
  return (
    <ChakraText
      color={props?.color || theme.colors.primary.black.hex}
      fontSize={{
        base: theme.text.paragraph_large.fontSize,
      }}
      fontWeight={{
        base:
          props?.css?.["fontWeight"] ||
          props?.fontWeight ||
          theme.text.paragraph_large.fontWeight,
      }}
      lineHeight={{
        base: theme.text.paragraph_large.lineHeight,
      }}
      as={props?.as || "p"}
      css={commonStyles(props?.css)}
    >
      {props?.children}
    </ChakraText>
  );
}
function P_MD(props?: TextTypographyProps) {
  return (
    <ChakraText
      color={props?.color || theme.colors.primary.black.hex}
      fontSize={{
        base: theme.text.paragraph_regular.fontSize,
      }}
      fontWeight={{
        base:
          props?.css?.["fontWeight"] ||
          props?.fontWeight ||
          theme.text.paragraph_regular.fontWeight,
      }}
      lineHeight={{
        base: theme.text.paragraph_regular.lineHeight,
      }}
      as={props?.as || "p"}
      css={commonStyles(props?.css)}
    >
      {props?.children}
    </ChakraText>
  );
}
function P_SM(props?: TextTypographyProps) {
  return (
    <ChakraText
      color={props?.color || theme.colors.primary.black.hex}
      fontSize={{
        base: theme.text.paragraph_small.fontSize,
      }}
      fontWeight={{
        base:
          props?.css?.["fontWeight"] ||
          props?.fontWeight ||
          theme.text.paragraph_small.fontWeight,
      }}
      lineHeight={{
        base: theme.text.paragraph_small.lineHeight,
      }}
      as={props?.as || "p"}
      css={commonStyles(props?.css)}
    >
      {props?.children}
    </ChakraText>
  );
}
function P_XS(props?: TextTypographyProps) {
  return (
    <ChakraText
      color={props?.color || theme.colors.primary.black.hex}
      fontSize={{
        base: theme.text.paragraph_extra_small.fontSize,
      }}
      fontWeight={{
        base:
          props?.css?.["fontWeight"] ||
          props?.fontWeight ||
          theme.text.paragraph_extra_small.fontWeight,
      }}
      lineHeight={{
        base: theme.text.paragraph_extra_small.lineHeight,
      }}
      as={props?.as || "p"}
      css={commonStyles(props?.css)}
    >
      {props?.children}
    </ChakraText>
  );
}

export const Text = {
  P_Hero,
  P_LG,
  P_MD,
  P_SM,
  P_XS,
  LargeLabel,
};
