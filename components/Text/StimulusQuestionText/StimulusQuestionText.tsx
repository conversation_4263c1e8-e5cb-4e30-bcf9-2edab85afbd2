import styled from "@emotion/styled";

import { theme } from "@/styles/theme";

import { Header } from "../Header/Header";

const StimulusQuestionText_LG = styled(Header.H2_LG)`
  font-weight: 600;
  max-width: 640px;
  z-index: 2;
  text-decoration: underline;
  text-decoration-color: ${theme.colors.ui.grey_04.hex};
  text-underline-offset: 8px;
  letter-spacing: 1.1;
  text-align: center;
  line-height: 1.5;
`;
const StimulusQuestionText_MD = styled(Header.H4_SM)`
  font-weight: 500;
  z-index: 2;
  max-width: 640px;
  text-decoration: underline;
  text-decoration-color: ${theme.colors.ui.grey_04.hex};
  text-underline-offset: 8px;
  letter-spacing: 1.1;
  text-align: center;
  line-height: 1.5;
`;

export const StimulusQuestionText = {
  LG: StimulusQuestionText_LG,
  MD: StimulusQuestionText_MD,
};
