import { Heading, HeadingProps } from "@chakra-ui/react";

import { theme } from "@/styles/theme";

type HeaderTypographyProps = HeadingProps & {
  fontSize?: string | number;
  children?: React.ReactNode;
  regular?: boolean;
};

function Hero_2XL(props?: HeaderTypographyProps) {
  return (
    <Heading
      color={props?.color || theme.colors.primary.black.hex}
      fontSize={{
        base: props?.fontSize || theme.text.hero_bold.mobile.fontSize,
        md: props?.fontSize || theme.text.hero_bold.desktop.fontSize,
      }}
      fontWeight={{
        base:
          props?.css?.["fontWeight"] || props?.regular
            ? 400
            : theme.text.hero_bold.mobile.fontWeight,
        // md:
        //   props?.css?.["fontWeight"] || props?.regular
        //     ? 400
        //     : theme.text.hero_bold.desktop.fontWeight,
      }}
      lineHeight={{
        base: theme.text.hero_bold.mobile.lineHeight,
        md: theme.text.hero_bold.desktop.lineHeight,
      }}
      as={props?.as || "h1"}
      css={props?.css}
    >
      {props?.children}
    </Heading>
  );
}

function H1_XL(props?: HeaderTypographyProps) {
  return (
    <Heading
      css={props?.css}
      color={props?.color || theme.colors.primary.black.hex}
      fontSize={{
        base: props?.fontSize || theme.text.h1.mobile.fontSize,
        md: props?.fontSize || theme.text.h1.desktop.fontSize,
      }}
      fontWeight={{
        base:
          props?.css?.["fontWeight"] || props?.regular
            ? 400
            : theme.text.h1.mobile.fontWeight,
        // md:
        //   props?.css?.["fontWeight"] || props?.regular
        //     ? 400
        //     : theme.text.h1.desktop.fontWeight,
      }}
      lineHeight={{
        base: theme.text.h1.mobile.lineHeight,
        md: theme.text.h1.desktop.lineHeight,
      }}
      as={props?.as || "h1"}
    >
      {props?.children}
    </Heading>
  );
}

function H2_LG(props?: HeaderTypographyProps) {
  return (
    <Heading
      color={props?.color || theme.colors.primary.black.hex}
      fontSize={{
        base: props?.fontSize || theme.text.h2.fontSize,
        md: props?.fontSize || theme.text.h2.fontSize,
      }}
      fontWeight={props?.css?.["fontWeight"] || props?.regular ? 400 : 500}
      lineHeight={{
        base: theme.text.h2.lineHeight,
        md: theme.text.h2.lineHeight,
      }}
      as={props?.as || "h2"}
      css={props?.css}
    >
      {props?.children}
    </Heading>
  );
}
function H3_MD(props?: HeaderTypographyProps) {
  return (
    <Heading
      css={props?.css}
      color={props?.color || theme.colors.primary.black.hex}
      fontSize={{
        base: props?.fontSize || theme.text.h3.fontSize,
        md: props?.fontSize || theme.text.h3.fontSize,
      }}
      fontWeight={{
        base:
          props?.css?.["fontWeight"] || props?.regular
            ? 400
            : theme.text.h3.fontWeight,
        // md:
        //   props?.css?.["fontWeight"] || props?.regular
        //     ? 400
        //     : theme.text.h3.fontWeight,
      }}
      lineHeight={{
        base: theme.text.h3.lineHeight,
        md: theme.text.h3.lineHeight,
      }}
      as={props?.as || "h3"}
    >
      {props?.children}
    </Heading>
  );
}
function H4_SM(props?: HeaderTypographyProps) {
  return (
    <Heading
      css={props?.css}
      color={props?.color || theme.colors.primary.black.hex}
      fontSize={{
        base: props?.fontSize || theme.text.h4.fontSize,
        md: props?.fontSize || theme.text.h4.fontSize,
      }}
      fontWeight={{
        base:
          props?.css?.["fontWeight"] || props?.regular
            ? 400
            : theme.text.h4.fontWeight,
        // md:
        //   props?.css?.["fontWeight"] || props?.regular
        //     ? 400
        //     : theme.text.h4.fontWeight,
      }}
      lineHeight={{
        base: theme.text.h4.lineHeight,
        md: theme.text.h4.lineHeight,
      }}
      as={props?.as || "h4"}
    >
      {props?.children}
    </Heading>
  );
}
function H5_XS(props?: HeaderTypographyProps) {
  return (
    <Heading
      css={props?.css}
      color={props?.color || theme.colors.primary.black.hex}
      fontSize={{
        base: props?.fontSize || theme.text.h5.fontSize,
        md: props?.fontSize || theme.text.h5.fontSize,
      }}
      fontWeight={{
        base:
          props?.css?.["fontWeight"] || props?.regular
            ? 400
            : theme.text.h5.fontWeight,
        // md:
        //   props?.css?.["fontWeight"] || props?.regular
        //     ? 400
        //     : theme.text.h5.fontWeight,
      }}
      lineHeight={{
        base: theme.text.h5.lineHeight,
        md: theme.text.h5.lineHeight,
      }}
      as={props?.as || "h5"}
    >
      {props?.children}
    </Heading>
  );
}

function H6_2XS(props?: HeaderTypographyProps) {
  return (
    <Heading
      css={props?.css}
      color={props?.color || theme.colors.primary.black.hex}
      fontSize={{
        base: props?.fontSize || theme.text.h6.fontSize,
        md: props?.fontSize || theme.text.h6.fontSize,
      }}
      fontWeight={{
        base:
          props?.css?.["fontWeight"] || props?.regular
            ? 500
            : theme.text.h6.fontWeight,
        // md:
        //   props?.css?.["fontWeight"] || props?.regular
        //     ? 500
        //     : theme.text.h6.fontWeight,
      }}
      lineHeight={{
        base: theme.text.h6.lineHeight,
        md: theme.text.h6.lineHeight,
      }}
      as={props?.as || "h6"}
    >
      {props?.children}
    </Heading>
  );
}

export const Header = {
  Hero_2XL,
  H1_XL,
  H2_LG,
  H3_MD,
  H4_SM,
  H5_XS,
  H6_2XS,
};
