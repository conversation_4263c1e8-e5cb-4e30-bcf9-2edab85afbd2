"use client";

import { Box, Flex } from "@chakra-ui/react";
import React from "react";

import { Text } from "@/components/v2/Text/Text";
import { theme } from "@/styles/theme";

import { InstructorIcon } from "../v2/InstructorIcon/InstructorIcon";

type RiskCardProps = {
  level: number;
  heading: string;
  description: string;
};

export const cardStyles = [
  {
    bgColor: theme.colors.ui.grey_04.hex,
    iconColor: theme.colors.ui.grey_03.hex,
  },
  {
    bgColor: theme.colors.ui.alert_green_02.hex,
    iconColor: theme.colors.ui.alert_green_01.hex,
  },
  {
    bgColor: theme.colors.tertiary.yellow_03.hex,
    iconColor: theme.colors.tertiary.yellow_01.hex,
  },
  {
    bgColor: theme.colors.ui.alert_orange_03.hex,
    iconColor: theme.colors.ui.alert_orange_01.hex,
  },
  {
    bgColor: theme.colors.ui.alert_red_02.hex,
    iconColor: theme.colors.ui.alert_red_01.hex,
  },
];

export const RiskCard = ({ level, heading, description }: RiskCardProps) => {
  const arrayPos = level - 1;
  return (
    <Box
      bgColor={cardStyles[arrayPos].bgColor}
      display="flex"
      flexDirection="column"
      padding={theme.spacing.lg.px}
      borderRadius={theme.border.radius.xxl.px}
      gap={theme.spacing.sm.px}
    >
      <Box display="flex" alignItems="center" gap={theme.spacing.sm.px}>
        <Flex alignItems="center" mr={2} gap={2}>
          <InstructorIcon
            bgColor={cardStyles[arrayPos].iconColor}
            width={40}
            height={40}
          />
        </Flex>
        <Text element="h4" variant="md" color={theme.colors.primary.black.hex}>
          {heading}
        </Text>
      </Box>
      <Text element="p" variant="lg" color={theme.colors.primary.black.hex}>
        <div dangerouslySetInnerHTML={{ __html: description }} />
      </Text>
    </Box>
  );
};
