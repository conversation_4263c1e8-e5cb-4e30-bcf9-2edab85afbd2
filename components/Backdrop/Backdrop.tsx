import styled from "@emotion/styled";
import { motion } from "framer-motion";

import { theme } from "@/styles/theme";

type BackdropProps = {
  color?: string;
};
export const BackdropStyles = styled(motion.div)<BackdropProps>`
  display: flex;
  flex-direction: row;
  justify-content: center;
  flex: 1;
  width: 100vw;
  height: 100vh;
  background-color: ${({ color }) =>
    color || theme.colors.secondary.purple_01.hex};
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
`;

export function Backdrop(props) {
  const testId = "backdrop";
  return <BackdropStyles {...props} data-testid={testId} />;
}
