"use client";

import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";
import Image from "next/image";
import { ButtonHTMLAttributes, forwardRef, useState } from "react";

import { cn } from "@/lib/utils";

const audioChipVariants = cva(
  "w-[80px] h-[80px] bg-secondary-purple_04 text-white relative flex items-center justify-center font-semibold transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none",
  {
    variants: {
      variant: {
        selected: "",
        default: "",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
);

export interface AudioChipProps
  extends ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof audioChipVariants> {
  asChild?: boolean;
}

const AudioChip = forwardRef<HTMLButtonElement, AudioChipProps>(
  (
    {
      className,
      variant,
      asChild = false,
      disabled,
      onClick,
      onKeyDown,
      ...props
    },
    ref
  ) => {
    const iconPaths = [
      "/images/audio-chip/audio-chip-1.svg",
      "/images/audio-chip/audio-chip-2.svg",
      "/images/audio-chip/audio-chip-3.svg",
      "/images/audio-chip/audio-chip-4.svg",
      "/images/audio-chip/audio-chip-5.svg",
      "/images/audio-chip/audio-chip-6.svg",
      "/images/audio-chip/audio-chip-7.svg",
      "/images/audio-chip/audio-chip-8.svg",
      "/images/audio-chip/audio-chip-9.svg",
      "/images/audio-chip/audio-chip-10.svg",
    ];

    const [iconSrc] = useState(() => {
      const randomIndex = Math.floor(Math.random() * iconPaths.length);
      return iconPaths[randomIndex];
    });

    const Comp = asChild ? Slot : "button";

    const isShadowActive = !disabled && variant !== "selected";
    const shadowColor = "bg-[#DDCEFF]";
    const shadowTranslate = "translate-y-1";
    const activeTranslate = isShadowActive && "group-active:translate-y-1";
    const defaultTranslate = "translate-y-1";

    const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
      if (!disabled && onClick) {
        onClick(e);
      }
    };

    return (
      <Comp
        data-testid="audio-chip"
        ref={ref}
        className={cn(
          "group relative inline-block cursor-pointer border-none bg-transparent p-0 outline-offset-4 transition-all duration-200",
          disabled && "cursor-not-allowed",
          className
        )}
        disabled={disabled}
        onClick={handleClick}
        tabIndex={0}
        {...props}
      >
        <div className="relative">
          {isShadowActive && (
            <span
              data-testid="audio-button-shadow"
              className={cn(
                "absolute top-0 left-0 w-full h-full rounded-[10px]",
                shadowColor,
                shadowTranslate
              )}
            />
          )}

          <span
            className={cn(
              "relative block rounded-[10px] will-change-transform transform transition-transform duration-500 ease-out select-none overflow-hidden",
              audioChipVariants({ variant }),
              isShadowActive ? activeTranslate : defaultTranslate,
              disabled &&
                cn({
                  "bg-ui-grey_04 text-ui-grey_02": variant === "default",
                })
            )}
          >
            <Image
              src={iconSrc}
              alt="Audio icon"
              width={55}
              height={55}
              className="pointer-events-none"
            />
          </span>
        </div>
      </Comp>
    );
  }
);

AudioChip.displayName = "AudioChip";

export { AudioChip, audioChipVariants };
