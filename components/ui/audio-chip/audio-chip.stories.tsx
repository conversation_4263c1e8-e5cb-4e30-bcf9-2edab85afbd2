import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";

import { AudioChip } from "./audio-chip";

const meta: Meta<typeof AudioChip> = {
  title: "Nova/Audio Chip",
  component: AudioChip,
  argTypes: {
    onClick: { action: "clicked" },
    disabled: { control: "boolean" },
    variant: {
      control: "select",
      options: ["default"],
    },
    asChild: {
      control: "boolean",
    },
  },
};

export default meta;

type Story = StoryObj<typeof AudioChip>;

export const Default: Story = {
  args: {
    variant: "default",
  },
};
