import { action } from "@storybook/addon-actions";
import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import React, { useState } from "react";

import { AudioButton } from "./audio-button";

const meta: Meta<typeof AudioButton> = {
  title: "Nova/Audio Button",
  component: AudioButton,
  argTypes: {
    onClick: { action: "clicked" },
    onKeyDown: { action: "key pressed" },
    keyToPress: {
      control: { type: "text" },
      defaultValue: "Enter",
    },
  },
};

export default meta;

type Story = StoryObj<typeof AudioButton>;

export const AllVariants: Story = {
  args: {
    keyToPress: "Enter",
  },
  render: ({ keyToPress }) => {
    const [playingId, setPlayingId] = useState<string | null>(null);

    const sizes: ("sm" | "md" | "lg")[] = ["sm", "md", "lg"];
    const variants: ("default" | "light")[] = ["default", "light"];

    return (
      <div className="flex flex-col gap-12">
        {variants.map((variant) => (
          <div key={variant} className="flex flex-col gap-6">
            <h3 className="text-lg text-white">{variant}</h3>

            <div className="flex flex-col gap-4">
              <div className="text-sm text-white">Enabled</div>
              <div className="flex gap-6">
                {sizes.map((size) => {
                  const id = `${variant}-${size}`;
                  const isPlaying = playingId === id;

                  return (
                    <div key={id} className="flex flex-col items-center gap-2">
                      <AudioButton
                        variant={variant}
                        size={size}
                        isPlaying={isPlaying}
                        keyToPress={keyToPress}
                        onClick={(e) => {
                          action("clicked")(e);
                          setPlayingId((prev) => (prev === id ? null : id));
                        }}
                        onKeyDown={action("key pressed")}
                      />
                      <span className="text-xs text-white uppercase">
                        {size}
                      </span>
                    </div>
                  );
                })}
              </div>
            </div>

            <div className="flex flex-col gap-4">
              <div className="text-sm text-white">Disabled</div>
              <div className="flex gap-6">
                {sizes.map((size) => (
                  <div
                    key={`disabled-${variant}-${size}`}
                    className="flex flex-col items-center gap-2"
                  >
                    <AudioButton
                      variant={variant}
                      size={size}
                      disabled
                      keyToPress={keyToPress}
                      onClick={action("clicked")}
                      onKeyDown={action("key pressed")}
                    />
                    <span className="text-xs text-white uppercase">{size}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        ))}

        <div className="flex flex-col gap-6">
          <h3 className="text-lg text-white">Play Icon Variant</h3>
          <div className="flex gap-6">
            {sizes.map((size) => (
              <div
                key={`playicon-${size}`}
                className="flex flex-col items-center gap-2"
              >
                <AudioButton
                  playIcon
                  size={size}
                  variant="default"
                  onClick={action("clicked")}
                  onKeyDown={action("key pressed")}
                />
                <span className="text-xs text-white uppercase">{size}</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  },
};
