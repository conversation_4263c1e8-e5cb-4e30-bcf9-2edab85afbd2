/* eslint-disable simple-import-sort/imports */
"use client";

import { PlayIcon } from "@heroicons/react/20/solid";
import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";
import <PERSON><PERSON> from "lottie-react";
import * as React from "react";

import { cn } from "@/lib/utils";

import AudioAnimationBlack from "./audio-button-black.lottie.json";
import AudioAnimationDisabled from "./audio-button-disabled.lottie.json";
import AudioAnimationDefault from "./audio-button.lottie.json";

const audioButtonVariants = cva(
  "relative flex items-center justify-center font-semibold transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none",
  {
    variants: {
      variant: {
        default: "bg-secondary-purple_04 text-white",
        light: "bg-white text-primary-purple",
      },
      size: {
        sm: "h-8 w-8",
        md: "h-10 w-10",
        lg: "h-20 w-20",
      },
      playing: {
        true: "",
        false: "",
      },
      playIcon: {
        true: "",
        false: "",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "lg",
      playing: false,
    },
  }
);

export interface AudioButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof audioButtonVariants> {
  isPlaying?: boolean;
  asChild?: boolean;
  keyToPress?: string;
}

const AudioButton = React.forwardRef<HTMLButtonElement, AudioButtonProps>(
  (
    {
      className,
      variant,
      size,
      isPlaying = false,
      playIcon = false,
      asChild = false,
      disabled,
      keyToPress,
      onClick,
      onKeyDown,
      ...props
    },
    ref
  ) => {
    const Comp = asChild ? Slot : "button";

    const isShadowActive = !disabled;

    const shadowColor =
      variant === "light" ? "bg-[rgba(0,0,0,0.2)]" : "bg-[#DDCEFF]";

    const shadowTranslate = size === "sm" ? "translate-y-0.5" : "translate-y-1";

    const activeTranslate =
      isShadowActive && size === "sm"
        ? "group-active:translate-y-0.5"
        : "group-active:translate-y-1";

    const defaultTranslate =
      size === "sm" ? "translate-y-0.5" : "translate-y-1";

    const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
      if (!disabled && onClick) {
        onClick(e);
      }
    };

    const handleKeyDown = (e: React.KeyboardEvent<HTMLButtonElement>) => {
      if (!disabled && keyToPress && e.key === keyToPress && onKeyDown) {
        onKeyDown(e);
      }
    };

    return (
      <Comp
        data-testid="audio-button"
        ref={ref}
        className={cn(
          "group relative inline-block cursor-pointer border-none bg-transparent p-0 outline-offset-4 transition-all duration-200",
          disabled && "cursor-not-allowed",
          className
        )}
        disabled={disabled}
        onClick={handleClick}
        onKeyDown={handleKeyDown}
        tabIndex={0}
        {...props}
      >
        <div className="relative">
          {isShadowActive && (
            <span
              data-testid="audio-button-shadow"
              className={cn(
                "absolute top-0 left-0 w-full h-full rounded-full",
                shadowColor,
                shadowTranslate
              )}
            />
          )}

          <span
            className={cn(
              "relative block rounded-full will-change-transform transform transition-transform duration-500 ease-out select-none overflow-hidden",
              audioButtonVariants({ variant, size, playing: isPlaying }),
              isShadowActive ? activeTranslate : defaultTranslate,
              disabled &&
                cn({
                  "bg-ui-grey_04 text-ui-grey_02": variant === "default",
                })
            )}
          >
            {playIcon ? (
              <PlayIcon
                width={19}
                height={19}
                className={cn("text-primary-purple", {
                  "text-ui-grey_02": size === "sm" || disabled,
                })}
              />
            ) : (
              <Lottie
                key={disabled ? "disabled" : isPlaying ? "playing" : "stopped"}
                animationData={
                  disabled
                    ? AudioAnimationDisabled
                    : variant === "default"
                      ? AudioAnimationDefault
                      : AudioAnimationBlack
                }
                loop
                autoplay={!disabled && isPlaying}
                className="w-full h-full"
              />
            )}
          </span>
        </div>
      </Comp>
    );
  }
);

AudioButton.displayName = "AudioButton";

export { AudioButton, audioButtonVariants };
