"use client";

import * as TooltipPrimitive from "@radix-ui/react-tooltip";
import { cva } from "class-variance-authority";
import * as React from "react";

import { cn } from "@/lib/utils";

const tooltipContentVariants = cva(
  "z-50 overflow-hidden rounded-md bg-tertiary-yellow_02 px-3 py-1.5 text-black font-semibold animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-tooltip-content-transform-origin]"
);

const TooltipProvider = TooltipPrimitive.Provider;

const Tooltip = TooltipPrimitive.Root;

const TooltipTrigger = TooltipPrimitive.Trigger;

const TooltipContent = React.forwardRef<
  React.ElementRef<typeof TooltipPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Content> & {
    playSound?: boolean;
    open?: boolean;
  }
>(({ className, sideOffset = 4, playSound = false, ...props }, ref) => {
  React.useEffect(() => {
    if (playSound && props["data-state"] === "delayed-open") {
      const audio = new Audio(
        "https://www.datocms-assets.com/103689/**********-pop-audio.mp3"
      );
      audio.play().catch(() => {});
    }
  }, [playSound, props["data-state"]]);

  return (
    <TooltipPrimitive.Portal>
      <TooltipPrimitive.Content
        ref={ref}
        sideOffset={sideOffset}
        className={cn(tooltipContentVariants(), className)}
        {...props}
      >
        {props.children}
        <TooltipPrimitive.Arrow className="fill-tertiary-yellow_02" />
      </TooltipPrimitive.Content>
    </TooltipPrimitive.Portal>
  );
});
TooltipContent.displayName = TooltipPrimitive.Content.displayName;

interface TooltipWithSoundProps {
  children: React.ReactNode;
  content: React.ReactNode;
  playSound?: boolean;
  open?: boolean;
  side?: React.ComponentProps<typeof TooltipPrimitive.Content>["side"];
}

const TooltipWithSound: React.FC<TooltipWithSoundProps> = ({
  children,
  content,
  playSound = false,
  open,
  side,
}) => {
  const [internalOpen, setInternalOpen] = React.useState(false);
  const isControlled = open !== undefined;
  const isOpen = isControlled ? open : internalOpen;

  React.useEffect(() => {
    if (isOpen && playSound) {
      const audio = new Audio(
        "https://www.datocms-assets.com/103689/**********-pop-audio.mp3"
      );
      audio.play().catch(() => {});
    }
  }, [isOpen, playSound]);

  return (
    <Tooltip open={isOpen} onOpenChange={setInternalOpen}>
      <TooltipTrigger asChild>{children}</TooltipTrigger>
      <TooltipContent side={side}>{content}</TooltipContent>
    </Tooltip>
  );
};

export {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
  TooltipWithSound,
};
