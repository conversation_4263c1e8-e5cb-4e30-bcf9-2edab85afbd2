import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import React from "react";

import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
  TooltipWithSound,
} from "./tooltip";

const meta: Meta = {
  title: "Nova/Tooltip",
  component: Tooltip,
};

export default meta;

type Story = StoryObj;

export const Default: Story = {
  render: () => (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <button className="px-4 py-2 bg-blue-500 text-white rounded">
            Hover me
          </button>
        </TooltipTrigger>
        <TooltipContent>Tooltip text</TooltipContent>
      </Tooltip>
    </TooltipProvider>
  ),
};

export const WithSound: Story = {
  render: () => (
    <TooltipProvider>
      <TooltipWithSound content="👆 Click to listen to the sound" playSound>
        <button className="px-4 py-2 bg-green-600 text-white rounded">
          Hover for sound
        </button>
      </TooltipWithSound>
    </TooltipProvider>
  ),
};
