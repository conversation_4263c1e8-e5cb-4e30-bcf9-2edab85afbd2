import { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";

import { SpeechBubble, SpeechBubbleProps } from "./speech-bubble";

const meta: Meta<typeof SpeechBubble> = {
  title: "Nova/Speech Bubble",
  component: SpeechBubble,
  argTypes: {
    variant: {
      control: "select",
      options: ["default", "error", "success"],
      description: "Changes the text color based on message type.",
    },
    width: {
      control: "select",
      options: ["small", "medium", "large"],
      description: "Adjusts the maximum width of the speech bubble.",
    },
    children: {
      control: "text",
      description: "Content inside the speech bubble.",
    },
  },
};

export default meta;
type Story = StoryObj<SpeechBubbleProps>;

export const Default: Story = {
  args: {
    children: "This is a speech bubble!",
    variant: "default",
    width: "medium",
  },
};

export const Error: Story = {
  args: {
    children: "Something went wrong!",
    variant: "error",
    width: "medium",
  },
};

export const Success: Story = {
  args: {
    children: "Great job!",
    variant: "success",
    width: "medium",
  },
};
