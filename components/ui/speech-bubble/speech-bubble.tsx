import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";
import { forwardRef } from "react";

import { cn } from "@/lib/utils";

const speechBubbleVariants = cva(
  "relative inline-block rounded-[32px] p-6 shadow-md bg-white w-full",
  {
    variants: {
      variant: {
        default: "text-black",
        error: "text-ui-alert_red_01",
        success: "text-ui-alert_green_01",
      },
      width: {
        small: "max-w-[150px]",
        medium: "max-w-[288px]",
        large: "max-w-[350px]",
      },
    },
    defaultVariants: {
      variant: "default",
      width: "medium",
    },
  }
);

export interface SpeechBubbleProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof speechBubbleVariants> {
  asChild?: boolean;
}

const SpeechBubble = forwardRef<HTMLDivElement, SpeechBubbleProps>(
  ({ className, variant, width, asChild = false, children, ...props }, ref) => {
    const Comp = asChild ? Slot : "div";
    return (
      <Comp
        className={cn(speechBubbleVariants({ variant, width }), className)}
        ref={ref}
        {...props}
      >
        <span
          data-testid="speech-bubble-contents"
          className="relative block text-[18px] font-semibold z-10"
        >
          {children}
        </span>
        <span
          data-testid="speech-bubble-tail"
          className="absolute left-1 bottom-[-20px] w-[77px] h-[53px]"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 77 53"
          >
            <g clipPath="url(#a)">
              <path
                fill="#fff"
                d="m30.563 52.113 19.991-13.81 22.474-15.078c1.547-1.038 1.554-3.311.014-4.36L49.898 3.126a2.63 2.63 0 0 0-3.892 1.128l-19.35 44.65c-1.052 2.426 1.732 4.713 3.907 3.21Z"
              />
            </g>
            <defs>
              <clipPath id="a">
                <path fill="#fff" d="M0 0h77v53H0z" />
              </clipPath>
            </defs>
          </svg>
        </span>
      </Comp>
    );
  }
);
SpeechBubble.displayName = "SpeechBubble";

export { SpeechBubble, speechBubbleVariants };
