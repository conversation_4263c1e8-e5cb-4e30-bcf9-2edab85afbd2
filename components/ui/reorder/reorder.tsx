import { Reorder as <PERSON>amer<PERSON><PERSON><PERSON> } from "framer-motion";

interface ReorderProps {
  items: string[];
  onReorder: (newOrder: string[]) => void;
}

export default function Reorder({ items, onReorder }: ReorderProps) {
  return (
    <FramerReorder.Group
      axis="x"
      values={items}
      onReorder={onReorder}
      className="flex gap-4"
    >
      {items.map((item) => (
        <div key={item} className="relative flex flex-col items-center">
          <div
            className="w-full h-16 rounded-lg transition-all duration-300 ease-in-out bg-secondary-purple_05/30"
            style={{
              position: "absolute",
              top: 0,
              zIndex: -1,
              pointerEvents: "none",
            }}
          />

          <FramerReorder.Item
            value={item}
            className="relative flex items-center justify-center bg-white !text-black px-6 py-4 rounded-lg font-semibold text-2xl shadow-md cursor-grab active:cursor-grabbing"
            style={{
              zIndex: 5,
              minWidth: "max-content",
              backgroundColor: "rgba(255, 255, 255, 1)",
            }}
            whileDrag={{
              scale: 1.2,
              rotate: Math.random() * 10 - 3,
              y: -13,
              boxShadow: "0px 15px 30px rgba(0, 0, 0, 0.4)",
              zIndex: 50,
            }}
            transition={{ type: "spring", stiffness: 500, damping: 30 }}
          >
            {item}
          </FramerReorder.Item>
        </div>
      ))}
    </FramerReorder.Group>
  );
}
