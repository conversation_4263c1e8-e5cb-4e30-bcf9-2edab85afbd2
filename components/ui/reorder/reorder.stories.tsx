import { <PERSON>a, StoryObj } from "@storybook/react";
import { useEffect, useState } from "react";

import Reorder from "./reorder";

const meta: Meta<typeof Reorder> = {
  title: "Nova/Reorder",
  component: Reorder,
  args: {
    items: ["gy", "pal", "thro", "po", "e", "o", "an", "lo"],
  },
};

export default meta;
type Story = StoryObj<typeof Reorder>;

export const Default: Story = {
  render: (args) => {
    const [orderedItems, setOrderedItems] = useState(args.items);

    useEffect(() => {
      setOrderedItems(args.items);
    }, [args.items]);

    return (
      <div className="flex justify-center py-16 min-h-screen">
        <Reorder items={orderedItems} onReorder={setOrderedItems} />
      </div>
    );
  },
};
