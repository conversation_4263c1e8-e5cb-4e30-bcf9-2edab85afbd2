"use client";

import Image from "next/image";
import React, { useEffect, useRef, useState } from "react";

import { NovaScaleItemRecord } from "@/types/graphql/graphql";

import { AudioButton } from "../audio-button/audio-button";

interface ImageStimulusProps {
  scaleItem: NovaScaleItemRecord | null;
}

export const ImageStimulus: React.FC<ImageStimulusProps> = ({ scaleItem }) => {
  const imageUrl = scaleItem?.stimulusMedia?.url ?? null;

  if (!scaleItem || !imageUrl) return null;

  return (
    <div className="flex flex-col gap-8 justify-center items-center">
      <Image src={imageUrl} width={300} height={240} alt="" />
      {scaleItem.stimulusText && (
        <div className="max-w-[800px] text-center font-semibold text-2xl">
          {scaleItem.stimulusText}
        </div>
      )}
    </div>
  );
};
