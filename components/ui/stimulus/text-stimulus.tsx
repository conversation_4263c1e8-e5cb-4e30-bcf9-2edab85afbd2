"use client";

import React from "react";

import { cn } from "@/lib/utils";
import { NovaScaleItemRecord } from "@/types/graphql/graphql";

interface TextStimulusProps {
  scaleItem: NovaScaleItemRecord | null;
}

export const TextStimulus: React.FC<TextStimulusProps> = ({ scaleItem }) => {
  if (!scaleItem) return null;

  return (
    <div className="max-w-[800px] min-h-[240px] flex flex-col justify-end text-center">
      <div
        className={cn(
          "font-semibold",
          scaleItem.stimulusType === "text_large" && "text-5xl",
          scaleItem.stimulusType === "text_small" && "text-2xl"
        )}
      >
        {scaleItem.stimulusText}
      </div>
    </div>
  );
};
