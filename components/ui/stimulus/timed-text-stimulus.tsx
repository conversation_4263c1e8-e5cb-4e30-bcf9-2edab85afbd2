"use client";

import React, { useEffect, useState } from "react";

import { Timer } from "@/components/ui/timer/timer";
import { cn } from "@/lib/utils";
import { useResponseDisabledStore } from "@/stores/use-response-disabled-store";
import { useSampleScaleItemStore } from "@/stores/use-sample-scale-item-store";
import { NovaScaleItemRecord } from "@/types/graphql/graphql";

interface TimedTextStimulusProps {
  scaleItem: NovaScaleItemRecord | null;
  totalTime?: number;
  onComplete?: () => void;
  showFeedback?: boolean;
}

export const TimedTextStimulus: React.FC<TimedTextStimulusProps> = ({
  scaleItem,
  totalTime = 2000,
  onComplete,
}) => {
  const [remainingTime, setRemainingTime] = useState(totalTime);
  const [isBlurred, setIsBlurred] = useState(false);
  const [timerRunning, setTimerRunning] = useState(false);

  const setResponseDisabled = useResponseDisabledStore(
    (s) => s.setResponseDisabled
  );

  const shouldPlayStimulusForId = useSampleScaleItemStore(
    (s) => s.shouldPlayStimulusForId
  );
  const resetStimulusTrigger = useSampleScaleItemStore((s) => s.reset);

  const isTutorial = !!scaleItem?.tutorial;
  const scaleItemId = scaleItem?.id ?? null;

  useEffect(() => {
    if (!scaleItem) return;
    if (shouldPlayStimulusForId !== scaleItem.id) return;

    setTimerRunning(false);
    setRemainingTime(totalTime);
    setIsBlurred(false);
  }, [scaleItem?.id, totalTime, shouldPlayStimulusForId]);

  useEffect(() => {
    if (!scaleItem || isTutorial) return;

    const timeout = setTimeout(() => {
      setTimerRunning(true);
      setResponseDisabled(true);
    }, 300);

    return () => clearTimeout(timeout);
  }, [scaleItem?.id, isTutorial, setResponseDisabled]);

  useEffect(() => {
    if (!scaleItemId || !isTutorial) return;
    if (shouldPlayStimulusForId !== scaleItemId) return;
    if (timerRunning) return;

    setTimerRunning(true);
    setResponseDisabled(true);
    resetStimulusTrigger();
  }, [
    shouldPlayStimulusForId,
    scaleItemId,
    isTutorial,
    timerRunning,
    resetStimulusTrigger,
    setResponseDisabled,
  ]);

  useEffect(() => {
    if (!timerRunning) {
      return;
    }

    setResponseDisabled(true);

    if (remainingTime <= 0) {
      setIsBlurred(true);
      setResponseDisabled(false);
      onComplete?.();
      return;
    }

    const interval = setInterval(() => {
      setRemainingTime((prev) => {
        const newTime = Math.max(prev - 100, 0);
        return newTime;
      });
    }, 100);

    return () => clearInterval(interval);
  }, [remainingTime, timerRunning, onComplete, setResponseDisabled]);

  if (!scaleItem) return null;

  return (
    <div className="flex flex-col items-center justify-end gap-6 min-h-[264px]">
      <div
        className={cn(
          "flex flex-col justify-end text-center transition-all duration-700",
          isBlurred ? "blur-[30px]" : "blur-0"
        )}
      >
        <div className="text-7xl font-semibold">{scaleItem.stimulusText}</div>
      </div>

      <Timer
        key={`${scaleItem?.id}-${shouldPlayStimulusForId}`}
        totalTime={totalTime}
        remainingTime={timerRunning ? remainingTime : totalTime}
        variant="small"
      />
    </div>
  );
};
