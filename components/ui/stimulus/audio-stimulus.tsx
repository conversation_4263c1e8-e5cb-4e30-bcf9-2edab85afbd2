"use client";

import React, { useEffect, useRef, useState } from "react";

import { useFeedbackAudioStore } from "@/stores/use-feedback-audio-store";
import { useResponseDisabledStore } from "@/stores/use-response-disabled-store";
import { useSampleScaleItemStore } from "@/stores/use-sample-scale-item-store";
import { NovaScaleItemRecord } from "@/types/graphql/graphql";

import { AudioButton } from "../audio-button/audio-button";

interface AudioStimulusProps {
  scaleItem: NovaScaleItemRecord | null;
}

export const AudioStimulus: React.FC<AudioStimulusProps> = ({ scaleItem }) => {
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [playCount, setPlayCount] = useState(0);
  const [hasAutoplayed, setHasAutoplayed] = useState(false);

  const setResponseDisabled = useResponseDisabledStore(
    (s) => s.setResponseDisabled
  );

  const shouldPlayStimulusForId = useSampleScaleItemStore(
    (s) => s.shouldPlayStimulusForId
  );
  const resetStimulusTrigger = useSampleScaleItemStore((s) => s.reset);

  const audioUrl = scaleItem?.stimulusMedia?.url ?? null;
  const maxPlays = scaleItem?.audioPlays ?? 0;
  const scaleItemId = scaleItem?.id ?? null;
  const isPracticeItem = !!scaleItem?.tutorial || !!scaleItem?.practiceItem;
  const hasRemainingPlays = playCount < maxPlays;
  const disableWhilePlaying = scaleItem?.disabledWhileAutoplay;
  const isDisabled = !audioUrl || (!hasRemainingPlays && !isPlaying);

  const handlePlayEvent = () => {
    setIsPlaying(true);
    if (disableWhilePlaying) {
      setResponseDisabled(true);
    } else {
      setResponseDisabled(false);
    }
  };
  const handlePauseEvent = () => setIsPlaying(false);
  const handleEndedEvent = () => {
    setIsPlaying(false);
    if (!hasAutoplayed) {
      setHasAutoplayed(true);
      setResponseDisabled(false);
    }
  };

  useEffect(() => {
    if (!audioUrl) return;

    const audio = new Audio(audioUrl);
    audio.preload = "auto";
    audioRef.current = audio;

    setHasAutoplayed(false);
    setPlayCount(0);

    audio.addEventListener("play", handlePlayEvent);
    audio.addEventListener("pause", handlePauseEvent);
    audio.addEventListener("ended", handleEndedEvent);

    return () => {
      audio.pause();
      audio.removeEventListener("play", handlePlayEvent);
      audio.removeEventListener("pause", handlePauseEvent);
      audio.removeEventListener("ended", handleEndedEvent);
    };
  }, [audioUrl]);

  useEffect(() => {
    const shouldAutoplay =
      !isPracticeItem || (!!scaleItem?.practiceItem && !scaleItem?.tutorial);

    if (
      !audioRef.current ||
      !audioUrl ||
      hasAutoplayed ||
      maxPlays < 1 ||
      !shouldAutoplay
    )
      return;

    const timeout = setTimeout(() => {
      const audio = audioRef.current!;
      audio.currentTime = 0;
      audio.load();
      audio.play().then(() => {
        setHasAutoplayed(true);
        if (!scaleItem?.tutorial) {
          setPlayCount(1);
        }
      });
    }, 300);

    return () => clearTimeout(timeout);
  }, [audioUrl, maxPlays, hasAutoplayed, isPracticeItem, scaleItem]);

  useEffect(() => {
    if (
      !audioRef.current ||
      !scaleItemId ||
      shouldPlayStimulusForId !== scaleItemId ||
      isPlaying ||
      !hasRemainingPlays
    )
      return;

    audioRef.current.currentTime = 0;
    useFeedbackAudioStore.getState().stop();
    audioRef.current.play().then(() => {
      setPlayCount((prev) => prev + 1);
    });
    resetStimulusTrigger();
  }, [
    shouldPlayStimulusForId,
    scaleItemId,
    resetStimulusTrigger,
    isPlaying,
    hasRemainingPlays,
    isPracticeItem,
  ]);

  const handlePlay = () => {
    if (!audioRef.current || isPlaying || !hasRemainingPlays) return;

    audioRef.current.currentTime = 0;
    audioRef.current.play().catch(console.warn);
    setPlayCount((prev) => prev + 1);
  };

  const isFeedbackPlaying = useFeedbackAudioStore((s) => s.isFeedbackPlaying);
  useEffect(() => {
    if (isFeedbackPlaying && audioRef.current) {
      audioRef.current.pause();
      audioRef.current.currentTime = 0;
      setIsPlaying(false);
    }
  }, [isFeedbackPlaying]);

  useEffect(() => {
    if (shouldPlayStimulusForId === scaleItemId) {
      setPlayCount(0);
    }
  }, [shouldPlayStimulusForId, scaleItemId]);

  if (!scaleItem || !audioUrl) return null;

  return (
    <div className="flex items-center gap-6">
      <AudioButton
        isPlaying={isPlaying}
        onClick={handlePlay}
        disabled={isDisabled}
        size="lg"
        variant="default"
      />
      {scaleItem.stimulusText && (
        <div className="text-6xl font-semibold translate-y-2">
          {scaleItem.stimulusText}
        </div>
      )}
    </div>
  );
};
