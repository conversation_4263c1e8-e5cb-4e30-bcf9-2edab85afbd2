import { Reorder } from "framer-motion";
import { useEffect, useMemo, useRef, useState } from "react";

import { cn } from "@/lib/utils";
import type { NovaScaleItemRecord } from "@/types/graphql/graphql";

import { AudioButton } from "../audio-button/audio-button";
import { AudioChip } from "../audio-chip/audio-chip";
import { TooltipProvider, TooltipWithSound } from "../tooltip/tooltip";

interface DragAudioV2Props {
  scaleItem: NovaScaleItemRecord;
  onReorder?: (order: string[]) => void;
  disabled?: boolean;
  selected?: string[];
  showFeedback?: boolean;
  isCorrect?: boolean | null;
}

export const DragAudioV2: React.FC<DragAudioV2Props> = ({
  scaleItem,
  onReorder,
  disabled,
  selected,
  showFeedback,
  isCorrect,
}) => {
  const responseMedia = useMemo(
    () => scaleItem.responseMedia || [],
    [scaleItem]
  );

  const initialItems = useMemo(() => {
    const filenames = responseMedia.map((c) => c.filename);
    if (!selected || selected.length === 0) return filenames;

    const selectedFilenames = selected
      .map((id) => filenames.find((f) => f.replace(/\.mp3$/, "") === id))
      .filter((f): f is string => Boolean(f));
    return selectedFilenames.length > 0 ? selectedFilenames : filenames;
  }, []);

  const correctAnswers = useMemo(
    () => scaleItem.correctAnswer?.split(",").map((s) => s.trim()) ?? [],
    [scaleItem.correctAnswer]
  );

  const [items, setItems] = useState<string[]>(initialItems);

  const [currentlyPlayingId, setCurrentlyPlayingId] = useState<string | null>(
    null
  );

  const [hasInteracted, setHasInteracted] = useState(false);
  const [showTooltip, setShowTooltip] = useState(false);

  useEffect(() => {
    const showTutorialFeedback = disabled === true;

    if (
      !scaleItem.practiceItem ||
      showTutorialFeedback ||
      hasInteracted ||
      disabled
    )
      return;

    const timer = setTimeout(() => {
      setShowTooltip(true);
    }, 5000);

    return () => clearTimeout(timer);
  }, [scaleItem.practiceItem, disabled, hasInteracted]);

  const audioContext = useRef<AudioContext | null>(null);
  const audioBuffers = useRef<Record<string, AudioBuffer>>({});

  const playbackTriggeredRef = useRef(false);
  const wasDraggingRef = useRef(false);

  useEffect(() => {
    audioContext.current = new (window.AudioContext ||
      window.webkitAudioContext)();
    loadAudio();
    return () => {
      audioContext.current?.close();
    };
  }, [responseMedia]);

  const loadAudio = async () => {
    if (!audioContext.current) return;

    for (const clip of responseMedia) {
      const response = await fetch(clip.url);
      const arrayBuffer = await response.arrayBuffer();
      const audioBuffer =
        await audioContext.current.decodeAudioData(arrayBuffer);
      audioBuffers.current[clip.url] = audioBuffer;
    }
  };

  const handleClick = async (filename: string) => {
    if (disabled || !audioContext.current) return;
    await audioContext.current.resume();

    const clip = responseMedia.find((c) => c.filename === filename);
    if (!clip) return;

    const buffer = audioBuffers.current[clip.url];
    if (!buffer) return;

    const source = audioContext.current.createBufferSource();
    source.buffer = buffer;
    source.connect(audioContext.current.destination);
    playbackTriggeredRef.current = true;
    setCurrentlyPlayingId(filename);
    source.start();
    setTimeout(() => {
      if (playbackTriggeredRef.current) {
        setCurrentlyPlayingId(null);
      }
      playbackTriggeredRef.current = false;
    }, buffer.duration * 1000);
  };

  return (
    <>
      <div className="mb-6 flex items-center gap-8 -ml-[72px]">
        <AudioButton
          onClick={async () => {
            if (disabled || !audioContext.current) return;
            await audioContext.current.resume();

            const ctx = audioContext.current;
            const now = ctx.currentTime;
            let offset = 0;

            for (const filename of items) {
              const clip = responseMedia.find((c) => c.filename === filename);
              if (!clip) continue;

              const buffer = audioBuffers.current[clip.url];
              if (buffer) {
                const source = ctx.createBufferSource();
                source.buffer = buffer;
                source.connect(ctx.destination);

                source.start(now + offset);

                setTimeout(() => {
                  if (!disabled) setCurrentlyPlayingId(filename);
                }, offset * 1000);

                setTimeout(
                  () => {
                    if (!disabled) setCurrentlyPlayingId(null);
                  },
                  (offset + buffer.duration) * 1000
                );

                offset += buffer.duration;
              }
            }
          }}
          playIcon
          size="md"
          disabled={disabled}
        />
        <TooltipProvider>
          <TooltipWithSound
            open={showTooltip}
            playSound={showTooltip}
            side="bottom"
            content={
              <div className="whitespace-pre-line text-center">
                👆 Click to listen to the sound{"\n"}✋ Drag to change the order
              </div>
            }
          >
            <div
              className={cn(
                "p-6 bg-white rounded-xl",
                isCorrect === true &&
                  scaleItem.feedback &&
                  "ring-8 ring-ui-alert_green_01",
                isCorrect === false &&
                  scaleItem.feedback &&
                  "ring-8 ring-ui-alert_red_01"
              )}
            >
              <Reorder.Group
                axis="x"
                values={items}
                onReorder={(newOrder) => {
                  setHasInteracted(true);
                  setShowTooltip(false);
                  wasDraggingRef.current = true;
                  playbackTriggeredRef.current = false;
                  setItems(newOrder);
                  const selectedIndexes = newOrder.map((filename) =>
                    (
                      responseMedia.findIndex(
                        (clip) => clip.filename === filename
                      ) + 1
                    ).toString()
                  );
                  onReorder?.(selectedIndexes);
                }}
                className="flex gap-4"
              >
                {items.map((filename, index) => {
                  const clip = responseMedia.find(
                    (c) => c.filename === filename
                  );
                  if (!clip) return null;
                  const chipIsPlaying = currentlyPlayingId === filename;

                  const isSelected =
                    filename.replace(/\.mp3$/, "") === correctAnswers[index];
                  let variant: "default" | "selected" | undefined = "default";

                  if (showFeedback && isSelected) {
                    variant = "selected";
                  }

                  return (
                    <div
                      key={filename}
                      className="relative flex flex-col items-center"
                    >
                      <div
                        className="w-full h-20 rounded-lg transition-all duration-300 ease-in-out bg-secondary-purple_05/30"
                        style={{
                          position: "absolute",
                          top: 0,
                          zIndex: -1,
                          pointerEvents: "none",
                        }}
                      />

                      <Reorder.Item
                        value={filename}
                        className="relative flex items-center justify-center !text-black rounded-lg shadow-md cursor-grab active:cursor-grabbing"
                        style={{
                          zIndex: 5,
                          minWidth: "max-content",
                        }}
                        whileDrag={{
                          scale: 1.2,
                          rotate: Math.random() * 10 - 3,
                          y: -13,
                          boxShadow: "0px 15px 30px rgba(0, 0, 0, 0.4)",
                          zIndex: 50,
                        }}
                        transition={{
                          type: "spring",
                          stiffness: 500,
                          damping: 30,
                        }}
                      >
                        <button
                          onClick={() => {
                            setHasInteracted(true);
                            setShowTooltip(false);
                            if (wasDraggingRef.current) {
                              wasDraggingRef.current = false;
                              return;
                            }
                            handleClick(filename);
                          }}
                        >
                          <AudioChip
                            disabled={disabled || chipIsPlaying}
                            variant={variant}
                          >
                            {filename.replace(/\.mp3$/, "")}
                          </AudioChip>
                        </button>
                      </Reorder.Item>
                    </div>
                  );
                })}
              </Reorder.Group>
            </div>
          </TooltipWithSound>
        </TooltipProvider>
      </div>
    </>
  );
};
