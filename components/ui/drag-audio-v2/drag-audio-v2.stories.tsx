import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import React from "react";

import type { <PERSON><PERSON>ield, NovaScaleItemRecord } from "@/types/graphql/graphql";

import { DragAudioV2 } from "./drag-audio-v2";

const meta: Meta<typeof DragAudioV2> = {
  title: "Nova/Drag Audio V2",
  component: DragAudioV2,
  parameters: {
    layout: "centered",
  },
};

export default meta;

type Story = StoryObj<typeof DragAudioV2>;

const mockScaleItem: NovaScaleItemRecord = {
  __typename: "NovaScaleItemRecord",
  id: "demo",
  difficulty: "easy",
  practiceItem: true,
  feedback: true,
  skippable: false,
  tutorial: true,
  stimulusType: "timed_text",
  stimulusText: "chihuahua",
  stimulusMedia: null,
  audioPlays: 2,
  responseType: "drag_audio",
  responseMedia: [
    {
      id: "FiiPnuTzQ56Hz6B7iu4TTw",
      url: "https://www.datocms-assets.com/103689/1745956642-3-thet.mp3",
      title: null,
      filename: "3-thet.mp3",
    },
    {
      id: "Q_dkzp8hS7-67ZeOPH54OQ",
      url: "https://www.datocms-assets.com/103689/1745956643-4-ih.mp3",
      title: null,
      filename: "4-ih.mp3",
    },
    {
      id: "eXHHEhzkT5WhTZBFa58bMg",
      url: "https://www.datocms-assets.com/103689/1745956642-5-cal.mp3",
      title: null,
      filename: "5-cal.mp3",
    },
    {
      id: "eYkvQEO8RH6_5Lz8GdRRZQ",
      url: "https://www.datocms-assets.com/103689/1745956643-2-per.mp3",
      title: null,
      filename: "2-per.mp3",
    },
    {
      id: "Fek5iHlkRV-iY-2wWJm5Gw",
      url: "https://www.datocms-assets.com/103689/1745956642-1-hy.mp3",
      title: null,
      filename: "1-hy.mp3",
    },
  ] as unknown as FileField[],
  responseText: "",
  gridColumns: null,
  minimumResponsesRequired: 1,
  maximumResponsesAllowed: 5,
  correctAnswer: "1-hy,2-per,3-thet,4-ih,5-cal",
  ifCorrectText: "Correct!",
  ifCorrectAudio: null,
  ifCorrectImage: null,
  ifIncorrectAction: "reset",
  ifIncorrectText: "Try again!",
  ifIncorrectAudio: null,
  ifIncorrectImage: null,
  tutorialText: "Reorder the sounds you hear",
  tutorialAudio: null,
  tutorialImage: null,
} as unknown as NovaScaleItemRecord;

export const Default: Story = {
  args: {
    scaleItem: mockScaleItem,
  },
  render: (args) => <DragAudioV2 {...args} />,
};

export const Disabled: Story = {
  args: {
    scaleItem: mockScaleItem,
  },
  render: (args) => <DragAudioV2 {...args} disabled />,
};
