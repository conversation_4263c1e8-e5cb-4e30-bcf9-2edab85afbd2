"use client";

import { cva, VariantProps } from "class-variance-authority";
import { useMemo } from "react";

import { cn } from "@/lib/utils";

export interface ITimerProps extends VariantProps<typeof timerVariants> {
  totalTime: number;
  remainingTime: number;
  onTimerEnd?: () => void;
}

const timerVariants = cva("flex items-center gap-2", {
  variants: {
    variant: {
      default: "w-[649px]",
      small: "w-[300px]",
    },
  },
  defaultVariants: {
    variant: "default",
  },
});

export const Timer = ({ totalTime, remainingTime, variant }: ITimerProps) => {
  const percentComplete = (remainingTime / totalTime) * 100;

  const backgroundColor = useMemo(() => {
    if (percentComplete >= 75) return "#44B342";
    if (percentComplete >= 50) return "#FFCB3C";
    if (percentComplete >= 25) return "#F98119";
    return "#E74848";
  }, [percentComplete]);

  return (
    <div
      role="progressbar"
      aria-valuemin={0}
      aria-valuemax={totalTime}
      aria-valuenow={totalTime - remainingTime}
      className={timerVariants({ variant })}
    >
      <div
        className={cn(
          "relative w-full overflow-hidden bg-white rounded-full",
          variant === "small" ? "h-[12px]" : "h-6"
        )}
      >
        <div
          className={cn(
            "absolute top-0 left-0 h-full rounded-full transition-all ease-linear",
            variant === "small" ? "pt-[2px] px-1" : "pt-1 px-3",
            variant === "small" ? "duration-500" : "duration-1000"
          )}
          style={{
            width: `${percentComplete}%`,
            backgroundColor,
          }}
        >
          <div
            className={cn(
              "w-full rounded-full bg-white/40",
              variant === "small" ? "h-[2px]" : "h-1"
            )}
          />
        </div>
      </div>
    </div>
  );
};
