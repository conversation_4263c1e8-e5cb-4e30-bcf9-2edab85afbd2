import { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import { useEffect, useState } from "react";

import { ITimerProps, Timer } from "./timer";

const meta: Meta<typeof Timer> = {
  title: "Nova/Timer",
  component: Timer,
  argTypes: {
    totalTime: {
      control: { type: "number", min: 10, max: 300, step: 10 },
      description: "Total time in seconds.",
    },
    remainingTime: {
      control: { type: "number", min: 0, max: 300, step: 5 },
      description: "Remaining time in seconds.",
    },
  },
};

export default meta;
type Story = StoryObj<ITimerProps>;

export const Full: Story = {
  args: {
    totalTime: 100,
    remainingTime: 100,
  },
};

export const HalfTime: Story = {
  args: {
    totalTime: 100,
    remainingTime: 50,
  },
};

export const AlmostDone: Story = {
  args: {
    totalTime: 100,
    remainingTime: 10,
  },
};

export const Expired: Story = {
  args: {
    totalTime: 100,
    remainingTime: 0,
  },
};

export const Countdown: Story = {
  args: {
    totalTime: 30,
  },
  render: ({ totalTime }) => {
    const [remainingTime, setRemainingTime] = useState(totalTime);

    useEffect(() => {
      if (remainingTime > 0) {
        const interval = setInterval(() => {
          setRemainingTime((prev) => Math.max(prev - 1, 0));
        }, 1000);
        return () => clearInterval(interval);
      }
    }, [remainingTime]);

    return <Timer totalTime={totalTime} remainingTime={remainingTime} />;
  },
};
