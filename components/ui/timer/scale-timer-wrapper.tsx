"use client";

import { useEffect } from "react";

import { Timer } from "@/components/ui/timer/timer";
import { useScaleTimerStore } from "@/stores/use-scale-timer-store";

interface ScaleTimerWrapperProps {
  onTimerExpired: () => void;
}

export const ScaleTimerWrapper = ({
  onTimerExpired,
}: ScaleTimerWrapperProps) => {
  const { timeLeft, totalTime, isRunning } = useScaleTimerStore((state) => ({
    timeLeft: state.timeLeft,
    totalTime: state.totalTime,
    isRunning: state.isRunning,
  }));

  useEffect(() => {
    if (!isRunning && timeLeft === 0) {
      onTimerExpired();
    }
  }, [isRunning, timeLeft, onTimerExpired]);

  if (!isRunning || !timeLeft || !totalTime) return null;

  return <Timer remainingTime={timeLeft} totalTime={totalTime} />;
};
