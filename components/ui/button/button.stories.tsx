import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { Button } from "@/components/ui";

const meta: Meta<typeof Button> = {
  title: "Nova/Button",
  component: Button,
  args: {
    children: "Click me",
  },
  argTypes: {
    variant: {
      control: "select",
      options: ["default", "negative", "positive", "isLoading"],
    },
    size: {
      control: "select",
      options: ["default", "sm", "lg", "icon"],
    },
    disabled: {
      control: "boolean",
    },
  },
};

export default meta;
type Story = StoryObj<typeof Button>;

export const Default: Story = {};

export const Negative: Story = {
  args: {
    variant: "negative",
  },
};

export const Positive: Story = {
  args: {
    variant: "positive",
  },
};

export const Loading: Story = {
  args: {
    loading: true,
  },
};

export const DefaultDisabled: Story = {
  args: {
    variant: "default",
    disabled: true,
  },
};
