import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";
import * as React from "react";

import { cn } from "@/lib/utils";

const buttonVariants = cva(
  "relative inline-flex items-center justify-center font-semibold transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none",
  {
    variants: {
      variant: {
        default: "text-white bg-primary-purple hover:bg-primary-purple_hover",
        negative: "text-white bg-ui-alert_red_01 hover:bg-ui-alert-red-hover",
        positive:
          "text-white bg-ui-alert_green_01 hover:bg-ui-alert-green-hover",
      },
      size: {
        default: "h-[72px] px-6 text-[40px]",
        sm: "h-10 px-4 text-sm",
        lg: "h-14 px-8 text-xl",
        xl: "h-20 px-12 text-4xl",
        icon: "h-12 w-12 p-0",
        iconxl: "h-20 w-20 p-0",
      },
      loading: {
        true: "",
        false: "",
      },
      pulsate: {
        true: "",
        false: "",
      },
      light: {
        true: "",
        false: "",
      },
    },
    compoundVariants: [
      {
        loading: true,
        class: "px-0",
      },
      {
        variant: "default",
        light: true,
        class: "bg-secondary-purple_04",
      },
    ],
    defaultVariants: {
      variant: "default",
      size: "default",
      loading: false,
    },
  }
);

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      className,
      variant,
      size,
      loading,
      pulsate,
      light,
      asChild = false,
      children,
      disabled,
      ...props
    },
    ref
  ) => {
    const Comp = asChild ? Slot : "button";

    const isShadowActive = !disabled && !loading;
    const shadowColor = light === true ? "bg-[rgba(0,0,0,0.1)]" : "bg-white/20";

    const shadowTranslateClass =
      size === "icon" ? "translate-y-[4px]" : "translate-y-2";
    const activeTranslateClass =
      size === "icon"
        ? "group-active:translate-y-[4px]"
        : "group-active:translate-y-2";

    return (
      <div
        className={cn(
          pulsate && "animate-pulse-scale transition-all duration-900"
        )}
      >
        <Comp
          data-testid="button"
          className={cn(
            "relative inline-block cursor-pointer border-none bg-transparent p-0 outline-offset-4 transition-all duration-200 group disabled:pointer-events-none",
            disabled && "cursor-not-allowed",
            className
          )}
          ref={ref}
          aria-busy={loading === true}
          disabled={disabled}
          {...props}
        >
          <div>
            {isShadowActive ? (
              <span
                data-testid="button-shadow"
                className={cn(
                  "absolute top-0 left-0 w-full h-full rounded-full",
                  shadowTranslateClass,
                  shadowColor
                )}
              />
            ) : null}
            <span
              data-testid="button-top"
              className={cn(
                "relative block px-10 py-3 rounded-full will-change-transform transform transition-transform duration-500 ease-out select-none",
                buttonVariants({ variant, size }),
                isShadowActive ? activeTranslateClass : shadowTranslateClass,
                disabled &&
                  cn({
                    "bg-ui-grey_04 text-ui-grey_02": variant === "default",
                  }),
                variant === "default" &&
                  light === true &&
                  "bg-secondary-purple_04"
              )}
            >
              {loading ? <div className="loading-dots" /> : children}
            </span>
          </div>
        </Comp>
      </div>
    );
  }
);
Button.displayName = "Button";

export { Button, buttonVariants };
