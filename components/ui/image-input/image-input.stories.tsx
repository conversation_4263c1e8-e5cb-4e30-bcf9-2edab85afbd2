import { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import React from "react";

import { ImageInput, type ImageInputProps } from "./image-input";

const meta: Meta<typeof ImageInput> = {
  title: "Nova/Image Input",
  component: ImageInput,
  tags: ["autodocs"],
  argTypes: {
    variant: {
      control: "radio",
      options: ["default", "positive", "negative", "selected"],
    },
    size: {
      control: "radio",
      options: ["sm", "lg"],
    },
    isCorrect: {
      control: "boolean",
    },
    disabled: {
      control: "boolean",
    },
  },
  args: {
    alt: "Example image",
    disabled: false,
  },
};

export default meta;

type Story = StoryObj<typeof ImageInput>;

const variants: ImageInputProps["variant"][] = [
  "default",
  "selected",
  "positive",
  "negative",
];

const sizes: ImageInputProps["size"][] = ["sm", "lg"];

// Mock <PERSON>toCMS-style responsiveImage data
const mockResponsiveImage = {
  smallImage: {
    alt: "Small mock image",
    src: "https://www.datocms-assets.com/103689/1689868141-sr-sc-response-4.svg",
    width: 64,
    height: 64,
  },
  mediumImage: {
    alt: "Medium mock image",
    src: "https://www.datocms-assets.com/103689/1709299975-picvoc-stimulus-new-30.png",
    width: 128,
    height: 128,
  },
};

export const ShapeVariants: Story = {
  render: () => (
    <div className="flex flex-col gap-6">
      {sizes.map((size) => (
        <div key={size}>
          <h3 className="text-lg mb-2 text-white">Size: {size}</h3>
          <div className="flex gap-4 flex-wrap">
            {variants.map((variant) => (
              <ImageInput
                key={`${variant}-${size}-shape`}
                responsiveImage={mockResponsiveImage}
                alt={`Shape: ${variant} ${size}`}
                variant={variant}
                size={size}
              />
            ))}
            <ImageInput
              key={`default-${size}-shape-disabled`}
              responsiveImage={mockResponsiveImage}
              alt={`Shape: default ${size} (disabled)`}
              variant="default"
              size={size}
              disabled
            />
            <ImageInput
              key={`default-${size}-shape-correct`}
              responsiveImage={mockResponsiveImage}
              alt={`Shape: default ${size} (correct)`}
              variant="default"
              size={size}
              disabled
              isCorrect
            />
          </div>
        </div>
      ))}
    </div>
  ),
};

export const ImageVariants: Story = {
  render: () => (
    <div className="flex flex-col gap-6">
      {sizes.map((size) => (
        <div key={size}>
          <h3 className="text-lg mb-2 text-white">Size: {size}</h3>
          <div className="flex gap-4 flex-wrap">
            {variants.map((variant) => (
              <ImageInput
                key={`${variant}-${size}-image`}
                responsiveImage={mockResponsiveImage}
                alt={`Image: ${variant} ${size}`}
                variant={variant}
                size={size}
              />
            ))}
            <ImageInput
              key={`default-${size}-image-disabled`}
              responsiveImage={mockResponsiveImage}
              alt={`Image: default ${size} (disabled)`}
              variant="default"
              size={size}
              disabled
            />
            <ImageInput
              key={`default-${size}-image-correct`}
              responsiveImage={mockResponsiveImage}
              alt={`Image: default ${size} (correct)`}
              variant="default"
              size={size}
              disabled
              isCorrect
            />
          </div>
        </div>
      ))}
    </div>
  ),
};
