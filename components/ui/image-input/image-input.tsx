/* eslint-disable jsx-a11y/alt-text */
import { cva, type VariantProps } from "class-variance-authority";
import Image from "next/image";
import * as React from "react";
import { Image as DatoImage } from "react-datocms/image";

import { cn } from "@/lib/utils";

const imageInputVariants = cva(
  "inline-flex items-center justify-center transition-all duration-200 rounded-[12px] focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none",
  {
    variants: {
      variant: {
        default: "bg-white",
        positive: "bg-ui-alert_green_01",
        negative: "bg-ui-alert_red_01",
        selected: "bg-primary-yellow",
      },
      size: {
        sm: "p-2",
        lg: "p-3",
      },
      isCorrect: {
        true: "bg-white",
        false: "",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "lg",
    },
  }
);

const imageBorderColorMap = {
  default: "bg-white",
  positive: "bg-ui-alert_green_01",
  negative: "bg-ui-alert_red_01",
  selected: "bg-tertiary-yellow_02",
};

const imageBackgroundColorMap = {
  default: "bg-white",
  positive: "bg-ui-alert_green_02",
  negative: "bg-ui-alert_red_02",
  selected: "bg-tertiary-yellow_02",
};

export interface ImageInputProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof imageInputVariants> {
  responsiveImage?: any;
  fallbackUrl?: string;
  alt?: string;
  disabled?: boolean;
}

export const ImageInput = React.forwardRef<HTMLDivElement, ImageInputProps>(
  (
    {
      className,
      responsiveImage,
      fallbackUrl,
      alt,
      variant = "default",
      size = "lg",
      disabled = false,
      isCorrect = false,
      ...props
    },
    ref
  ) => {
    const isShadowActive =
      !disabled && variant !== "positive" && variant !== "negative";

    const imageSize =
      size === "sm" ? responsiveImage.smallImage : responsiveImage.mediumImage;

    const hasResponsiveImage = Boolean(
      responsiveImage.smallImage || responsiveImage.mediumImage
    );

    const fallbackImgDimensions = size === "sm" ? 136 : 176;

    return (
      <div className={cn(isCorrect && "animate-pulse-scale")}>
        <div
          ref={ref}
          className={cn(
            "relative inline-block cursor-pointer border-none bg-transparent p-0 outline-offset-4 transition-all duration-200 group disabled:pointer-events-none",
            disabled &&
              variant !== "positive" &&
              variant !== "negative" &&
              !isCorrect &&
              "opacity-50",
            disabled && "cursor-not-allowed",
            className
          )}
          {...props}
        >
          <div>
            {isShadowActive && (
              <span
                className="absolute top-0 left-0 w-full h-full rounded-[12px] bg-white/20 translate-y-2"
                aria-hidden="true"
              />
            )}

            <span
              className={cn(
                "relative block will-change-transform transform transition-transform duration-500 ease-out select-none rounded-[12px]",
                isShadowActive ? "group-active:translate-y-2" : "translate-y-0",
                imageInputVariants({ variant, size, isCorrect })
              )}
            >
              <div
                className={cn(
                  "overflow-hidden rounded-[8px]",
                  imageBorderColorMap[variant ?? "default"]
                )}
              >
                {hasResponsiveImage && (
                  <DatoImage data={imageSize} usePlaceholder priority />
                )}

                {!hasResponsiveImage && fallbackUrl && (
                  <div
                    className={imageBackgroundColorMap[variant ?? "default"]}
                  >
                    <Image
                      src={fallbackUrl}
                      priority
                      width={fallbackImgDimensions}
                      height={fallbackImgDimensions}
                      alt={""}
                    />
                  </div>
                )}
              </div>
            </span>
          </div>
        </div>
      </div>
    );
  }
);

ImageInput.displayName = "ImageInput";
