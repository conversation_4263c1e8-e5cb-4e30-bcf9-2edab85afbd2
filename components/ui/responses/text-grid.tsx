"use client";

import * as React from "react";
import { useEffect, useMemo, useState } from "react";

import { NovaScaleItemRecord } from "@/types/graphql/graphql";

import { TextInput, type TextInputProps } from "../text-input/text-input";

interface TextGridProps {
  scaleItem: NovaScaleItemRecord;
  onChange?: (selected: string[]) => void;
  showFeedback?: boolean;
  selected?: string[];
  resetKey?: number;
}

export const TextGrid: React.FC<TextGridProps> = ({
  scaleItem,
  onChange,
  showFeedback = false,
  resetKey,
}) => {
  const {
    responseText,
    correctAnswer,
    gridColumns = 5,
    maximumResponsesAllowed,
  } = scaleItem;

  const responseItems = useMemo(() => {
    return (responseText ?? "")
      .split(",")
      .map((item) => item.trim())
      .filter(Boolean);
  }, [responseText]);

  const maxAllowed = maximumResponsesAllowed ?? responseItems.length;
  const correctAnswers = correctAnswer?.split(",").map((i) => i.trim()) ?? [];

  const [selectedIndices, setSelectedIndices] = useState<number[]>([]);

  useEffect(() => {
    setSelectedIndices([]);
  }, [resetKey]);

  const isAtMax = selectedIndices.length >= maxAllowed;

  const toggleSelect = (index: number) => {
    const isSelected = selectedIndices.includes(index);
    let newIndices: number[];

    if (maxAllowed === 1) {
      newIndices = isSelected ? [] : [index];
    } else {
      if (isSelected) {
        newIndices = selectedIndices.filter((i) => i !== index);
      } else {
        if (isAtMax) return;
        newIndices = [...selectedIndices, index];
      }
    }

    setSelectedIndices(newIndices);

    const selectedValues = newIndices.map((i) => responseItems[i]);
    onChange?.(selectedValues);
  };

  return (
    <div
      className="grid gap-4 justify-center"
      style={{ gridTemplateColumns: `repeat(${gridColumns}, auto)` }}
    >
      {responseItems.map((item, index) => {
        let variant: TextInputProps["variant"] = "default";
        let isCorrect = false;

        const isSelected = selectedIndices.includes(index);
        const isCorrectAnswer = correctAnswers.includes(item);

        if (showFeedback) {
          if (isSelected && isCorrectAnswer) variant = "positive";
          else if (isSelected) variant = "negative";
          else if (isCorrectAnswer) isCorrect = true;
        } else {
          variant = isSelected ? "selected" : "default";
        }

        const isDisabled =
          showFeedback || (maxAllowed !== 1 && !isSelected && isAtMax);
        const size: TextInputProps["size"] = item.length < 3 ? "sm" : "md";

        return (
          <TextInput
            key={`${item}-${index}`}
            label={item}
            size={size}
            variant={variant}
            isCorrect={isCorrect}
            onClick={() => !isDisabled && toggleSelect(index)}
            disabled={isDisabled}
          />
        );
      })}
    </div>
  );
};
