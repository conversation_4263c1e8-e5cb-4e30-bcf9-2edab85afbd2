import { useEffect, useRef } from "react";

import { NovaScaleItemRecord } from "@/types/graphql/graphql";

import {
  KeyboardInput,
  KeyboardInputGroup,
  KeyboardInputSlot,
} from "../keyboard-input/keyboard-input";

interface KeyboardResponseProps {
  scaleItem: NovaScaleItemRecord;
  onChange?: (selected: string[]) => void;
  showFeedback?: boolean;
  selected?: string[];
  disabled?: boolean;
  isCorrect?: boolean | null;
}

export const KeyboardResponse: React.FC<KeyboardResponseProps> = ({
  scaleItem,
  onChange,
  showFeedback = false,
  selected = [],
  disabled = false,
  isCorrect,
}) => {
  const inputRef = useRef<HTMLInputElement>(null);
  const prevDisabled = useRef(disabled);
  const autofocusOnMount = !scaleItem?.disabledWhileAutoplay;
  const isTutorial = !!scaleItem?.tutorial;

  const correctAnswer =
    scaleItem.correctAnswer?.split(",").map((i) => i.trim()) ?? [];

  const expectedLength =
    scaleItem.maximumResponsesAllowed ?? correctAnswer.length;

  const hasError = Boolean(showFeedback && selected.length > 0 && !isCorrect);
  const showCorrect = Boolean(showFeedback && selected.length > 0 && isCorrect);

  const handleChange = (val: string) => {
    const trimmed = val.slice(0, expectedLength);
    const next = trimmed.split("");
    onChange?.(next);
  };

  useEffect(() => {
    if (prevDisabled.current && !disabled && inputRef.current) {
      inputRef.current.focus();
    }
    prevDisabled.current = disabled;
  }, [disabled]);

  useEffect(() => {
    if (autofocusOnMount && !isTutorial && inputRef.current) {
      inputRef.current.focus();
    }
  }, []);

  return (
    <KeyboardInput
      ref={inputRef}
      maxLength={expectedLength}
      value={selected.join("").slice(0, expectedLength)}
      onChange={handleChange}
      hasError={hasError}
      showCorrect={showCorrect}
      lettersOnly={scaleItem.responseType === "keyboard_letters"}
      numbersOnly={scaleItem.responseType === "keyboard_digits"}
      disabled={disabled}
    >
      <KeyboardInputGroup>
        {Array.from({ length: expectedLength }).map((_, idx) => (
          <KeyboardInputSlot key={idx} index={idx} />
        ))}
      </KeyboardInputGroup>
    </KeyboardInput>
  );
};
