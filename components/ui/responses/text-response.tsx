"use client";

import React, { useEffect, useRef, useState } from "react";

import { NovaScaleItemRecord } from "@/types/graphql/graphql";

import { AudioButton } from "../audio-button/audio-button";
import { TextInput, type TextInputProps } from "../text-input/text-input";

interface TextResponseProps {
  scaleItem: NovaScaleItemRecord;
  onChange?: (selected: string[]) => void;
  showFeedback?: boolean;
  selected?: string[];
}

const getSizeFromType = (type: string): TextInputProps["size"] => {
  switch (type) {
    case "text_small":
      return "sm";
    case "text_large":
      return "xl";
    default:
      return "lg";
  }
};

export const TextResponse: React.FC<TextResponseProps> = ({
  scaleItem,
  onChange,
  showFeedback = false,
  selected,
}) => {
  const selectedAnswers = selected ?? [];

  const {
    responseText,
    responseType,
    maximumResponsesAllowed,
    correctAnswer,
    responseMedia,
  } = scaleItem;

  const isAudio = responseType === "text_audio";

  const responseItems = (responseText ?? "")
    .split(",")
    .map((item) => item.trim())
    .filter(Boolean);

  const maxAllowed = maximumResponsesAllowed ?? responseItems.length;
  const isAtMax = selectedAnswers.length >= maxAllowed;
  const size = getSizeFromType(responseType);
  const correctAnswers = correctAnswer?.split(",").map((i) => i.trim()) ?? [];

  const [playingIndex, setPlayingIndex] = useState<number | null>(null);
  const audioCache = useRef<Map<string, HTMLAudioElement>>(new Map());
  const lastPlayedTimestamps = useRef<Record<number, number>>({});

  useEffect(() => {
    if (!isAudio || !responseMedia) return;

    responseMedia.forEach((media) => {
      if (!media?.url || audioCache.current.has(media.url)) return;

      const audio = new Audio(media.url);
      audio.preload = "auto";
      audio.load();
      audioCache.current.set(media.url, audio);
    });
  }, [isAudio, responseMedia]);

  const toggleSelect = (item: string) => {
    const isSelected = selectedAnswers.includes(item);

    let newSelected: string[];

    if (maxAllowed === 1) {
      newSelected = isSelected ? [] : [item];
    } else {
      newSelected = isSelected
        ? selectedAnswers.filter((i) => i !== item)
        : isAtMax
          ? selectedAnswers
          : [...selectedAnswers, item];
    }

    onChange?.(newSelected);
  };

  const handleHover = (index: number, mediaUrl: string | null) => {
    if (!mediaUrl) return;

    const now = Date.now();
    const lastPlayed = lastPlayedTimestamps.current[index] ?? 0;
    const cooldown = 1000;
    if (now - lastPlayed < cooldown) return;

    const audio = audioCache.current.get(mediaUrl);
    if (audio) {
      audio.currentTime = 0;
      audio.play();
      lastPlayedTimestamps.current[index] = now;
      setPlayingIndex(index);
    }
  };

  return (
    <div
      className={
        size === "sm"
          ? "flex flex-wrap gap-4 justify-center"
          : "grid grid-cols-1 sm:grid-cols-2 gap-4 justify-center"
      }
    >
      {responseItems.map((item, index) => {
        let variant: TextInputProps["variant"] = "default";
        let isCorrect = false;

        const isCorrectAnswer = correctAnswers.includes(item);
        const isSelected = selectedAnswers.includes(item);

        if (showFeedback) {
          if (isSelected && isCorrectAnswer) variant = "positive";
          else if (isSelected) variant = "negative";
          else if (isCorrectAnswer) isCorrect = true;
        } else {
          variant = isSelected ? "selected" : "default";
        }

        const isDisabled =
          showFeedback || (maxAllowed !== 1 && !isSelected && isAtMax);
        const mediaUrl = responseMedia?.[index]?.url ?? null;

        return (
          <TextInput
            key={`${item}-${index}`}
            label={item}
            size={size}
            variant={variant}
            isCorrect={isCorrect}
            onClick={() => !isDisabled && toggleSelect(item)}
            disabled={isDisabled}
            beforeLabel={
              isAudio ? (
                <div
                  onMouseEnter={() => {
                    if (!isDisabled) handleHover(index, mediaUrl);
                  }}
                >
                  <AudioButton
                    disabled={isDisabled}
                    size="sm"
                    variant={variant !== "default" ? "light" : "default"}
                    isPlaying={playingIndex === index}
                  />
                </div>
              ) : undefined
            }
          />
        );
      })}
    </div>
  );
};
