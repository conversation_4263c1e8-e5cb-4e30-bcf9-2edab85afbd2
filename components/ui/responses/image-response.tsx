import React, { useEffect } from "react";

import { NovaScaleItemRecord } from "@/types/graphql/graphql";

import { ImageInput, type ImageInputProps } from "../image-input/image-input";

interface ImageResponseProps {
  scaleItem: NovaScaleItemRecord;
  onChange?: (selected: string[]) => void;
  showFeedback?: boolean;
  selected?: string[];
}

const getSizeFromType = (type): ImageInputProps["size"] => {
  return type === "image_small" ? "sm" : "lg";
};

export const ImageResponse: React.FC<ImageResponseProps> = ({
  scaleItem,
  onChange,
  showFeedback = false,
  selected,
}) => {
  const selectedAnswers = selected ?? [];

  const {
    responseMedia,
    responseType,
    maximumResponsesAllowed,
    correctAnswer,
  } = scaleItem;

  const maxAllowed = maximumResponsesAllowed ?? responseMedia.length;
  const isAtMax = selectedAnswers.length >= maxAllowed;
  const size = getSizeFromType(responseType);

  const correctAnswers = correctAnswer?.split(",").map((i) => i.trim()) ?? [];

  const toggleSelect = (responseIndex: string) => {
    const isSelected = selectedAnswers.includes(responseIndex);
    let newSelected: string[];

    if (maxAllowed === 1) {
      newSelected = [responseIndex];
    } else {
      if (isSelected) {
        newSelected = selectedAnswers.filter((item) => item !== responseIndex);
      } else {
        if (isAtMax) return;
        newSelected = [...selectedAnswers, responseIndex];
      }
    }

    onChange?.(newSelected);
  };

  return (
    <div className="flex flex-wrap gap-4 justify-center">
      {responseMedia.map((item, index) => {
        const responseIndex = (index + 1).toString();
        const isSelected = selectedAnswers.includes(responseIndex);
        const isCorrectAnswer = correctAnswers.includes(responseIndex);

        let variant: ImageInputProps["variant"] = "default";
        let isCorrect = false;

        if (showFeedback) {
          if (isSelected && isCorrectAnswer) {
            variant = "positive";
          } else if (isSelected && !isCorrectAnswer) {
            variant = "negative";
          } else if (!isSelected && isCorrectAnswer) {
            isCorrect = true;
          }
        } else {
          variant = isSelected ? "selected" : "default";
        }

        const isDisabled =
          showFeedback || (maxAllowed !== 1 && !isSelected && isAtMax);

        return (
          <ImageInput
            key={index}
            fallbackUrl={item.url}
            responsiveImage={item}
            alt=""
            size={size}
            variant={variant}
            isCorrect={isCorrect}
            onClick={() => !isDisabled && toggleSelect(responseIndex)}
            disabled={isDisabled}
          />
        );
      })}
    </div>
  );
};
