import { action } from "@storybook/addon-actions";
import { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import React, { useState } from "react";

import { Button } from "@/components/ui/button/button";
import { NovaScaleItemRecord } from "@/types/graphql/graphql";

import { ImageResponse } from "./image-response";

const meta: Meta<typeof ImageResponse> = {
  title: "Response/Image Response",
  component: ImageResponse,
};

export default meta;

type Story = StoryObj<typeof ImageResponse>;

const mockScaleItem = {
  __typename: "NovaScaleItemRecord",
  id: "LnsCAi5NRkaAJwlBNzMzYQ",
  difficulty: "easy",
  practiceItem: false,
  feedback: true,
  skippable: false,
  stimulusType: "audio",
  stimulusText: "",
  stimulusMedia: {
    url: "https://www.datocms-assets.com/103689/1688725428-general-correct.mp3",
  },
  audioPlays: 3,
  responseType: "image_large",
  responseMedia: [
    {
      url: "https://www.datocms-assets.com/103689/1708948828-picvo-stimulus-i27new-4.png",
    },
    {
      url: "https://www.datocms-assets.com/103689/1709466399-church.png",
    },
    {
      url: "https://www.datocms-assets.com/103689/1709466178-jimminoid_stock_simple_plain_background_mask_585e6016-e953-45c0-9272-50eefe7603e8.png",
    },
    {
      url: "https://www.datocms-assets.com/103689/1709466840-lamb.png",
    },
  ],
  responseText: "",
  minimumResponsesRequired: 1,
  maximumResponsesAllowed: 3,
  correctAnswer: "1,2",
  ifCorrectText: "",
  ifCorrectAudio: null,
  ifCorrectImage: null,
  ifIncorrectAction: "",
  ifIncorrectText: "",
  ifIncorrectAudio: null,
  ifIncorrectImage: null,
};
export const Default: Story = {
  args: {
    scaleItem: mockScaleItem as unknown as NovaScaleItemRecord,
  },
  render: (args) => {
    const [selected, setSelected] = useState<string[]>([]);
    const [showFeedback, setShowFeedback] = useState(false);

    return (
      <div className="flex flex-col items-center gap-6">
        <ImageResponse
          {...args}
          selected={selected}
          onChange={(newSelected) => {
            action("onChange")(newSelected);
            setSelected(newSelected);
          }}
          showFeedback={showFeedback}
        />

        <div className="flex flex-col gap-4 mt-6">
          <Button size="lg" onClick={() => setShowFeedback((prev) => !prev)}>
            Toggle feedback {showFeedback ? "off" : "on"}
          </Button>
          <button
            onClick={() => setSelected([])}
            className="text-sm underline text-muted-foreground"
          >
            Clear selection
          </button>
        </div>
      </div>
    );
  },
};
