"use client";

import { OTPInput, OTPInputContext } from "input-otp";
import { Minus } from "lucide-react";
import * as React from "react";

import { cn } from "@/lib/utils";

const KeyboardInput = React.forwardRef<
  React.ElementRef<typeof OTPInput>,
  React.ComponentPropsWithoutRef<typeof OTPInput> & {
    hasError?: boolean;
    showCorrect?: boolean;
    lettersOnly?: boolean;
    numbersOnly?: boolean;
  }
>(
  (
    {
      className,
      containerClassName,
      hasError,
      showCorrect,
      lettersOnly,
      numbersOnly,
      onKeyDown,
      ...props
    },
    ref
  ) => {
    const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
      const allowedNavigation = [
        "Backspace",
        "Delete",
        "ArrowLeft",
        "ArrowRight",
        "Tab",
      ];

      if (allowedNavigation.includes(e.key)) return;

      const isLetter = /^[a-zA-Z]$/.test(e.key);
      const isNumber = /^[0-9]$/.test(e.key);

      if (lettersOnly && !isLetter) {
        e.preventDefault();
        return;
      }

      if (numbersOnly && !isNumber) {
        e.preventDefault();
        return;
      }

      if (!lettersOnly && !numbersOnly && !(isLetter || isNumber)) {
        e.preventDefault();
        return;
      }

      onKeyDown?.(e);
    };

    return (
      <div className="block h-[88px] ">
        <OTPInput
          spellCheck={false}
          ref={ref}
          onKeyDown={handleKeyDown}
          onPaste={(e) => {
            const pasted = e.clipboardData.getData("text");
            if (
              (lettersOnly && /[^a-zA-Z]/.test(pasted)) ||
              (numbersOnly && /[^0-9]/.test(pasted)) ||
              (!lettersOnly && !numbersOnly && /[^a-zA-Z0-9]/.test(pasted))
            ) {
              e.preventDefault();
            }
          }}
          containerClassName={cn(
            "inline-flex items-center gap-2 has-[:disabled]:opacity-50 transition-all",
            hasError && "ring-8 ring-ui-alert_red_01 rounded-md",
            showCorrect && "ring-8 ring-ui-alert_green_01 rounded-md",
            containerClassName
          )}
          className={cn("disabled:cursor-not-allowed", className)}
          {...props}
        />
      </div>
    );
  }
);
KeyboardInput.displayName = "KeyboardInput";

const KeyboardInputGroup = React.forwardRef<
  React.ElementRef<"div">,
  React.ComponentPropsWithoutRef<"div">
>(({ className, ...props }, ref) => (
  <div ref={ref} className={cn("flex items-center", className)} {...props} />
));
KeyboardInputGroup.displayName = "KeyboardInputGroup";

const KeyboardInputSlot = React.forwardRef<
  React.ElementRef<"div">,
  React.ComponentPropsWithoutRef<"div"> & { index: number }
>(({ index, className, ...props }, ref) => {
  const KeyboardInputContext = React.useContext(OTPInputContext);
  const { char, hasFakeCaret, isActive } = KeyboardInputContext.slots[index];

  return (
    <div
      ref={ref}
      className={cn(
        "bg-white text-black font-semibold relative flex h-[88px] w-[72px] items-center justify-center border-y border-r border-input text-5xl transition-all first:rounded-l-md first:border-l last:rounded-r-md",
        isActive &&
          "z-10 rounded-md ring-[8px] ring-primary-yellow border-transparent",
        className
      )}
      {...props}
    >
      {char}
      {hasFakeCaret && (
        <div className="pointer-events-none absolute inset-0 flex items-center justify-center">
          <div className="h-10 w-1 animate-caret-blink bg-foreground duration-1000" />
        </div>
      )}
    </div>
  );
});
KeyboardInputSlot.displayName = "KeyboardInputSlot";

const KeyboardInputSeparator = React.forwardRef<
  React.ElementRef<"div">,
  React.ComponentPropsWithoutRef<"div">
>(({ ...props }, ref) => (
  <div ref={ref} role="separator" {...props}>
    <Minus />
  </div>
));
KeyboardInputSeparator.displayName = "KeyboardInputSeparator";

export {
  KeyboardInput,
  KeyboardInputGroup,
  KeyboardInputSeparator,
  KeyboardInputSlot,
};
