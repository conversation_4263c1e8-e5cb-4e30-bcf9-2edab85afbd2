import { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import { OTPInput } from "input-otp";
import { useRef, useState } from "react";

import {
  KeyboardInput,
  KeyboardInputGroup,
  KeyboardInputSlot,
} from "./keyboard-input";

const meta: Meta<typeof KeyboardInput> = {
  title: "Nova/Keyboard Input",
  component: KeyboardInput,
  argTypes: {
    hasError: {
      control: "boolean",
      description: "Indicates whether the input should show an error state.",
    },
    maxLength: {
      control: { type: "number", min: 4, max: 8, step: 1 },
      description: "Number of OTP input slots.",
    },
  },
};

export default meta;

type Story = StoryObj<{
  hasError: boolean;
  maxLength: number;
}>;

const Template = ({
  hasError,
  maxLength,
  ...extraProps
}: {
  hasError: boolean;
  maxLength: number;
  lettersOnly?: boolean;
  numbersOnly?: boolean;
}) => {
  const [otp, setOtp] = useState("");
  const otpRef = useRef<React.ElementRef<typeof OTPInput>>(null);

  return (
    <KeyboardInput
      ref={otpRef}
      maxLength={maxLength}
      value={otp}
      hasError={hasError}
      onChange={setOtp}
      {...extraProps}
    >
      <KeyboardInputGroup spellCheck={false}>
        {[...Array(maxLength)].map((_, i) => (
          <KeyboardInputSlot key={i} index={i} />
        ))}
      </KeyboardInputGroup>
    </KeyboardInput>
  );
};

export const Default: Story = {
  args: {
    hasError: false,
    maxLength: 8,
  },
  render: Template,
};

export const WithError: Story = {
  args: {
    hasError: true,
    maxLength: 8,
  },
  render: Template,
};

export const LettersOnly: Story = {
  args: {
    hasError: false,
    maxLength: 6,
  },
  render: (args) => Template({ ...args, lettersOnly: true }),
};

export const NumbersOnly: Story = {
  args: {
    hasError: false,
    maxLength: 6,
  },
  render: (args) => Template({ ...args, numbersOnly: true }),
};
