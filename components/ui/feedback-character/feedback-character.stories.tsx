import { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";

import { SpeechBubbleProps } from "../speech-bubble/speech-bubble";
import { FeedbackCharacter } from "./feedback-character";

const speechBubbleVariants: SpeechBubbleProps["variant"][] = [
  "default",
  "success",
  "error",
];

const meta: Meta<typeof FeedbackCharacter> = {
  title: "Nova/Feedback Character",
  component: FeedbackCharacter,
  argTypes: {
    variant: {
      control: "select",
      options: speechBubbleVariants,
    },
    isActive: {
      control: "boolean",
    },
    message: {
      control: "text",
    },
  },
};

export default meta;
type Story = StoryObj<typeof FeedbackCharacter>;

export const Active: Story = {
  args: {
    message: "I'm active now!",
    variant: "default",
    isActive: true,
  },
};

export const Success: Story = {
  args: {
    message: "Great job!",
    variant: "success",
    isActive: true,
  },
};

export const Error: Story = {
  args: {
    message: "Something went wrong!",
    variant: "error",
    isActive: true,
  },
};
