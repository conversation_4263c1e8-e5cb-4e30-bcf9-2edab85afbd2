"use client";

import { motion } from "framer-motion";
import Image from "next/image";

import { SpeechBubble } from "@/components/ui";

import { SpeechBubbleProps } from "../speech-bubble/speech-bubble";

interface IFeedbackCharacterProps {
  message: string;
  variant?: SpeechBubbleProps["variant"];
  isActive?: boolean;
  characterUrl?: string;
}

export const FeedbackCharacter = ({
  variant = "default",
  message,
  isActive = false,
  characterUrl = "/images/feedback-character.svg",
}: IFeedbackCharacterProps) => {
  return (
    <div className="fixed bottom-0 left-0 z-10 select-none">
      <motion.div
        initial={{ x: "-100%" }}
        animate={{
          x: isActive ? "0%" : "-100%",
          y: isActive ? "0" : "30px",
          rotate: isActive ? "0deg" : "-2deg",
        }}
        transition={{
          type: "spring",
          stiffness: 180,
          damping: 12,
          mass: 0.5,
        }}
        className="relative w-[200px] h-[200px]"
      >
        <Image src={characterUrl} width={200} height={200} alt="" priority />

        {isActive && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            transition={{
              type: "spring",
              stiffness: 200,
              damping: 15,
              delay: 0.2,
            }}
            className="absolute bottom-[170px] -right-[230px] w-[288px]"
          >
            <SpeechBubble variant={variant} width="medium">
              {message}
            </SpeechBubble>
          </motion.div>
        )}
      </motion.div>
    </div>
  );
};
