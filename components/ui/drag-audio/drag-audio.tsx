import { Reorder } from "framer-motion";
import React, { useEffect, useMemo, useRef, useState } from "react";

import type { NovaScaleItemRecord } from "@/types/graphql/graphql";

import { AudioButton } from "../audio-button/audio-button";
import { AudioChip } from "../audio-chip/audio-chip";

interface DragAudioResponseProps {
  scaleItem: NovaScaleItemRecord;
  onChange?: (selected: string[]) => void;
  selected?: string[];
  showFeedback?: boolean;
  disabled?: boolean;
}

interface DraggableButtonProps {
  id: string;
  label: string;
  isInTopRow: boolean;
  isPlaying: boolean;
  onClick: (id: string) => void;
  onHover: (id: string) => void;
  onHoverEnd: () => void;
  variant: "default" | "positive" | "negative";
  isCorrect: boolean;
  disabled: boolean;
}

const DraggableButton = ({
  id,
  label,
  isInTopRow,
  isPlaying,
  onClick,
  onHover,
  onHoverEnd,
  disabled,
}: DraggableButtonProps) => {
  const isDragging = useRef(false);

  const handlePointerDown = () => {
    isDragging.current = false;
  };

  const handlePointerMove = () => {
    isDragging.current = true;
  };

  const handlePointerUp = () => {
    if (!isDragging.current) {
      onClick(id);
    }
  };

  return (
    <div
      onPointerDown={handlePointerDown}
      onPointerMove={handlePointerMove}
      onPointerUp={handlePointerUp}
      onMouseEnter={() => {
        if (!disabled && !isInTopRow) onHover(id);
      }}
      onMouseLeave={() => {
        if (!disabled) onHoverEnd();
      }}
      className="cursor-pointer w-full h-full flex items-center justify-center"
    >
      <AudioChip disabled={disabled || isPlaying}>{label}</AudioChip>
    </div>
  );
};

export const DragAudioResponse: React.FC<DragAudioResponseProps> = ({
  scaleItem,
  onChange,
  selected = [],
  showFeedback = false,
  disabled = false,
}) => {
  const responseMedia = useMemo(
    () => scaleItem.responseMedia || [],
    [scaleItem]
  );
  const maxAllowed = scaleItem.maximumResponsesAllowed ?? responseMedia.length;
  const correctAnswers =
    scaleItem.correctAnswer?.split(",").map((s) => s.trim()) ?? [];

  const [topRow, setTopRow] = useState<string[]>(selected);
  const [currentlyPlayingId, setCurrentlyPlayingId] = useState<string | null>(
    null
  );
  const [isLoadingAudio, setIsLoadingAudio] = useState(true);
  const audioBuffers = useRef<Record<string, AudioBuffer>>({});
  const audioContext = useRef<AudioContext | null>(null);
  const hoverTimeout = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    audioContext.current = new (window.AudioContext ||
      window.webkitAudioContext)();
    loadAllAudio();
    return () => {
      audioContext.current?.close();
    };
  }, []);

  useEffect(() => {
    setTopRow(
      selected
        .map(
          (f) =>
            responseMedia.find((c) => c.filename.replace(/\.mp3$/, "") === f)
              ?.filename
        )
        .filter(Boolean) as string[]
    );
  }, [responseMedia, selected]);

  const topRowWidth = useMemo(() => {
    const chipWidth = 80;
    const gap = 12;
    const buttonWidth = 40;
    const outerPadding = 24;
    const n = responseMedia.length;
    return `calc(${n} * ${chipWidth}px + ${n} * ${gap}px + ${buttonWidth}px + ${
      outerPadding * 2
    }px)`;
  }, [responseMedia.length]);

  const loadAllAudio = async () => {
    if (!audioContext.current) return;
    for (const clip of responseMedia) {
      const response = await fetch(clip.url);
      const arrayBuffer = await response.arrayBuffer();
      const audioBuffer =
        await audioContext.current.decodeAudioData(arrayBuffer);
      audioBuffers.current[clip.url] = audioBuffer;
    }
    setIsLoadingAudio(false);
  };

  const handleHoverPlay = (filename: string) => {
    if (disabled || !audioContext.current) return;
    if (hoverTimeout.current) clearTimeout(hoverTimeout.current);

    const clip = responseMedia.find((c) => c.filename === filename);
    if (!clip) return;

    hoverTimeout.current = setTimeout(() => {
      const buffer = audioBuffers.current[clip.url];
      if (buffer) {
        const source = audioContext.current!.createBufferSource();
        source.buffer = buffer;
        source.connect(audioContext.current!.destination);
        source.start();
      }
    }, 200);
  };

  const cancelHoverPlay = () => {
    if (hoverTimeout.current) clearTimeout(hoverTimeout.current);
  };

  const handleClick = (filename: string) => {
    if (disabled) return;
    let newTop: string[];

    if (topRow.includes(filename)) {
      newTop = topRow.filter((i) => i !== filename);
    } else {
      if (topRow.length >= maxAllowed) return;
      newTop = [...topRow, filename];
    }

    setTopRow(newTop);
    onChange?.(newTop.map((f) => f.replace(/\.mp3$/, "")));
  };

  const handlePlayAll = async () => {
    if (!topRow.length || !audioContext.current) return;
    await audioContext.current.resume();

    const ctx = audioContext.current;
    const now = ctx.currentTime;
    let offset = 0;

    for (const filename of topRow) {
      const clip = responseMedia.find((c) => c.filename === filename);
      if (!clip) continue;

      const buffer = audioBuffers.current[clip.url];
      if (buffer) {
        const source = ctx.createBufferSource();
        source.buffer = buffer;
        source.connect(ctx.destination);
        source.start(now + offset);

        setTimeout(() => setCurrentlyPlayingId(filename), offset * 1000);
        setTimeout(
          () => setCurrentlyPlayingId(null),
          (offset + buffer.duration) * 1000
        );

        offset += buffer.duration;
      }
    }
  };

  return (
    <div className="flex flex-col gap-12 items-center">
      <div
        className="flex items-center gap-3 py-3 px-6 rounded-[30px] bg-white min-h-[100px]"
        style={{ width: topRowWidth }}
      >
        <AudioButton
          onClick={handlePlayAll}
          playIcon
          size="md"
          disabled={disabled || isLoadingAudio || topRow.length === 0}
        />
        <Reorder.Group
          axis="x"
          values={topRow}
          onReorder={(newOrder) => {
            setTopRow(newOrder);
            onChange?.(newOrder.map((f) => f.replace(/\.mp3$/, "")));
          }}
          className="flex flex-row gap-3 items-center"
        >
          {topRow.map((filename, index) => {
            const clip = responseMedia.find((c) => c.filename === filename);
            if (!clip) return null;

            const selectedFilename = selected[index];
            const correctFilename = correctAnswers[index];

            const isSelected = clip.filename === selectedFilename;
            const isCorrectAnswer =
              clip.filename.replace(/\.mp3$/, "") === correctFilename;

            let variant: "default" | "positive" | "negative" = "default";
            let isCorrect = false;

            if (showFeedback) {
              if (isSelected && isCorrectAnswer) {
                variant = "positive";
              } else if (isSelected) {
                variant = "negative";
              } else if (isCorrectAnswer) {
                isCorrect = true;
              }
            }
            return (
              <Reorder.Item
                key={filename}
                value={filename}
                className="w-[80px] h-[80px] flex items-center justify-center rounded-[30px]"
                whileDrag={{
                  scale: 1.2,
                  rotate: Math.random() * 10 - 3,
                  y: -13,
                  boxShadow: "0px 15px 30px rgba(0, 0, 0, 0.4)",
                  zIndex: 50,
                }}
                transition={{ type: "spring", stiffness: 500, damping: 30 }}
              >
                <DraggableButton
                  id={clip.filename}
                  label={clip.filename}
                  isInTopRow
                  isPlaying={currentlyPlayingId === filename}
                  onClick={handleClick}
                  onHover={handleHoverPlay}
                  onHoverEnd={cancelHoverPlay}
                  variant={variant}
                  isCorrect={isCorrect}
                  disabled={showFeedback || disabled}
                />
              </Reorder.Item>
            );
          })}
        </Reorder.Group>
      </div>

      <div className="flex flex-wrap gap-4 justify-center min-h-[100px]">
        {responseMedia.map((clip) => {
          const isSelected = topRow.includes(clip.filename);
          return (
            <div
              key={clip.filename}
              className="w-[80px] h-[80px] flex items-center justify-center rounded-[10px] bg-white/20"
            >
              {!isSelected ? (
                <DraggableButton
                  id={clip.filename}
                  label={clip.filename}
                  isInTopRow={false}
                  isPlaying={false}
                  onClick={handleClick}
                  onHover={handleHoverPlay}
                  onHoverEnd={cancelHoverPlay}
                  variant="default"
                  isCorrect={false}
                  disabled={
                    disabled || showFeedback || topRow.length >= maxAllowed
                  }
                />
              ) : null}
            </div>
          );
        })}
      </div>
    </div>
  );
};
