import { <PERSON><PERSON>, <PERSON><PERSON>bj } from "@storybook/react";
import React from "react";

import { AudioButton } from "../audio-button/audio-button";
import { TextInput, type TextInputProps } from "./text-input";

const meta: Meta<typeof TextInput> = {
  title: "Nova/Text Input",
  component: TextInput,
  tags: ["autodocs"],
  argTypes: {
    variant: {
      control: "radio",
      options: ["default", "positive", "negative", "selected"],
    },
    size: {
      control: "radio",
      options: ["sm", "md", "lg", "xl"],
    },
  },
  args: {
    disabled: false,
  },
};

export default meta;

type Story = StoryObj<typeof TextInput>;

const variants: TextInputProps["variant"][] = [
  "default",
  "selected",
  "positive",
  "negative",
];

function renderInputsForSize(
  size: TextInputProps["size"],
  label: string,
  includeAudio = false
) {
  return (
    <div className="flex flex-col gap-4">
      <h3 className="text-lg text-white">Size: {size}</h3>
      <div className="flex gap-4 flex-wrap">
        {variants.map((variant) => (
          <TextInput
            key={`${variant}-${size}`}
            label={label}
            variant={variant}
            size={size}
          />
        ))}
        <TextInput
          key={`default-${size}-disabled`}
          label={label}
          variant="default"
          size={size}
          disabled
        />
        <TextInput
          key={`default-${size}-disabled-correct`}
          label={label}
          variant="default"
          size={size}
          disabled
          isCorrect
        />
        {includeAudio &&
          variants.map((variant) => (
            <TextInput
              key={`${variant}-${size}-audio`}
              label={label}
              variant={variant}
              size={size}
              beforeLabel={
                <AudioButton
                  variant={variant === "default" ? "default" : "light"}
                  size="sm"
                />
              }
            />
          ))}
      </div>
    </div>
  );
}

export const Small: Story = {
  name: "Small (sm)",
  render: () => renderInputsForSize("sm", "1"),
};

export const Medium: Story = {
  name: "Medium (md)",
  render: () => renderInputsForSize("md", "Flibber"),
};

export const Large: Story = {
  name: "Large (lg)",
  render: () => renderInputsForSize("lg", "Prognostication", true),
};

export const ExtraLarge: Story = {
  name: "Extra Large (xl)",
  render: () => renderInputsForSize("xl", "True"),
};
