"use client";

import { cva, type VariantProps } from "class-variance-authority";
import * as React from "react";

import { cn } from "@/lib/utils";

const textInputVariants = cva(
  "inline-flex items-center p-2 justify-center transition-all duration-200 rounded-[12px] focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none",
  {
    variants: {
      variant: {
        default: "bg-white",
        positive: "bg-ui-alert_green_01",
        negative: "bg-ui-alert_red_01",
        selected: "bg-primary-yellow",
      },
      size: {
        sm: "w-20",
        md: "w-[136px]",
        lg: "w-[248px]",
        xl: "w-[272px]",
      },
      isCorrect: {
        true: "bg-white",
        false: "",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "lg",
    },
  }
);

const textBackgroundColorMap: Record<
  NonNullable<VariantProps<typeof textInputVariants>["variant"]>,
  string
> = {
  default: "bg-white",
  positive: "bg-ui-alert_green_02",
  negative: "bg-ui-alert_red_02",
  selected: "bg-tertiary-yellow_02",
};

export interface TextInputProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof textInputVariants> {
  label: string;
  disabled?: boolean;
  beforeLabel?: React.ReactNode;
}

export const TextInput = React.forwardRef<HTMLDivElement, TextInputProps>(
  (
    {
      label,
      className,
      variant = "default",
      size = "lg",
      disabled = false,
      isCorrect = false,
      beforeLabel,
      ...props
    },
    ref
  ) => {
    const isShadowActive =
      !disabled && variant !== "positive" && variant !== "negative";

    const textSizeMap = {
      sm: "text-3xl leading-none text-center justify-center",
      md: "text-2xl leading-none pt-[14px] pb-[10px]",
      lg: "text-2xl leading-none pt-[14px] pb-[10px]",
      xl: "text-4xl leading-none text-center justify-center",
    } as const;

    const sizeClass = textSizeMap[size ?? "lg"];

    return (
      <div className={cn(isCorrect && "animate-pulse-scale")}>
        <div
          ref={ref}
          className={cn(
            "relative inline-block cursor-pointer border-none bg-transparent p-0 outline-offset-4 transition-all duration-200 group disabled:pointer-events-none",
            disabled &&
              variant !== "positive" &&
              variant !== "negative" &&
              !isCorrect &&
              "opacity-50",
            disabled && "cursor-not-allowed",
            className
          )}
          {...props}
        >
          <div>
            {isShadowActive && (
              <span
                className="absolute top-0 left-0 w-full h-full rounded-[12px] bg-white/20 translate-y-1"
                aria-hidden="true"
              />
            )}

            <span
              className={cn(
                "relative block will-change-transform transform transition-transform duration-500 ease-out select-none rounded-[12px] ",
                isShadowActive ? "group-active:translate-y-1" : "translate-y-0",
                textInputVariants({ variant, size, isCorrect }),
                beforeLabel && "w-[272px]"
              )}
            >
              <div
                className={cn(
                  "w-full overflow-hidden flex items-center gap-3 rounded-[8px] px-3 pt-[11px] pb-[7px] font-semibold whitespace-nowrap",
                  textBackgroundColorMap[variant ?? "default"],
                  sizeClass,
                  beforeLabel && "px-1 pt-[7px] pb-[3px]"
                )}
              >
                {beforeLabel && <div>{beforeLabel}</div>}
                <span className="text-black">{label}</span>
              </div>
            </span>
          </div>
        </div>
      </div>
    );
  }
);

TextInput.displayName = "TextInput";
