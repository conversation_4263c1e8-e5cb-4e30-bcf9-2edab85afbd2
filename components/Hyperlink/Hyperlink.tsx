import styled from "@emotion/styled";
import { useRouter } from "next/navigation";
import { CSSProperties } from "react";

import { Text } from "@/components/Text";
import { theme } from "@/styles/theme";

export const StyledLink = styled.a`
  color: ${theme.colors.primary.purple.hex};
  &:hover {
    text-decoration: underline;
    transition: 1s;
  }
`;

function customLinkOnClick({ event, href, router }) {
  event.preventDefault();
  router.push(href);
}
export function Hyperlink({
  secondary,
  href,
  label,
  css,
  target = "_self",
}: {
  href: string;
  label: string;
  css?: CSSProperties;
  secondary?: boolean;
  target?: string;
}) {
  const router = useRouter();
  return (
    <StyledLink
      style={
        secondary
          ? {
              fontSize: "inherit",
              color: theme.colors.primary.black.hex,
              textDecoration: "underline",
            }
          : {
              ...css,
            }
      }
      href={href}
      target={target}
      onClick={(e) => customLinkOnClick({ event: e, href, router })}
    >
      {label}
    </StyledLink>
  );
}
