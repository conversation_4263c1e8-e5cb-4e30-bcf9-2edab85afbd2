import { ChevronDownIcon } from "@chakra-ui/icons";
import {
  But<PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>er<PERSON><PERSON>lay,
  HStack,
  Icon,
  Link,
  Menu,
  MenuButton,
  MenuItem,
  MenuList,
  useDisclosure,
  useMediaQuery,
} from "@chakra-ui/react";
import { useSupabaseClient, useUser } from "@supabase/auth-helpers-react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useContext, useRef } from "react";
import {
  IoApps,
  IoLockOpen,
  IoMail,
  IoMenu,
  IoOpenSharp,
  IoSettings,
} from "react-icons/io5";

import { Button } from "@/components";
import { UserContext } from "@/context/user-context";
import { theme } from "@/styles/theme";

export function Nav() {
  const user = useUser();
  const { user: userContext, clearUser } = useContext(UserContext);
  const supabaseClient = useSupabaseClient();
  const { push } = useRouter();
  const { isOpen, onOpen, onClose } = useDisclosure();
  const btnRef = useRef(null);
  const [isMobileWidth] = useMediaQuery(
    `(max-width: ${theme.breakpoints.portable})`
  );
  return (
    <HStack
      p={{ base: theme.spacing.xs.rem, md: theme.spacing.sm.rem }}
      justify={"space-between"}
      w={"100%"}
      h={"64px"}
    >
      <Link href="/">
        <Image
          src={`/images/logos/talamo-logo.svg`}
          width="131"
          height="30"
          alt="Talamo logo"
        />
      </Link>
      {isMobileWidth ? (
        <>
          <ChakraButton ref={btnRef} onClick={onOpen}>
            <IoMenu />
          </ChakraButton>
          <Drawer
            isOpen={isOpen}
            placement="right"
            onClose={onClose}
            finalFocusRef={btnRef}
          >
            <DrawerOverlay />
            <DrawerContent>
              <DrawerCloseButton />
              <DrawerHeader>
                {user?.email && user?.email?.length > 15
                  ? user?.email?.slice(0, 13) + "..."
                  : user?.email}
              </DrawerHeader>

              <DrawerBody>
                {/* <Input placeholder="Type here..." /> */}
              </DrawerBody>

              <DrawerFooter>
                <Button variant="secondary" mr={3} onClick={onClose}>
                  Cancel
                </Button>
                <Button colorScheme="blue">Save</Button>
              </DrawerFooter>
            </DrawerContent>
          </Drawer>
        </>
      ) : (
        <Menu>
          <MenuButton as={ChakraButton} rightIcon={<ChevronDownIcon />}>
            {user?.email}
          </MenuButton>
          <MenuList>
            <MenuItem onClick={() => push("/dashboard")}>
              <Icon
                as={IoOpenSharp}
                color={theme.colors.primary.purple.hex}
                size={1.5}
                mr={"3"}
              />
              Dashboard
            </MenuItem>
            <MenuItem onClick={() => push("/dashboard/account")}>
              <Icon
                as={IoSettings}
                color={theme.colors.primary.purple.hex}
                size={1.5}
                mr={"3"}
              />
              Account
            </MenuItem>
            <MenuItem>
              <Icon
                as={IoMail}
                size={1.5}
                color={theme.colors.primary.purple.hex}
                mr={"3"}
              />
              Support
            </MenuItem>
            <MenuItem
              onClick={() => {
                clearUser();
                supabaseClient.auth.signOut().then((result) => push("/login"));
              }}
            >
              <Icon
                as={IoLockOpen}
                size={"96px"}
                color={theme.colors.primary.purple.hex}
                mr={"3"}
              />
              Logout
            </MenuItem>
          </MenuList>
        </Menu>
      )}
    </HStack>
  );
}
