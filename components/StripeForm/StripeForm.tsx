import { Box } from "@chakra-ui/react";
import {
  PaymentElement,
  useElements,
  useStripe,
} from "@stripe/react-stripe-js";
import { ConfirmPaymentData } from "@stripe/stripe-js";
import { useEffect, useState } from "react";

import { STRIPE_ASSESSMENT_PURCHASE_PRICE } from "@/constants/constants";
import { theme } from "@/styles/theme";

import { PaymentStatus } from "../ModalPayment/PaymentStatus";
import { Button, Text } from "../v2";

declare global {
  interface Window {
    fbq: any;
  }
}

type CheckoutFormProps = {
  confirmParams: ConfirmPaymentData;
  setPaymentSuccess: React.Dispatch<React.SetStateAction<boolean>>;
  setIsModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
};

export const StripeForm = ({
  confirmParams,
  setPaymentSuccess,
  setIsModalOpen,
}: CheckoutFormProps) => {
  const stripe = useStripe();
  const elements = useElements();

  const [message, setMessage] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isReady, setIsReady] = useState(false);
  const [paymentStatus, setPaymentStatus] = useState("payment");

  useEffect(() => {
    if (!stripe) {
      return;
    }

    const clientSecret = new URLSearchParams(window.location.search).get(
      "payment_intent_client_secret"
    );

    if (!clientSecret) {
      return;
    }

    stripe.retrievePaymentIntent(clientSecret).then(({ paymentIntent }) => {
      if (!paymentIntent) {
        return null;
      }

      switch (paymentIntent.status) {
        case "succeeded":
          setMessage("Payment succeeded!");
          break;
        case "processing":
          setMessage("Your payment is processing.");
          break;
        case "requires_payment_method":
          setMessage("Your payment was not successful, please try again.");
          break;
        default:
          setMessage("Something went wrong.");
          break;
      }
    });
  }, [stripe]);

  const handleSubmit = async (e: any) => {
    e.preventDefault();

    if (!stripe || !elements) {
      return;
    }

    setIsLoading(true);

    const { error, paymentIntent } = await stripe.confirmPayment({
      elements,
      redirect: "if_required",
      confirmParams,
    });

    if (error) {
      if (error.type === "card_error" || error.type === "validation_error") {
        setMessage(error.message || "");
      } else {
        setMessage("An unexpected error occured.");
      }
      setIsLoading(false);
      setPaymentStatus("error");
    }

    if (paymentIntent?.status === "succeeded") {
      setPaymentSuccess(true);
      setPaymentStatus("success");
      console.log("Payment succeeded");
      //FB Pixel - Track purchase
      window.fbq("track", "Purchase", {
        value: STRIPE_ASSESSMENT_PURCHASE_PRICE / 100,
        currency: "GBP",
      });
    }
  };

  const handlePaymentElementChange = (event) => {
    if (event.complete) {
      setIsReady(true);
    } else {
      setIsReady(false);
    }
  };

  if (paymentStatus === "error") {
    return (
      <PaymentStatus
        state="error"
        setIsModalOpen={setIsModalOpen}
        failureReason={message}
        setPaymentStatus={setPaymentStatus}
      />
    );
  }

  if (paymentStatus === "success") {
    return (
      <PaymentStatus
        state="success"
        setIsModalOpen={setIsModalOpen}
        setPaymentStatus={setPaymentStatus}
      />
    );
  }

  return (
    <>
      {/* Payment screen */}
      <Box textAlign="center" pt={theme.spacing.lg.px}>
        <Text element="h3" variant="xl" mb={theme.spacing.md.px}>
          Unlock your screener
        </Text>
        <Text element="h3" variant="md">
          Total cost:
        </Text>
        <Text element="p" variant="xl" mb={theme.spacing.lg.px}>
          1 x dyslexia screener @{" "}
          <strong>£{STRIPE_ASSESSMENT_PURCHASE_PRICE / 100}</strong>
        </Text>
      </Box>
      <form id="payment-form" onSubmit={handleSubmit}>
        <Box my={"20px"} minH="180px">
          <PaymentElement
            id="payment-element"
            onChange={handlePaymentElementChange}
          />
        </Box>
        <Box my="20px" display="flex" justifyContent="center">
          <Button
            disabled={isLoading || !stripe || !elements}
            size="md"
            type="submit"
            id="submit"
            isDisabled={!isReady}
          >
            {isLoading
              ? "Loading"
              : `Pay £${STRIPE_ASSESSMENT_PURCHASE_PRICE / 100}`}
          </Button>
        </Box>
      </form>
    </>
  );
};
