"use client";

import { DragAudioV2 } from "@/components/ui/drag-audio-v2/drag-audio-v2";
import { ImageResponse } from "@/components/ui/responses/image-response";
import { KeyboardResponse } from "@/components/ui/responses/keyboard-response";
import { TextGrid } from "@/components/ui/responses/text-grid";
import { TextResponse } from "@/components/ui/responses/text-response";
import { useResponseDisabledStore } from "@/stores/use-response-disabled-store";
import { NovaScaleItemRecord } from "@/types/graphql/graphql";

interface ResponseSelectorProps {
  scaleItem: NovaScaleItemRecord | null;
  onChange?: (selected: string[]) => void;
  showFeedback?: boolean;
  selected?: string[];
  isCorrect?: boolean | null;
  resetKey?: number;
}

export const ResponseSelector = ({
  scaleItem,
  onChange,
  showFeedback,
  selected,
  isCorrect,
  resetKey,
}: ResponseSelectorProps) => {
  const responseDisabled = useResponseDisabledStore((s) => s.responseDisabled);

  if (!scaleItem) return null;

  const { responseType } = scaleItem;

  switch (responseType) {
    case "image_small":
    case "image_large":
      return (
        <ImageResponse
          scaleItem={scaleItem}
          onChange={onChange}
          showFeedback={showFeedback}
          selected={selected}
        />
      );
    case "text_small":
    case "text_medium":
    case "text_large":
    case "text_audio":
      return (
        <TextResponse
          scaleItem={scaleItem}
          onChange={onChange}
          showFeedback={showFeedback}
          selected={selected}
        />
      );
    case "text_grid":
      return (
        <TextGrid
          scaleItem={scaleItem}
          onChange={onChange}
          showFeedback={showFeedback}
          selected={selected}
          resetKey={resetKey}
        />
      );
    case "keyboard_letters":
    case "keyboard_digits":
      return (
        <KeyboardResponse
          scaleItem={scaleItem}
          onChange={onChange}
          showFeedback={showFeedback}
          selected={selected}
          disabled={responseDisabled}
          isCorrect={isCorrect}
        />
      );

    case "drag_audio":
      return (
        <DragAudioV2
          scaleItem={scaleItem}
          onReorder={onChange}
          showFeedback={showFeedback}
          selected={selected}
          disabled={responseDisabled}
          isCorrect={isCorrect}
        />
      );
    default:
      return <div style={{ color: "red" }}>Coming soon: {responseType}</div>;
  }
};
