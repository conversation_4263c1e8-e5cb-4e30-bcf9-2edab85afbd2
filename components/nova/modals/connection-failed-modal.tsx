"use client";

import Image from "next/image";
import { useRouter } from "next/navigation";

import { Button } from "@/components/ui";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog/dialog";
import { useAssessmentResponsesStore } from "@/stores/use-assessment-responses";
import { useScaleTimerStore } from "@/stores/use-scale-timer-store";

interface ConnectionFailedModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  assessmentId: string;
  scaleId?: string;
}

export const ConnectionFailedModal: React.FC<ConnectionFailedModalProps> = ({
  open,
  onOpenChange,
  assessmentId,
  scaleId,
}) => {
  const router = useRouter();
  const removeScale = useAssessmentResponsesStore((s) => s.removeScale);

  if (!scaleId) {
    return null;
  }

  const exitActivityHandler = () => {
    removeScale(scaleId);
    useScaleTimerStore.getState().resetTimer();
    useScaleTimerStore.getState().stopTimer();
    router.push(`/assessment/${assessmentId}`);
  };
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <div className="flex justify-center mb-6">
            <Image
              src="/images/modals/quit-modal-character.svg"
              width={160}
              height={160}
              alt=""
            />
          </div>
          <DialogTitle>Uh oh!</DialogTitle>
          <DialogDescription>
            There seems to be an issue with the internet. Ask for help, then try
            again.
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button variant="negative" light onClick={exitActivityHandler}>
            Exit activity
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
