"use client";

import Image from "next/image";
import { useRouter } from "next/navigation";

import { Button } from "@/components/ui";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog/dialog";

interface TimesUpModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  assessmentId: string;
  scaleId?: string;
  onFinish: () => void;
}

export const TimesUpModal: React.FC<TimesUpModalProps> = ({
  open,
  onOpenChange,
  assessmentId,
  scaleId,
  onFinish,
}) => {
  const router = useRouter();

  if (!scaleId) {
    return null;
  }

  const finishActivityHandler = () => {
    onOpenChange(false);
    onFinish();
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        onEscapeKeyDown={(e) => e.preventDefault()}
        onInteractOutside={(e) => e.preventDefault()}
      >
        <DialogHeader>
          <div className="flex justify-center mb-6">
            <Image
              src="/images/modals/quit-modal-character.svg"
              width={160}
              height={160}
              alt=""
            />
          </div>
          <DialogTitle>Time’s up!</DialogTitle>
          <DialogDescription>
            Nice work. Click continue to move on to the next activity.
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button variant="default" light onClick={finishActivityHandler}>
            Finish activity
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
