"use client";

import Image from "next/image";
import { useRouter } from "next/navigation";

import { Button } from "@/components/ui";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog/dialog";

interface AssessmentExitModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const AssessmentExitModal: React.FC<AssessmentExitModalProps> = ({
  open,
  onOpenChange,
}) => {
  const router = useRouter();

  const quitAssessmentHandler = () => {
    onOpenChange(false);
    router.push("/dashboard");
  };
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <div className="flex justify-center mb-6">
            <Image
              src="/images/modals/quit-modal-character.svg"
              width={160}
              height={160}
              alt=""
            />
          </div>
          <DialogTitle>Exit Talamo?</DialogTitle>
          <DialogDescription>Your progress will be saved.</DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button variant="default" light onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button variant="negative" light onClick={quitAssessmentHandler}>
            Exit
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
