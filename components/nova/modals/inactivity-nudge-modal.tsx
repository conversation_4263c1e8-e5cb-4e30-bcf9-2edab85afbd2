"use client";

import Image from "next/image";
import React, { useEffect, useRef, useState } from "react";

import { Button } from "@/components/ui";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog/dialog";

interface InactivityNudgeModalProps {
  show: boolean;
  variant: "scale" | "dashboard";
  onCancel: () => void;
  onLogout: () => void;
}

export const InactivityNudgeModal = React.memo(
  ({
    show,
    onCancel,
    onLogout,
    variant = "dashboard",
  }: InactivityNudgeModalProps) => {
    const [countdown, setCountdown] = useState(10);
    const countdownRef = useRef<NodeJS.Timeout | null>(null);

    useEffect(() => {
      if (!show) {
        clearInterval(countdownRef.current!);
        return;
      }

      countdownRef.current = setInterval(() => {
        setCountdown((prev) => {
          if (prev <= 1) {
            clearInterval(countdownRef.current!);
            onLogout();
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

      return () => clearInterval(countdownRef.current!);
    }, [show, onLogout]);

    return (
      <Dialog open={show} onOpenChange={onCancel}>
        <DialogContent>
          <DialogHeader>
            <div className="flex justify-center mb-6">
              <Image
                src="/images/modals/nudge-modal-character.svg"
                width={160}
                height={160}
                alt=""
              />
            </div>
            <DialogTitle>Still there?</DialogTitle>
            {variant === "dashboard" && (
              <DialogDescription>
                You haven’t done anything in a while. We’ll log you out in{" "}
                <span className="font-semibold">{countdown}</span> seconds...
              </DialogDescription>
            )}
            {variant === "scale" && (
              <DialogDescription>
                You haven’t done anything in a while. We’ll exit this activity
                in <span className="font-semibold">{countdown}</span> seconds...
              </DialogDescription>
            )}
          </DialogHeader>

          <DialogFooter>
            <Button
              variant="default"
              light
              autoFocus={false}
              onClick={() => {
                onCancel();
                setCountdown(10);
              }}
            >
              I’m here!
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  }
);

InactivityNudgeModal.displayName = "InactivityNudgeModal";
