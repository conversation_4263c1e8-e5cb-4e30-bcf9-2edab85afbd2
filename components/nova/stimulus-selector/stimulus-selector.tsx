"use client";

import React from "react";

import { AudioStimulus } from "@/components/ui/stimulus/audio-stimulus";
import { ImageStimulus } from "@/components/ui/stimulus/image-stimulus";
import { TextStimulus } from "@/components/ui/stimulus/text-stimulus";
import { TimedTextStimulus } from "@/components/ui/stimulus/timed-text-stimulus";
import { NovaScaleItemRecord } from "@/types/graphql/graphql";

interface StimulusSelectorProps {
  scaleItem: NovaScaleItemRecord | null;
}

export const StimulusSelector = ({ scaleItem }: StimulusSelectorProps) => {
  if (!scaleItem) return null;

  const { stimulusType } = scaleItem;

  switch (stimulusType) {
    case "audio":
    case "audio_text":
      return <AudioStimulus key={scaleItem?.id} scaleItem={scaleItem} />;
    case "text_large":
    case "text_small":
      return <TextStimulus key={scaleItem?.id} scaleItem={scaleItem} />;
    case "text_audio":
      return <AudioStimulus key={scaleItem?.id} scaleItem={scaleItem} />;
    case "image":
    case "image_text":
      return <ImageStimulus key={scaleItem?.id} scaleItem={scaleItem} />;
    case "timed_text":
      return <TimedTextStimulus key={scaleItem?.id} scaleItem={scaleItem} />;
    default:
      return null;
  }
};
