/* eslint-disable simple-import-sort/imports */
"use client";

import { XMarkIcon } from "@heroicons/react/24/outline";
import { useRouter } from "next/navigation";
import {
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";

import { <PERSON><PERSON>, FeedbackCharacter, NightSkyBg } from "@/components/ui";
import { ScaleTimerWrapper } from "@/components/ui/timer/scale-timer-wrapper";
import { StudentContext } from "@/context/student-context";
import { useAssessmentSubmission } from "@/hooks/assessment/use-assessment-submission.hook";
import { useAssessment } from "@/hooks/assessment/use-assessment.hook";
import { useInactivityNudge } from "@/hooks/assessment/use-inactivity-nudge";
import { useAssessmentResponsesStore } from "@/stores/use-assessment-responses";
import { useAudioStore } from "@/stores/use-audio-store";
import { useResponseDisabledStore } from "@/stores/use-response-disabled-store";
import { useSampleScaleItemStore } from "@/stores/use-sample-scale-item-store";
import { useScaleTimerStore } from "@/stores/use-scale-timer-store";
import { useTimeTakenStore } from "@/stores/use-time-taken-store";
import type { GetNovaAssessmentQuery } from "@/types/graphql/graphql";

import { AssessmentLayout } from "../assessment-layout/assessment-layout";
import { FeedbackBar } from "../feedback-bar/feedback-bar";
import { ConnectionFailedModal } from "../modals/connection-failed-modal";
import { InactivityNudgeModal } from "../modals/inactivity-nudge-modal";
import { ScaleExitModal } from "../modals/scale-exit-modal";
import { TimesUpModal } from "../modals/times-up-modal";
import { ResponseSelector } from "../response-selector/response-selector";
import { ScaleAnimation } from "../scale-animation/scale-animation";
import { StimulusSelector } from "../stimulus-selector/stimulus-selector";
import { isAgeInRelevantRange } from "./utils/is-age-in-relevant-range";
import { isScaleItemRelevantForAge } from "./utils/is-scale-item-relevant-for-age";

interface ScaleProps {
  assessmentData: GetNovaAssessmentQuery["novaAssessment"];
  scaleId: string;
  scaleItemIds: string[];
  assessmentId: string;
}

export const Scale = ({
  assessmentData,
  scaleId,
  scaleItemIds,
  assessmentId,
}: ScaleProps) => {
  const resetScaleTimer = useScaleTimerStore((s) => s.resetTimer);
  const startScaleTimer = useScaleTimerStore((s) => s.startTimer);
  const stopScaleTimer = useScaleTimerStore((s) => s.stopTimer);
  const resumeScaleTimer = useScaleTimerStore((s) => s.resumeTimer);
  const pauseScaleTimer = useScaleTimerStore((s) => s.pauseTimer);

  const startTimeTakenTimer = useTimeTakenStore((s) => s.startTimer);
  const stopTimeTakenTimer = useTimeTakenStore((s) => s.stopTimer);
  const resetTimeTakenTimer = useTimeTakenStore((s) => s.resetTimer);

  const { student } = useContext(StudentContext);

  const [selectedResponses, setSelectedResponses] = useState<string[]>([]);
  const [showFeedback, setShowFeedback] = useState(false);
  const [isCorrect, setIsCorrect] = useState<boolean | null>(null);
  const [hasSubmitted, setHasSubmitted] = useState(false);
  const [isAnimatingOut, setIsAnimatingOut] = useState(false);
  const [activeItemIndex, setActiveItemIndex] = useState(0);
  const [showTutorialFeedback, setShowTutorialFeedback] = useState(false);
  const [showIntro, setShowIntro] = useState(activeItemIndex === 0);
  const [showOutro, setShowOutro] = useState(false);
  const [consecutiveIncorrectCount, setConsecutiveIncorrectCount] = useState(0);
  const [skipStage, setSkipStage] = useState<"initial" | "confirm">("initial");
  const [exitModalOpen, setExitModalOpen] = useState(false);
  const [_connectionFailedModalOpen, setConnectionFailedModalOpen] =
    useState(false);
  const [timesUpModalOpen, setTimesUpModalOpen] = useState(false);
  const [hasTimedOut, setHasTimedOut] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [resetKey, setResetKey] = useState(0);

  const hasShownTutorialRef = useRef<string | null>(null);
  const hasRedirectedRef = useRef(false);
  const continueClickedRef = useRef(false);
  const skipClickedRef = useRef(false);

  const removeScale = useAssessmentResponsesStore((s) => s.removeScale);

  const router = useRouter();

  const { currentScale: currentScaleRaw } = useAssessment({
    assessmentData,
    scaleId,
    scaleItemIndex: activeItemIndex,
  });

  const filteredScaleItemIds = useMemo(() => {
    if (!currentScaleRaw || student?.age == null) return [];

    return scaleItemIds.filter((id) => {
      const item = currentScaleRaw.scaleContent.find(
        (i) => i.__typename === "NovaScaleItemRecord" && i.id === id
      ) as
        | Extract<
            (typeof currentScaleRaw.scaleContent)[number],
            { __typename: "NovaScaleItemRecord" }
          >
        | undefined;

      if (!item) return false;

      return isScaleItemRelevantForAge({
        item: {
          relevantAges: item.relevantAges as string[] | null,
        },
        age: student.age,
        practiceItem: item.practiceItem ?? false,
      });
    });
  }, [currentScaleRaw, scaleItemIds, student?.age]);

  const { currentScaleItem } = useAssessment({
    assessmentData,
    scaleId,
    scaleItemIndex: activeItemIndex,
    filteredScaleItemIds,
  });
  const currentScale = currentScaleRaw;

  const handleNudgeLogout = useCallback(() => {
    removeScale(scaleId);
    resetScaleTimer();
    stopScaleTimer();
    router.push(`/assessment/${assessmentId}`);
  }, [
    removeScale,
    resetScaleTimer,
    stopScaleTimer,
    router,
    assessmentId,
    scaleId,
  ]);

  const { shouldShowModal, cancel } = useInactivityNudge(
    currentScale?.nudge ?? false,
    currentScale?.nudgeTimerDuration ?? 60
  );

  const activeScaleItemId = filteredScaleItemIds[activeItemIndex];

  const {
    submit: submitAssessmentData,
    showRetryFailedModal,
    setShowRetryFailedModal,
  } = useAssessmentSubmission({
    assessmentData,
    scaleId,
    scaleItemId: activeScaleItemId,
    filteredScaleItemIds,
  });

  const triggerPlayStimulus = useSampleScaleItemStore(
    (s) => s.triggerPlayStimulus
  );

  const replayStimulus = useSampleScaleItemStore((s) => s.replayStimulus);

  const previousScaleId = useScaleTimerStore((s) => s.previousScaleId);
  const resetResponseDisabled = useResponseDisabledStore((s) => s.reset);
  const responseDisabled = useResponseDisabledStore((s) => s.responseDisabled);

  const { play: playAudio, stop: stopAudio } = useAudioStore();

  const { assessment } = useAssessmentResponsesStore();

  useEffect(() => {
    if (!assessmentData) return;
    const { id, displayName } = assessmentData;
    if (!assessment) {
      useAssessmentResponsesStore
        .getState()
        .initAssessment({ id, name: displayName });
    }
  }, [assessment, assessmentData]);

  const skippedLoggedRef = useRef(false);

  useEffect(() => {
    if (
      skippedLoggedRef.current ||
      !assessment ||
      !student?.age ||
      !currentScale
    ) {
      return;
    }

    const skippedItems = currentScale.scaleContent
      .filter(
        (
          item
        ): item is Extract<
          typeof item,
          { __typename: "NovaScaleItemRecord" }
        > => item.__typename === "NovaScaleItemRecord"
      )
      .filter((item) => !filteredScaleItemIds.includes(item.id));

    skippedItems.forEach((item) => {
      useAssessmentResponsesStore.getState().addItemResponse({
        scaleId: currentScale.id,
        scaleName: currentScale.displayName,
        item: {
          id: item.id,
          name: item.internalId,
          practice: item.practiceItem ?? false,
          seen: false,
          skipped: false,
          timeTakenMs: 0,
          score: 1,
          answer: [],
        },
      });
    });

    skippedLoggedRef.current = true;
  }, [assessment, student?.age, currentScale]);

  const minRequired = currentScaleItem?.minimumResponsesRequired ?? 1;
  const isPractice = !!currentScaleItem?.practiceItem;
  const isTutorial = !!currentScaleItem?.tutorial;
  const isTutorialOrPractice = isPractice || isTutorial;
  const showTutorialOverlay = isTutorial && showTutorialFeedback && !isPractice;
  const showPracticeOverlay = isPractice && showTutorialFeedback;
  const hasMinResponses = selectedResponses.length >= minRequired;
  const kickOutThreshold =
    currentScale?.kickOut === true
      ? (currentScale?.kickOutThreshold ?? 3)
      : null;
  const showTutorial = useMemo(() => {
    if (!currentScaleItem?.tutorial || student?.age == null) return false;

    return isAgeInRelevantRange(
      student.age,
      currentScaleItem.relevantAges as string[] | null
    );
  }, [
    currentScaleItem?.tutorial,
    currentScaleItem?.relevantAges,
    student?.age,
  ]);
  const isLastItem = activeItemIndex === filteredScaleItemIds.length - 1;

  const outroAnimationLengthMs = 3000;
  const introAnimationLengthMs = 4500;

  useEffect(() => {
    if (!currentScale || hasTimedOut || isPractice || exitModalOpen) return;
    const duration = currentScale.scaleTimerDuration ?? null;
    const hasScaleTimer = currentScale.scaleTimer;
    if (hasScaleTimer && duration && currentScale.id !== previousScaleId) {
      startScaleTimer(duration, currentScale.id, isTutorialOrPractice);
    } else if (!hasScaleTimer) {
      stopScaleTimer();
    }
  }, [
    currentScale,
    previousScaleId,
    startScaleTimer,
    stopScaleTimer,
    isTutorialOrPractice,
    isPractice,
    hasTimedOut,
    exitModalOpen,
  ]);

  useEffect(() => {
    if (isTutorialOrPractice) {
      pauseScaleTimer();
    } else {
      resumeScaleTimer();
    }
  }, [
    isTutorialOrPractice,
    activeItemIndex,
    pauseScaleTimer,
    resumeScaleTimer,
  ]);

  useEffect(() => {
    resetTimeTakenTimer();
  }, [activeItemIndex]);

  useEffect(() => {
    if (!isTutorial) {
      startTimeTakenTimer();
    }
  }, [activeItemIndex, isTutorial]);

  useEffect(() => setShowTutorialFeedback(false), [activeItemIndex]);

  useEffect(() => {
    if (
      isTutorialOrPractice &&
      currentScaleItem?.tutorialText &&
      showTutorial &&
      currentScaleItem.id !== hasShownTutorialRef.current
    ) {
      setShowTutorialFeedback(true);
      hasShownTutorialRef.current = currentScaleItem.id;
    }
  }, [isTutorialOrPractice, currentScaleItem, showTutorial]);

  useEffect(() => {
    if (
      !showIntro &&
      showTutorialFeedback &&
      currentScaleItem?.tutorialAudio?.url &&
      showTutorial &&
      currentScaleItem?.id === hasShownTutorialRef.current
    ) {
      playAudio(currentScaleItem.tutorialAudio.url);
    }
  }, [
    showIntro,
    showTutorialFeedback,
    currentScaleItem?.id,
    currentScaleItem?.tutorialAudio?.url,
    showTutorial,
    playAudio,
  ]);

  useEffect(() => {
    if (activeItemIndex === 0) {
      pauseScaleTimer();
      stopTimeTakenTimer();
      const timeout = setTimeout(() => {
        setShowIntro(false);
        resumeScaleTimer();
        if (!isTutorialOrPractice) {
          startTimeTakenTimer();
        }
      }, introAnimationLengthMs);
      return () => clearTimeout(timeout);
    }
  }, [
    activeItemIndex,
    isTutorialOrPractice,
    pauseScaleTimer,
    resumeScaleTimer,
    startTimeTakenTimer,
    stopTimeTakenTimer,
  ]);

  useEffect(() => {
    if (exitModalOpen) {
      pauseScaleTimer();
    } else if (
      !isTutorialOrPractice &&
      !showIntro &&
      !showOutro &&
      !timesUpModalOpen &&
      !hasTimedOut
    ) {
      resumeScaleTimer();
    }
  }, [
    exitModalOpen,
    pauseScaleTimer,
    resumeScaleTimer,
    isTutorialOrPractice,
    showIntro,
    showOutro,
    timesUpModalOpen,
    hasTimedOut,
  ]);

  const calculateIsCorrect = useCallback((): boolean => {
    if (!currentScaleItem) return false;
    const correctAnswers =
      currentScaleItem.correctAnswer?.split(",").map((s) => s.trim()) ?? [];

    if (
      currentScaleItem.responseType === "keyboard_letters" ||
      currentScaleItem.responseType === "keyboard_numbers" ||
      currentScaleItem.responseType === "keyboard_digits"
    ) {
      return (
        selectedResponses.length === correctAnswers.length &&
        selectedResponses.every(
          (val, idx) =>
            val?.toLowerCase() === correctAnswers[idx]?.toLowerCase()
        )
      );
    }

    if (
      currentScaleItem.responseType?.startsWith("text") ||
      currentScaleItem.responseType === "text_audio"
    ) {
      const selectedSet = new Set(selectedResponses);
      const correctSet = new Set(correctAnswers);
      return (
        selectedSet.size === correctSet.size &&
        [...selectedSet].every((val) => correctSet.has(val))
      );
    }

    if (
      currentScaleItem.responseType === "image_small" ||
      currentScaleItem.responseType === "image_large"
    ) {
      const sortedSelected = [...selectedResponses].sort();
      const sortedCorrect = [...correctAnswers].sort();

      return (
        sortedSelected.length === sortedCorrect.length &&
        sortedSelected.every((val, idx) => val === sortedCorrect[idx])
      );
    }

    if (currentScaleItem.responseType === "drag_audio") {
      return (
        selectedResponses.length === correctAnswers.length &&
        selectedResponses.every((val, idx) => val === correctAnswers[idx])
      );
    }

    const correctSelections = currentScaleItem.responseMedia
      .filter((_, idx) => correctAnswers.includes((idx + 1).toString()))
      .map((item) => item.url);
    const selectedSet = new Set(selectedResponses);
    const correctSet = new Set(correctSelections);
    return (
      selectedSet.size === correctSet.size &&
      [...selectedSet].every((val) => correctSet.has(val))
    );
  }, [currentScaleItem, selectedResponses]);

  const handleResponseChange = useCallback(
    (selected: string[]) => {
      setSelectedResponses(selected);
      setHasSubmitted(false);
      setIsCorrect(null);
      setShowFeedback(selected.length >= minRequired);
    },
    [minRequired]
  );

  const handleCheckAnswers = async () => {
    const correct = calculateIsCorrect();
    setIsCorrect(correct);
    setHasSubmitted(true);
  };

  const handleContinue = async () => {
    if (continueClickedRef.current) return;
    continueClickedRef.current = true;

    if (isLastItem) {
      setIsSaving(true);
    }

    const correct = calculateIsCorrect();
    setIsCorrect(correct);
    await new Promise((res) => setTimeout(res, 600));
    setIsAnimatingOut(true);

    const updatedConsecutiveIncorrect =
      !correct && !isPractice ? consecutiveIncorrectCount + 1 : 0;

    const shouldRedirect =
      kickOutThreshold !== null &&
      updatedConsecutiveIncorrect >= kickOutThreshold;

    const result = await submitAssessmentData({
      selectedResponses,
      isCorrect: correct,
      skipped: false,
      reasonForExit: shouldRedirect ? "kickout" : undefined,
    });
    if (!result.success) {
      continueClickedRef.current = false;
      setIsAnimatingOut(false);
      return;
    }

    if (!isPractice) {
      setConsecutiveIncorrectCount(updatedConsecutiveIncorrect);
    }

    if (shouldRedirect && !hasRedirectedRef.current) {
      setShowOutro(true);
      stopAudio();
      stopScaleTimer();
      stopTimeTakenTimer();

      if (currentScale) {
        useAssessmentResponsesStore.getState().completeScale({
          scaleId: currentScale.id,
          reasonForExit: "kickout",
        });
      }

      hasRedirectedRef.current = true;
      setIsSaving(false);

      setTimeout(() => {
        router.push(`/assessment/${assessmentId}`);
      }, outroAnimationLengthMs);
      return;
    }

    const hasNextItem = activeItemIndex < filteredScaleItemIds.length - 1;
    if (hasNextItem) {
      setActiveItemIndex((prev) => prev + 1);
    } else {
      setShowOutro(true);
      stopAudio();
      stopScaleTimer();
      stopTimeTakenTimer();
      setTimeout(() => {
        router.push(`/assessment/${assessmentId}`);
      }, outroAnimationLengthMs);
      return;
    }

    setSelectedResponses([]);
    setHasSubmitted(false);
    setIsCorrect(null);
    setShowFeedback(false);
    setIsAnimatingOut(false);
    resetResponseDisabled();

    setTimeout(() => {
      continueClickedRef.current = false;
    }, 1000);
  };

  const handleSkip = async () => {
    if (isPractice || skipClickedRef.current) return;
    skipClickedRef.current = true;

    setIsCorrect(false);
    setIsAnimatingOut(true);

    const updatedConsecutiveIncorrect = !isPractice
      ? consecutiveIncorrectCount + 1
      : 0;
    const shouldRedirect =
      kickOutThreshold !== null &&
      updatedConsecutiveIncorrect >= kickOutThreshold;

    const result = await submitAssessmentData({
      selectedResponses: [],
      isCorrect: false,
      skipped: true,
      reasonForExit: shouldRedirect ? "kickout" : undefined,
    });
    if (!result.success) {
      setIsAnimatingOut(false);
      setTimeout(() => {
        skipClickedRef.current = false;
      }, 1000);
      return;
    }

    if (!isPractice) {
      setConsecutiveIncorrectCount(updatedConsecutiveIncorrect);
    }

    if (shouldRedirect && !hasRedirectedRef.current) {
      setShowOutro(true);
      stopAudio();
      stopScaleTimer();
      stopTimeTakenTimer();

      useAssessmentResponsesStore.getState().completeScale({
        scaleId: currentScale?.id ?? "",
        reasonForExit: "kickout",
      });

      hasRedirectedRef.current = true;
      setTimeout(() => {
        router.push(`/assessment/${assessmentId}`);
      }, outroAnimationLengthMs);
      setTimeout(() => {
        skipClickedRef.current = false;
      }, 1000);
      return;
    }

    const hasNextItem = activeItemIndex < filteredScaleItemIds.length - 1;
    if (hasNextItem) {
      setActiveItemIndex((prev) => prev + 1);
      setTimeout(() => {
        setSkipStage("initial");
      }, 500);
    } else {
      setShowOutro(true);
      stopAudio();
      stopScaleTimer();
      stopTimeTakenTimer();
      setTimeout(() => {
        router.push(`/assessment/${assessmentId}`);
      }, outroAnimationLengthMs);
      setTimeout(() => {
        skipClickedRef.current = false;
      }, 1000);
      return;
    }

    setSelectedResponses([]);
    setHasSubmitted(false);
    setIsCorrect(null);
    setShowFeedback(false);
    setIsAnimatingOut(false);
    resetResponseDisabled();
    setTimeout(() => {
      skipClickedRef.current = false;
    }, 1000);
  };

  const handleTutorialDismiss = () => {
    stopAudio();
    resumeScaleTimer();
    resetTimeTakenTimer();
    startTimeTakenTimer();
    setShowTutorialFeedback(false);
    triggerPlayStimulus(currentScaleItem?.id ?? "");
  };

  const handleScaleTimerExit = async () => {
    if (hasTimedOut) return;
    setHasTimedOut(true);

    stopAudio();
    stopScaleTimer();
    stopTimeTakenTimer();

    await submitAssessmentData({
      selectedResponses: [],
      isCorrect: false,
      skipped: true,
      reasonForExit: "timeout",
    });

    setTimesUpModalOpen(true);
  };

  useEffect(() => {
    if (
      showOutro &&
      !hasTimedOut &&
      kickOutThreshold !== null &&
      consecutiveIncorrectCount >= kickOutThreshold &&
      !hasRedirectedRef.current
    ) {
      hasRedirectedRef.current = true;

      (async () => {
        const result = await submitAssessmentData({
          selectedResponses: [],
          isCorrect: false,
          skipped: true,
          reasonForExit: "kickout",
        });
        if (!result.success) {
          return;
        }
        useAssessmentResponsesStore.getState().completeScale({
          scaleId: currentScale?.id ?? "",
          reasonForExit: "kickout",
        });
        const timeout = setTimeout(() => {
          router.push(`/assessment/${assessmentId}`);
        }, outroAnimationLengthMs);
        return () => clearTimeout(timeout);
      })();
    }
  }, [
    showOutro,
    consecutiveIncorrectCount,
    kickOutThreshold,
    assessmentId,
    router,
    submitAssessmentData,
    currentScale?.id,
    hasTimedOut,
  ]);

  useEffect(() => {
    setSkipStage("initial");
  }, [activeItemIndex]);

  useEffect(() => {
    if (!currentScale?.scaleTimer) {
      stopScaleTimer();
      resetScaleTimer();
    }
  }, [currentScale?.id, stopScaleTimer, resetScaleTimer]);

  if (showIntro)
    return (
      <ScaleAnimation scaleData={currentScale} animationType="countdown" />
    );

  if (showOutro)
    return <ScaleAnimation scaleData={currentScale} animationType="complete" />;

  return (
    <>
      {showTutorialFeedback && currentScaleItem?.tutorialText && (
        <div className="absolute top-4 left-4 z-50">
          <FeedbackCharacter
            characterUrl={currentScaleItem.tutorialImage?.url}
            message={currentScaleItem.tutorialText}
            isActive
          />
        </div>
      )}
      <AssessmentLayout
        isTutorialItem={showTutorialOverlay}
        isPracticeItem={showPracticeOverlay}
        stimulusType={currentScaleItem?.stimulusType}
        top={
          currentScale?.scaleTimer ? (
            <ScaleTimerWrapper onTimerExpired={handleScaleTimerExit} />
          ) : null
        }
        stimulus={
          !isAnimatingOut && (
            <div key={`stimulus-${activeScaleItemId}`}>
              <StimulusSelector scaleItem={currentScaleItem} />
            </div>
          )
        }
        response={
          !isAnimatingOut && (
            <div key={`response-${activeScaleItemId}`}>
              <ResponseSelector
                scaleItem={currentScaleItem}
                onChange={handleResponseChange}
                showFeedback={showFeedback && hasSubmitted}
                selected={selectedResponses}
                isCorrect={isCorrect}
                resetKey={resetKey}
              />
            </div>
          )
        }
        bottom={
          showTutorialFeedback ? (
            <div key="tutorial-feedback-button">
              <Button
                onClick={handleTutorialDismiss}
                variant="default"
                pulsate={isPractice}
              >
                {isPractice ? "Start practice" : "Start"}
              </Button>
            </div>
          ) : currentScaleItem?.feedback ? (
            !hasSubmitted ? (
              <div key="check-button">
                <Button
                  onClick={handleCheckAnswers}
                  variant="default"
                  disabled={!showFeedback}
                >
                  Check
                </Button>
              </div>
            ) : (
              <div key={`feedback-${isCorrect ? "correct" : "incorrect"}`}>
                <FeedbackBar
                  scaleItem={currentScaleItem}
                  isCorrect={!!isCorrect}
                />
                {currentScaleItem.ifIncorrectAction === "reset" &&
                !isCorrect ? (
                  <Button
                    onClick={() => {
                      replayStimulus(currentScaleItem.id);
                      setSelectedResponses([]);
                      setHasSubmitted(false);
                      setIsCorrect(null);
                      setShowFeedback(false);
                      setResetKey((prev) => prev + 1);
                    }}
                    variant="negative"
                  >
                    Try again
                  </Button>
                ) : (
                  <Button
                    onClick={handleContinue}
                    variant={
                      isCorrect === true
                        ? "positive"
                        : isCorrect === false
                          ? "negative"
                          : "default"
                    }
                  >
                    Continue
                  </Button>
                )}
              </div>
            )
          ) : selectedResponses.length === 0 && currentScaleItem?.skippable ? (
            <div key={`skip-button${currentScale?.id}`}>
              <Button
                onClick={async () => {
                  if (skipStage === "initial") {
                    setSkipStage("confirm");
                  } else {
                    handleSkip();
                  }
                }}
                variant={"negative"}
              >
                {skipStage === "confirm" ? "Are you sure?" : "Skip"}
              </Button>
            </div>
          ) : (
            <div key="continue-button" className="flex flex-col">
              <Button
                onClick={handleContinue}
                variant="default"
                disabled={!hasMinResponses && !isTutorialOrPractice}
              >
                {isSaving && isLastItem ? "Saving..." : "Continue"}
              </Button>
            </div>
          )
        }
      />
      <NightSkyBg />
      <div className="fixed top-5 left-5">
        <Button
          size="icon"
          variant="negative"
          onClick={() => setExitModalOpen(true)}
        >
          <XMarkIcon width={24} height={24} strokeWidth={2.5} />
        </Button>
      </div>
      <ScaleExitModal
        assessmentId={assessmentId}
        open={exitModalOpen}
        onOpenChange={setExitModalOpen}
        scaleId={currentScale?.id}
      />
      <TimesUpModal
        assessmentId={assessmentId}
        open={timesUpModalOpen}
        onOpenChange={setTimesUpModalOpen}
        scaleId={currentScale?.id}
        onFinish={() => {
          setShowOutro(true);
          setTimeout(() => {
            router.push(`/assessment/${assessmentId}`);
          }, outroAnimationLengthMs);
        }}
      />
      <InactivityNudgeModal
        show={shouldShowModal}
        onCancel={cancel}
        onLogout={handleNudgeLogout}
        variant="scale"
      />
      <ConnectionFailedModal
        assessmentId={assessmentId}
        open={showRetryFailedModal}
        onOpenChange={(open) => {
          setShowRetryFailedModal(open);
          setConnectionFailedModalOpen(open);
        }}
        scaleId={currentScale?.id}
      />
    </>
  );
};
