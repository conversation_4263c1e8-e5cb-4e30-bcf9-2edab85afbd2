interface IScaleItemRelevantForAgeInput {
  item: { relevantAges?: string[] | null };
  age: number;
  practiceItem?: boolean;
}

export const isScaleItemRelevantForAge = ({
  item,
  age,
  practiceItem = false,
}: IScaleItemRelevantForAgeInput): boolean => {
  if (!Array.isArray(item?.relevantAges)) return false;

  return item.relevantAges.some((range) => {
    if (typeof range !== "string") return false;

    if (range.endsWith("+")) {
      const min = parseInt(range.replace("+", ""), 10);
      return !isNaN(min) && (practiceItem ? age >= min : age <= min);
    }

    const [minStr, maxStr] = range.split("-");
    const min = parseInt(minStr, 10);
    const max = parseInt(maxStr, 10);
    if (isNaN(min) || isNaN(max)) return false;

    return practiceItem ? age >= min && age <= max : age <= max;
  });
};
