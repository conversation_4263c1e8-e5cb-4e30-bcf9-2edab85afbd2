export const isAgeInRelevantRange = (
  age: number,
  relevantAges: string[] | null | undefined
): boolean => {
  if (!Array.isArray(relevantAges)) return false;

  return relevantAges.some((range) => {
    if (typeof range !== "string") return false;

    if (range.endsWith("+")) {
      const min = parseInt(range.replace("+", ""), 10);
      return !isNaN(min) && age >= min;
    }

    const [minStr, maxStr] = range.split("-");
    const min = parseInt(minStr, 10);
    const max = parseInt(maxStr, 10);

    if (isNaN(min) || isNaN(max)) return false;

    return age >= min && age <= max;
  });
};
