"use client";

import {
  PauseCircleIcon,
  PlayIcon,
  XMarkIcon,
} from "@heroicons/react/24/solid";
import Image from "next/image";
import Link from "next/link";
import { useRef, useState } from "react";

import { Button, NightSkyBg } from "@/components/ui";
import { GetNovaAssessmentQuery } from "@/types/graphql/graphql";

interface AssessmentStartProps {
  assessmentId: string;
  assessmentData: GetNovaAssessmentQuery["novaAssessment"];
}

export const AssessmentStart = ({
  assessmentId,
  assessmentData,
}: AssessmentStartProps) => {
  const tutorialVideo = assessmentData?.tutorialVideo?.url;
  const [isVideoPlaying, setIsVideoPlaying] = useState(false);
  const [_hasPaused, setHasPaused] = useState(false);
  const [showReplay, setShowReplay] = useState(false);
  const [hasCompletedFirstPlay, setHasCompletedFirstPlay] = useState(false);
  const videoRef = useRef<HTMLVideoElement | null>(null);

  const toggleVideoPlayback = () => {
    if (!videoRef.current) return;
    if (isVideoPlaying) {
      videoRef.current.pause();
      setIsVideoPlaying(false);
    } else {
      videoRef.current.play();
      setIsVideoPlaying(true);
    }
  };

  return (
    <>
      <div className="fixed top-5 left-5">
        <Link href={`/assessment/${assessmentId}`}>
          <Button size="icon" variant="negative">
            <XMarkIcon width={24} height={24} strokeWidth={2.5} />
          </Button>
        </Link>
      </div>

      {tutorialVideo && (
        <div className="flex flex-col items-center justify-center min-h-screen gap-8">
          <div
            className="relative max-w-4xl w-full aspect-video rounded-[20px] overflow-hidden cursor-pointer mx-auto border-[20px] border-[#44418A]"
            onClick={toggleVideoPlayback}
          >
            <video
              ref={videoRef}
              src={tutorialVideo}
              controls={false}
              className="w-full h-full object-cover"
              onPlay={() => {
                setShowReplay(false);
                setIsVideoPlaying(true);
              }}
              onPause={() => {
                setIsVideoPlaying(false);
                setHasPaused(true);
              }}
              onEnded={() => {
                setShowReplay(true);
                setIsVideoPlaying(false);
                setHasCompletedFirstPlay(true);
              }}
            />

            {!isVideoPlaying && !showReplay && (
              <>
                <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center"></div>
                <div className="absolute inset-0 flex items-center justify-center">
                  <Button size="iconxl" light variant="default">
                    <PlayIcon
                      className="text-purple-600"
                      width={32}
                      height={32}
                    />
                  </Button>
                </div>
              </>
            )}

            {isVideoPlaying && (
              <div className="absolute bottom-5 right-5 pointer-events-none">
                <PauseCircleIcon
                  className="text-white"
                  width={32}
                  height={32}
                />
              </div>
            )}

            {showReplay && (
              <button
                className="absolute inset-0 flex items-center justify-center"
                onClick={(e) => {
                  e.stopPropagation();
                  videoRef.current?.play();
                  setShowReplay(false);
                }}
              >
                <Image
                  src="/images/onboarding/replay.svg"
                  alt="Replay"
                  width={82}
                  height={82}
                />
              </button>
            )}
          </div>
          <Link href={`/assessment/${assessmentId}/`} aria-label="Secret skip">
            <div className="absolute top-0 right-0 w-[20px] h-[20px] bg-transparent z-[99999]" />
          </Link>
          <div>
            <Link href={`/assessment/${assessmentId}/`}>
              <Button
                size="xl"
                className={hasCompletedFirstPlay ? "" : "invisible"}
              >
                Continue
              </Button>
            </Link>
          </div>
        </div>
      )}

      <NightSkyBg />
    </>
  );
};
