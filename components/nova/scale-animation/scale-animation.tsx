import dynamic from "next/dynamic";
import { useEffect, useState } from "react";

import { NightSkyBg } from "@/components/ui";
import { GetNovaAssessmentQuery } from "@/types/graphql/graphql";

const Lottie = dynamic(() => import("lottie-react"), { ssr: false });

type ScaleFromQuery = Extract<
  NonNullable<
    GetNovaAssessmentQuery["novaAssessment"]
  >["assessmentContent"][number],
  { __typename: "NovaScaleRecord" }
>;

interface ScaleAnimationProps {
  scaleData: ScaleFromQuery | null;
  animationType: "countdown" | "complete";
}

export const ScaleAnimation = ({
  scaleData,
  animationType,
}: ScaleAnimationProps) => {
  const [animationData, setAnimationData] = useState<any | null>(null);

  const remoteUrl =
    animationType === "countdown"
      ? scaleData?.countdownAnimation?.url
      : scaleData?.completeAnimation?.url;

  useEffect(() => {
    if (!remoteUrl) return;

    const loadAnimation = async () => {
      const res = await fetch(remoteUrl);
      const json = await res.json();
      setAnimationData(json);
    };

    loadAnimation();
  }, [remoteUrl]);

  return (
    <div className="fixed flex flex-col items-center justify-center h-full w-full text-center">
      {animationData && (
        <div className="w-[480px] h-[480px]">
          <Lottie animationData={animationData} autoplay loop={false} />
        </div>
      )}
      <NightSkyBg />
    </div>
  );
};
