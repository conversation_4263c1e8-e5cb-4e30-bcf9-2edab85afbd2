"use client";

import Image from "next/image";
import Link from "next/link";
import { useContext } from "react";

import { Button } from "@/components/ui";
import { StudentContext } from "@/context/student-context";
import { useAssessmentResponsesStore } from "@/stores/use-assessment-responses";
import { GetNovaAssessmentQuery } from "@/types/graphql/graphql";

interface IStartScreenProps {
  assessmentData: GetNovaAssessmentQuery["novaAssessment"];
}

type CompletedStatus = "none" | "partial" | "complete";

export const StartScreen = ({ assessmentData }: IStartScreenProps) => {
  const responses = useAssessmentResponsesStore.getState().getAssessment();
  const { student, logout } = useContext(StudentContext);
  const assessmentScales = assessmentData?.assessmentContent.filter(
    (content) => content.__typename === "NovaScaleRecord"
  );

  if (!assessmentData) {
    return null;
  }

  const totalScales = assessmentScales?.length ?? 0;
  const totalCompleted = responses?.scales.length ?? 0;

  const completedStatus: CompletedStatus =
    totalCompleted === 0
      ? "none"
      : totalCompleted < totalScales
        ? "partial"
        : "complete";

  let paragraphText = "";
  let buttonText = "Start";

  if (completedStatus === "partial") {
    paragraphText = "Ready to get back into it?";
    buttonText = "Continue";
  } else if (completedStatus === "complete") {
    paragraphText = "Nothing to do today!";
    buttonText = "Logout";
  }

  return (
    <>
      <div className="bg-white text-black  w-[704px] p-[72px] rounded-xxl flex flex-col justify-between px-6 items-center text-center">
        <Image
          src="/images/start/start-icon.svg"
          width={160}
          height={160}
          alt=""
          className="mb-6"
        />

        <h2 className="font-semibold text-5xl mb-4">
          Hi, {student?.first_names} 🙌
        </h2>
        <p className="text-3xl">{paragraphText}</p>
        {completedStatus === "complete" ? (
          <Button
            variant="negative"
            light
            size="xl"
            className="mt-12"
            onClick={logout}
          >
            {buttonText}
          </Button>
        ) : (
          <Link
            href={`/assessment/${assessmentData.id}/${completedStatus === "none" ? "start/" : ""}`}
          >
            <Button light size="xl" className="mt-12">
              {buttonText}
            </Button>
          </Link>
        )}
      </div>
      <p className="text-center mt-20">
        If this isn’t you,{" "}
        <Link href="#" onClick={logout} className="font-semibold underline">
          logout
        </Link>{" "}
        and try again
      </p>
    </>
  );
};
