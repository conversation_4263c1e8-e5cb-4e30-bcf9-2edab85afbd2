"use client";

import { AnimatePresence, motion } from "framer-motion";
import React, { useEffect } from "react";

import { cn } from "@/lib/utils";

interface IAssessmentLayoutProps {
  top?: React.ReactNode;
  stimulus?: React.ReactNode;
  response?: React.ReactNode;
  bottom?: React.ReactNode;
  isTutorialItem?: boolean;
  isPracticeItem?: boolean;
  stimulusType?: string;
}

export const AssessmentLayout = ({
  top,
  stimulus,
  response,
  bottom,
  isTutorialItem,
  isPracticeItem,
  stimulusType,
}: IAssessmentLayoutProps) => {
  const tutorialStyles = "!opacity-50 blur-sm pointer-events-none";
  const practiceStyles = "!opacity-20 pointer-events-none";

  const stimulusMinHeight = (() => {
    switch (stimulusType) {
      case "image":
        return "min-h-[240px]";
      case "text":
        return "min-h-[264px]";
      default:
        return "min-h-[160px]";
    }
  })();
  return (
    <div
      data-testid="assessment-layout"
      className="flex flex-col w-full h-screen gap-[24px] select-none"
    >
      <div data-testid="top" className="h-[56px] flex items-end justify-center">
        {top}
      </div>

      <div
        data-testid="stimulus"
        className={cn(
          stimulusMinHeight,
          "shrink-0 flex items-end justify-center"
        )}
      >
        <AnimatePresence mode="wait">
          {stimulus && (
            <motion.div
              key={(stimulus as any)?.key || "stimulus"}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -40 }}
              transition={{ duration: 0.4, delay: 0.2 }}
              className={cn(
                "w-full h-full flex items-end justify-center",
                isTutorialItem && tutorialStyles,
                isPracticeItem && practiceStyles
              )}
            >
              {stimulus}
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      <div
        data-testid="response"
        className="flex-1 flex items-center justify-center"
      >
        <AnimatePresence mode="wait">
          {response && (
            <motion.div
              key={(response as any)?.key || "response"}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -40 }}
              transition={{ duration: 0.4, delay: 0.2 }}
              className={cn(
                "w-full flex items-center justify-center",
                isTutorialItem && tutorialStyles,
                isPracticeItem && practiceStyles
              )}
            >
              {response}
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      <div
        data-testid="bottom"
        className="h-[120px] lg:h-[200px] flex items-start justify-center"
      >
        <AnimatePresence mode="wait">
          {bottom && (
            <motion.div
              key={(bottom as any)?.key || "bottom"}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{
                duration: 0.3,
                delay: 0,
              }}
              className="w-full flex items-start justify-center pointer"
            >
              {bottom}
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};
