import { useEffect, useRef } from "react";

import { FeedbackCharacter } from "@/components/ui";
import { useFeedbackAudioStore } from "@/stores/use-feedback-audio-store";
import { NovaScaleItemRecord } from "@/types/graphql/graphql";

interface FeedbackBarProps {
  isCorrect: boolean;
  scaleItem: NovaScaleItemRecord;
}

export const FeedbackBar = ({ isCorrect, scaleItem }: FeedbackBarProps) => {
  const hasPlayedRef = useRef(false);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const setIsFeedbackPlaying = useFeedbackAudioStore(
    (s) => s.setIsFeedbackPlaying
  );

  const audioUrl = isCorrect
    ? scaleItem.ifCorrectAudio?.url
    : scaleItem.ifIncorrectAudio?.url;

  useEffect(() => {
    useFeedbackAudioStore.getState().stop();

    if (!audioUrl || hasPlayedRef.current) return;

    setIsFeedbackPlaying(true);

    const audio = new Audio(audioUrl);
    audioRef.current = audio;

    const handleCanPlayThrough = () => {
      audio.play().then(() => {
        hasPlayedRef.current = true;
      });
    };

    const handleEnded = () => {
      setIsFeedbackPlaying(false);
    };

    audio.addEventListener("canplaythrough", handleCanPlayThrough);
    audio.addEventListener("ended", handleEnded);

    return () => {
      audio.pause();
      audio.currentTime = 0;
      audio.removeEventListener("canplaythrough", handleCanPlayThrough);
      audio.removeEventListener("ended", handleEnded);
      setIsFeedbackPlaying(false);
    };
  }, [audioUrl, setIsFeedbackPlaying]);

  if (!scaleItem) return null;

  const text = isCorrect ? scaleItem.ifCorrectText : scaleItem.ifIncorrectText;

  return (
    <FeedbackCharacter
      variant={isCorrect ? "success" : "error"}
      message={text || ""}
      isActive={true}
      characterUrl={scaleItem.tutorialImage?.url}
    />
  );
};
