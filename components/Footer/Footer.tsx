import { Button } from "@chakra-ui/react";
import styled from "@emotion/styled";
import Image from "next/image";
import { IoLogoInstagram, IoLogoLinkedin } from "react-icons/io5";

import { Header, Text } from "@/components";
import static_content from "@/data/static-content.json";
import { theme } from "@/styles/theme";

const FooterWrapper = styled.footer`
  display: flex;
  justify-content: center;
  background-color: ${theme.colors.secondary.purple_01.hex};
  min-height: 414px;
  height: 100%;
  width: 100%;
  padding: ${theme.spacing.xl.rem} 0;
  @media (max-width: ${theme.breakpoints.portable}) {
    padding: ${theme.spacing.md.rem} ${theme.spacing.sm.rem};
  }
`;
const FooterBounds = styled.div`
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  width: 100%;
  height: 100%;
  max-width: ${theme.max_page_width};
  @media (max-width: ${theme.breakpoints.portable}) {
    flex-wrap: wrap-reverse;
  }
`;
const FooterColumnContainer = styled.div`
  padding: ${theme.spacing.xs.rem};
  display: flex;
  flex-direction: column;
  flex: 1;
  min-width: 280px;
  margin-bottom: ${theme.spacing.lg.rem};
  h5,
  p {
    color: ${theme.colors.primary.white.hex};
    padding: ${theme.spacing.sm.rem} 0;
    line-height: 1.375rem;
    font-size: 0.875rem;
  }
  h5 {
    font-weight: 600;
    padding-top: ${theme.spacing.xs.rem};
    padding-bottom: 0;
  }
  @media (max-width: ${theme.breakpoints.portable}) {
    padding: ${theme.spacing.xs.rem};
  }
`;

export function Footer() {
  return (
    <FooterWrapper>
      <FooterBounds>
        <FooterColumnContainer>
          <div>
            <Image
              src={`/images/logos/talamo-motif.svg`}
              width="22"
              height="22"
              alt="Talamo motif"
            />
          </div>
          <div>
            {static_content?.footer?.company?.map((item, index) => {
              return (
                <Text.P_SM color="white" key={index}>
                  {item}
                </Text.P_SM>
              );
            })}
          </div>
        </FooterColumnContainer>
        <FooterColumnContainer>
          <Header.H4_SM
            fontSize={"1rem"}
            regular
            color={theme.colors.primary.white.hex}
          >
            Footer header
          </Header.H4_SM>
          <p>
            <Button
              variant={"unstyled"}
              css={{ fontWeight: "400", fontSize: ".9rem" }}
              color={theme.colors.primary.white.hex}
              onClick={() => alert("link clicked")}
            >
              Page name
            </Button>
          </p>
          <p>
            <Button
              variant={"unstyled"}
              css={{ fontWeight: "400", fontSize: ".9rem" }}
              color={theme.colors.primary.white.hex}
              onClick={() => alert("link clicked")}
            >
              Page name
            </Button>
          </p>
          <p>
            <Button
              variant={"unstyled"}
              css={{ fontWeight: "400", fontSize: ".9rem" }}
              color={theme.colors.primary.white.hex}
              onClick={() => alert("link clicked")}
            >
              Page name
            </Button>
          </p>
          <p>
            <Button
              variant={"unstyled"}
              css={{ fontWeight: "400", fontSize: ".9rem" }}
              color={theme.colors.primary.white.hex}
              onClick={() => alert("link clicked")}
            >
              Page name
            </Button>
          </p>
        </FooterColumnContainer>
        <FooterColumnContainer>
          <Header.H4_SM
            fontSize={"1rem"}
            regular
            color={theme.colors.primary.white.hex}
          >
            Footer header
          </Header.H4_SM>
          <p>
            <Button
              variant={"unstyled"}
              css={{ fontWeight: "400", fontSize: ".9rem" }}
              color={theme.colors.primary.white.hex}
              onClick={() => alert("link clicked")}
            >
              Page name
            </Button>
          </p>
          <p>
            <Button
              variant={"unstyled"}
              css={{ fontWeight: "400", fontSize: ".9rem" }}
              color={theme.colors.primary.white.hex}
              onClick={() => alert("link clicked")}
            >
              Page name
            </Button>
          </p>
          <p>
            <Button
              variant={"unstyled"}
              css={{ fontWeight: "400", fontSize: ".9rem" }}
              color={theme.colors.primary.white.hex}
              onClick={() => alert("link clicked")}
            >
              Page name
            </Button>
          </p>
          <p>
            <Button
              variant={"unstyled"}
              css={{ fontWeight: "400", fontSize: ".9rem" }}
              color={theme.colors.primary.white.hex}
              onClick={() => alert("link clicked")}
            >
              Page name
            </Button>
          </p>
        </FooterColumnContainer>
        <FooterColumnContainer>
          <Header.H4_SM
            fontSize={"1rem"}
            regular
            color={theme.colors.primary.white.hex}
          >
            Get In Touch
          </Header.H4_SM>
          <p>
            <Button
              variant={"unstyled"}
              css={{
                fontWeight: "400",
                textDecoration: "underline",
                fontSize: ".9rem",
              }}
              color={theme.colors.primary.white.hex}
              onClick={() => alert("link clicked")}
            >
              <EMAIL>
            </Button>
          </p>
          <div
            style={{
              display: "flex",
              justifyContent: "flex-start",
              flexWrap: "wrap",
            }}
          >
            <div style={{ marginRight: "8px" }}>
              <Button
                variant={"unstyled"}
                css={{ fontWeight: "400", fontSize: ".9rem" }}
                color={theme.colors.primary.white.hex}
                rightIcon={<IoLogoInstagram size={24} />}
                onClick={() => alert("link clicked")}
              />
            </div>
            <div style={{ marginRight: "8px" }}></div>
            <Button
              variant={"unstyled"}
              css={{ fontWeight: "400", fontSize: ".9rem" }}
              color={theme.colors.primary.white.hex}
              rightIcon={<IoLogoLinkedin size={24} />}
              onClick={() => alert("link clicked")}
            />
          </div>
        </FooterColumnContainer>
      </FooterBounds>
    </FooterWrapper>
  );
}
