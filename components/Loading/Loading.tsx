import { Center, Spinner, VStack } from "@chakra-ui/react";

import { theme } from "@/styles/theme";

export function Loading({ css }: { css?: any }) {
  return (
    <VStack
      w={"100vw"}
      h={"100vh"}
      left={0}
      top={0}
      justify="center"
      background={`rgba(${theme.colors.ui.grey_04}, 0.5)`}
      css={{ position: "fixed", zIndex: "100", ...css }}
    >
      <Center>
        <Spinner
          borderWidth={3}
          width={theme.spacing.xl.rem}
          height={theme.spacing.xl.rem}
          color={theme.colors.primary.purple.hex}
        />
      </Center>
    </VStack>
  );
}
