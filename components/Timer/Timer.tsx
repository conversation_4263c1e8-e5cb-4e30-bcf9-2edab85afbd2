import { Box, HStack } from "@chakra-ui/react";

import { theme } from "@/styles/theme";

import { Text } from "../v2";

export interface ITimerProps {
  totalTime: number;
  remainingTime: number;
}

export const Timer = ({ totalTime, remainingTime }: ITimerProps) => {
  const percentComplete = (remainingTime / totalTime) * 100;
  let backgroundColor;
  if (percentComplete >= 75) {
    backgroundColor = "#6FD06D";
  } else if (percentComplete >= 50 && percentComplete < 75) {
    backgroundColor = "#FFD769";
  } else if (percentComplete >= 25 && percentComplete < 50) {
    backgroundColor = "#F6A55D";
  } else if (percentComplete >= 0 && percentComplete < 25) {
    backgroundColor = "#F17373";
  }
  return (
    <HStack gap="10px" alignItems="center">
      <Text element="h4" variant="md" m={0} mb="-5px">
        {remainingTime}
      </Text>
      <Box
        w="100px"
        h="25px"
        display="block"
        pos="relative"
        borderRadius="10px"
        bgColor="rgba(0,0,0,0.02)"
        boxShadow="inset 0 0 0 1px rgba(0, 0, 0, 0.1)"
        overflow="hidden"
      >
        <Box
          w={`${percentComplete}%`}
          h="25px"
          pos="absolute"
          top={0}
          left={0}
          borderRadius="10px"
          bgColor={backgroundColor}
          transition="all 0.3s ease-in"
        ></Box>
        <Box
          width="100%"
          height="100%"
          pos="absolute"
          top={0}
          left={0}
          zIndex={10}
          backgroundImage="url('/images/assessment-progress/loading-stripe.svg')"
        />
      </Box>
    </HStack>
  );
};
