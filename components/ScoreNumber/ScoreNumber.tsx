import { Box } from "@chakra-ui/react";

import { theme } from "@/styles/theme";

import { Text } from "../v2";

export const ScoreNumber = ({ children }: { children: number }) => {
  if (children <= 69) {
    return (
      <Box
        py={theme.spacing.xxxs.px}
        px={theme.spacing.xs.px}
        borderRadius="500px"
        display="inline-flex"
        bgColor={theme.colors.ui.alert_red_02.hex}
      >
        <Text
          element="h3"
          variant="xs"
          color={theme.colors.ui.alert_red_01.hex}
        >
          {children}
        </Text>
      </Box>
    );
  }
  return <Text variant="sm">{children}</Text>;
};
