import "@testing-library/jest-dom/extend-expect";

import { render, screen } from "@testing-library/react";

import { ILoaderProps, Loader } from "./Loader";

jest.mock("lottie-react", () => {
  return {
    __esModule: true,
    default: () => {
      return <div>Lottie Animation</div>;
    },
  };
});

describe("<Loader />", () => {
  const defaultProps: ILoaderProps = {
    variant: "snoozy",
    loadingText: "Loading",
    isActive: true,
  };

  it("renders without crashing", () => {
    render(<Loader {...defaultProps} />);
    const loadingText = defaultProps.loadingText || "Loading";
    expect(screen.getByText(loadingText)).toBeInTheDocument();
  });

  it("initially renders in an invisible state", () => {
    const { container } = render(<Loader {...defaultProps} />);
    expect(container.firstChild).toHaveStyle("opacity: 0");
  });
});
