import { Box, VStack } from "@chakra-ui/react";
import { AnimatePresence, motion } from "framer-motion";
import dynamic from "next/dynamic";

import { theme } from "@/styles/theme";

import { Text } from "../Text/Text";
import { AnimatedEllipsis } from "./components/AnimatedEllipsis";
import HuzzahAnimation from "./Lottie/LottieHuzzahAnimation.json";
import SnoozyAnimation from "./Lottie/LottieSnoozyAnimation.json";

const Lottie = dynamic(() => import("lottie-react"), { ssr: false });

export type VariantType = "snoozy" | "huzzah";
export interface ILoaderProps {
  variant?: VariantType;
  loadingText?: string;
  isActive?: boolean;
}

export const Loader = ({
  variant = "snoozy",
  loadingText = "Thinking",
  isActive = false,
}: ILoaderProps) => {
  let lottieAnimation;

  switch (variant) {
    case "snoozy":
      lottieAnimation = SnoozyAnimation;
      break;
    case "huzzah":
      lottieAnimation = HuzzahAnimation;
      break;
    default:
      lottieAnimation = SnoozyAnimation;
  }

  return (
    <AnimatePresence>
      {isActive && (
        <motion.div
          initial="hidden"
          animate="visible"
          exit="exit"
          variants={{
            hidden: { opacity: 0 },
            visible: { opacity: 1 },
            exit: { opacity: 0 },
          }}
          transition={{ duration: 0.2 }}
        >
          <Box
            display="flex"
            justifyContent="center"
            alignItems="center"
            pos="fixed"
            top="0"
            left="0"
            w="100vw"
            h="100vh"
            bgColor={theme.colors.secondary.purple_01.hex}
            zIndex="99999"
          >
            <VStack gap={theme.spacing.md.px}>
              <Lottie
                animationData={lottieAnimation}
                loop={true}
                autoplay={true}
                style={{
                  height: "256px",
                  width: "256px",
                  display: "block",
                }}
              />

              <Box display="flex">
                <Text
                  element="h2"
                  variant="lg"
                  color={theme.colors.primary.white.hex}
                >
                  {loadingText}
                </Text>
                <Text
                  element="h2"
                  variant="lg"
                  color={theme.colors.primary.white.hex}
                >
                  <AnimatedEllipsis />
                </Text>
              </Box>
            </VStack>
          </Box>
        </motion.div>
      )}
    </AnimatePresence>
  );
};
