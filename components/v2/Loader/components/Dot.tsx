import { motion, useAnimation } from "framer-motion";
import { useEffect, useRef } from "react";

const Dot = ({ delay = 0 }) => {
  const controls = useAnimation();
  const mountedRef = useRef(false);

  useEffect(() => {
    mountedRef.current = true;

    const animateDots = () => {
      if (!mountedRef.current) return;

      controls
        .start("visible")
        .then(() => {
          if (!mountedRef.current) return;
          return controls.start("hidden");
        })
        .catch(() => {});
    };

    const timer = setTimeout(animateDots, 1000);
    const interval = setInterval(animateDots, 2500);

    return () => {
      mountedRef.current = false;
      clearInterval(interval);
      clearTimeout(timer);
    };
  }, [controls]);

  return (
    <motion.span
      initial={{ opacity: 0 }}
      animate={controls}
      variants={{
        hidden: {
          opacity: 0,
          transition: { delay: delay + 0.3, duration: 0.2 },
        },
        visible: { opacity: 1, transition: { delay: delay, duration: 0.2 } },
      }}
    >
      .
    </motion.span>
  );
};

export default Dot;
