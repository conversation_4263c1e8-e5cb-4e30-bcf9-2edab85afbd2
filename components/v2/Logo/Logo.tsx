import { theme } from "@/styles/theme";

export interface ILogoProps {
  color?: string;
  width?: number;
  height?: number;
}

export const Logo = ({
  color = theme.colors.primary.black.hex,
  width = 98,
  height = 25,
}: ILogoProps) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 98 25"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      data-testid="talamo-logo"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M23.3746 12.5005C24.3909 14.1988 24.1675 16.4323 22.7044 17.8954C21.5843 19.0156 20.0127 19.4091 18.5753 19.076C18.9083 20.5133 18.5148 22.0848 17.3947 23.2049C15.9316 24.6681 13.6982 24.8915 11.9999 23.8751C10.3016 24.8913 8.06835 24.6678 6.6053 23.2048C5.48521 22.0846 5.09168 20.5131 5.42473 19.0757C3.98729 19.409 2.41551 19.0155 1.29528 17.8952C-0.167855 16.4321 -0.391227 14.1986 0.625167 12.5002C-0.391476 10.8018 -0.168174 8.56805 1.29507 7.10476C1.99516 6.40465 2.8716 5.98839 3.7814 5.85598C4.03012 5.81978 4.28134 5.8048 4.53212 5.81103C4.83175 5.81847 5.13076 5.85619 5.42417 5.92419C5.09117 4.48688 5.4847 2.91536 6.60477 1.79527C8.06792 0.332072 10.3014 0.108702 11.9997 1.12516C13.6981 0.108518 15.9317 0.331831 17.3949 1.7951C18.515 2.91523 18.9085 4.48679 18.5755 5.92413C20.0129 5.59085 21.5847 5.98435 22.7049 7.10461C24.1683 8.56802 24.3915 10.802 23.3746 12.5005ZM12 13.444L7.54894 17.8951C6.34336 19.1007 6.34336 21.0554 7.54895 22.2611C8.75453 23.4667 10.7092 23.4667 11.9148 22.2611L16.3658 17.8099L12 13.444ZM17.3949 16.9517C17.6963 17.2531 18.0445 17.4792 18.4162 17.6299C19.531 18.082 20.8566 17.8559 21.7608 16.9517C22.6617 16.0508 22.8894 14.7315 22.4438 13.6191C22.3117 13.2895 22.1205 12.978 21.8702 12.7009C21.8349 12.6618 21.7984 12.6234 21.7608 12.5858L17.3094 8.13431L17.3092 8.13411L12.9434 12.5001L12.9436 12.5003L17.3949 16.9517ZM18.428 7.36556L22.444 11.3817C22.89 10.2691 22.6624 8.94945 21.7613 8.04829C20.8602 7.14713 19.5405 6.91955 18.428 7.36556ZM13.1188 22.9444C14.2311 23.3898 15.5502 23.1621 16.4511 22.2612C17.3519 21.3603 17.5796 20.0412 17.1342 18.9288L13.1188 22.9444ZM2.23893 16.9516C1.338 16.0506 1.11033 14.7313 1.55592 13.6189L5.57145 17.6346C4.4591 18.0802 3.13986 17.8525 2.23893 16.9516ZM11.056 12.5L6.69017 16.866L2.23874 12.4144C1.03315 11.2088 1.03315 9.25406 2.23874 8.04844C2.682 7.60516 3.22652 7.32486 3.79784 7.20755C4.00346 7.16533 4.21254 7.14422 4.42163 7.14422C5.21169 7.14422 6.00175 7.44562 6.60454 8.04844L11.056 12.5ZM11.9997 11.5564L7.63391 7.19045L12.0854 2.73878C13.291 1.53316 15.2457 1.53316 16.4513 2.73878C17.6569 3.94441 17.6569 5.89912 16.4513 7.10475L11.9997 11.5564ZM7.54842 2.73895C8.44936 1.83797 9.76862 1.6103 10.881 2.05593L6.86542 6.07161C6.41981 4.95922 6.64747 3.63991 7.54842 2.73895Z"
        fill={color}
      />
      <path
        d="M33.872 14.012V9.836H32.576V7.784H33.998L34.304 5.462H36.572V7.784H38.66V9.836H36.572V13.742C36.572 14.516 37.04 15.038 37.796 15.038C38.048 15.038 38.444 14.984 38.75 14.894V17.072C38.192 17.252 37.472 17.36 36.896 17.36C35.06 17.36 33.872 15.974 33.872 14.012ZM45.3739 14.984C46.7779 14.984 47.7859 13.886 47.7859 12.392C47.7859 10.898 46.7779 9.8 45.3739 9.8C43.9699 9.8 42.9619 10.898 42.9619 12.392C42.9619 13.886 43.9699 14.984 45.3739 14.984ZM47.8039 9.062V7.784H50.5039V17H47.8039V15.722C47.0839 16.748 45.9859 17.36 44.6719 17.36C42.0799 17.36 40.2619 15.308 40.2619 12.392C40.2619 9.476 42.0799 7.424 44.6719 7.424C45.9859 7.424 47.0839 8.036 47.8039 9.062ZM52.8219 3.644H55.5219V17H52.8219V3.644ZM62.5477 14.984C63.9517 14.984 64.9597 13.886 64.9597 12.392C64.9597 10.898 63.9517 9.8 62.5477 9.8C61.1437 9.8 60.1357 10.898 60.1357 12.392C60.1357 13.886 61.1437 14.984 62.5477 14.984ZM64.9777 9.062V7.784H67.6777V17H64.9777V15.722C64.2577 16.748 63.1597 17.36 61.8457 17.36C59.2537 17.36 57.4357 15.308 57.4357 12.392C57.4357 9.476 59.2537 7.424 61.8457 7.424C63.1597 7.424 64.2577 8.036 64.9777 9.062ZM72.7138 17H70.0138V7.784H72.6058V8.882C73.1818 7.946 74.1898 7.424 75.3058 7.424C76.6378 7.424 77.7538 8.054 78.2758 9.062C78.8698 8.054 80.0938 7.424 81.3718 7.424C83.4598 7.424 84.9178 8.99 84.9178 11.204V17H82.2178V11.744C82.2178 10.646 81.5338 9.836 80.5078 9.8C79.4638 9.8 78.7617 10.61 78.7617 11.744V17H76.0618V11.744C76.0618 10.574 75.4138 9.8 74.4058 9.8C73.3798 9.8 72.7138 10.574 72.7138 11.744V17ZM91.7621 7.424C94.8041 7.424 96.8921 9.44 96.8921 12.392C96.8921 15.326 94.8041 17.36 91.7621 17.36C88.7381 17.36 86.6501 15.326 86.6501 12.392C86.6501 9.44 88.7381 7.424 91.7621 7.424ZM91.7621 14.984C93.1841 14.984 94.1741 13.904 94.1741 12.392C94.1741 10.88 93.1841 9.8 91.7621 9.8C90.3581 9.8 89.3681 10.88 89.3681 12.392C89.3681 13.904 90.3581 14.984 91.7621 14.984Z"
        fill={color}
      />
    </svg>
  );
};
