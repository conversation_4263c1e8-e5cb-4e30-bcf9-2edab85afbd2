import "@testing-library/jest-dom/extend-expect";

import { render } from "@testing-library/react";

import { Logo } from "./Logo";

describe("<Logo />", () => {
  it("renders without crashing", () => {
    const { getByTestId } = render(<Logo />);
    expect(getByTestId("talamo-logo")).toBeInTheDocument();
  });

  it("applies the default props correctly", () => {
    const { getByTestId } = render(<Logo />);
    const logo = getByTestId("talamo-logo");

    expect(logo).toHaveAttribute("width", "98");
    expect(logo).toHaveAttribute("height", "25");
    expect(logo.querySelector("path")).toHaveAttribute("fill", "#111124");
  });

  it("applies the custom props correctly", () => {
    const customProps = {
      color: "#ff0000",
      width: 150,
      height: 50,
    };

    const { getByTestId } = render(<Logo {...customProps} />);
    const logo = getByTestId("talamo-logo");

    expect(logo).toHaveAttribute("width", "150");
    expect(logo).toHaveAttribute("height", "50");
    expect(logo.querySelector("path")).toHaveAttribute("fill", "#ff0000");
  });
});
