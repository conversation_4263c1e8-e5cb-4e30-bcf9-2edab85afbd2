import { Button } from "@chakra-ui/react";
import Image from "next/image";
import { useEffect, useRef, useState } from "react";

import { theme } from "@/styles/theme";

import { AudioButton } from "../AudioButton/AudioButton";
import { KeyboardNudge } from "../KeyboardNudge/KeyboardNudge";
import { Tooltip } from "../Tooltip/Tooltip";

export type ButtonState =
  | "default"
  | "selected"
  | "correct"
  | "incorrect"
  | "disabled";

interface IStates {
  [key: string]: {
    backgroundColor: string;
    borderColor: string;
    nudgeColor?: string;
  };
}

export interface IMultiChoiceTextProps {
  state?: ButtonState;
  label: string;
  disabled?: boolean;
  keyToPress: string | number;
  onButtonClick: () => void;
  audio?: string;
  isSample?: boolean;
  isStimulusComplete?: boolean;
  tooltipTimout?: boolean;
  imageNumber?: number;
}
export const MultiChoiceAudio = ({
  state = "default",
  label,
  disabled = false,
  keyToPress,
  onButtonClick,
  audio,
  isSample,
  isStimulusComplete,
  tooltipTimout,
  imageNumber,
  ...rest
}: IMultiChoiceTextProps) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [isTooltipActive, setIsTooltipActive] = useState(false);
  const [hasUserInteracted, setHasUserInteracted] = useState(false);

  const audioRef = useRef(new Audio(audio));

  const states: IStates = {
    default: {
      backgroundColor: theme.colors.primary.white.hex,
      borderColor: theme.colors.ui.grey_03.hex,
    },
    selected: {
      backgroundColor: theme.colors.secondary.purple_04.hex,
      borderColor: theme.colors.secondary.purple_02.hex,
      nudgeColor: theme.colors.secondary.purple_02.hex,
    },
    correct: {
      backgroundColor: theme.colors.ui.alert_green_02.hex,
      borderColor: theme.colors.ui.alert_green_01.hex,
      nudgeColor: theme.colors.ui.alert_green_01.hex,
    },
    incorrect: {
      backgroundColor: theme.colors.ui.alert_red_02.hex,
      borderColor: theme.colors.ui.alert_red_01.hex,
      nudgeColor: theme.colors.ui.alert_red_01.hex,
    },
    disabled: {
      backgroundColor: theme.colors.primary.white.hex,
      borderColor: theme.colors.ui.grey_03.hex,
    },
  };

  const handleKeyDown = (event) => {
    if (disabled === true || event.key !== keyToPress.toString()) return;
    onButtonClick();
  };

  useEffect(() => {
    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [disabled]);

  const handleMouseEnter = () => {
    setIsPlaying(true);
    setIsTooltipActive(false);
    audioRef.current.play();
  };

  const handleMouseLeave = () => {
    setIsPlaying(false);
    audioRef.current.pause();
    audioRef.current.currentTime = 0;
  };

  useEffect(() => {
    if (
      //tooltipTimout &&
      isSample &&
      keyToPress === 1 &&
      isStimulusComplete &&
      !hasUserInteracted
    ) {
      setIsTooltipActive(true);
    }
    if (hasUserInteracted) {
      setIsTooltipActive(false);
    }
  }, [isStimulusComplete, hasUserInteracted]);

  const handleButtonClick = () => {
    onButtonClick();
    setHasUserInteracted(true);
  };

  return (
    <Button
      data-testid="multi-choice-audio"
      bg={states[state].backgroundColor}
      border={`2px solid ${states[state].borderColor}`}
      isDisabled={disabled}
      onClick={handleButtonClick}
      display="flex"
      alignItems="center"
      justifyContent="flex-start"
      py={theme.spacing.md.px}
      px={theme.spacing.sm.px}
      gap={theme.spacing.md.px}
      fontSize={theme.v2Text.headings.md.fontSize}
      {...rest}
    >
      {audio ? (
        <div onMouseEnter={handleMouseEnter} onMouseLeave={handleMouseLeave}>
          {isTooltipActive ? (
            <Tooltip
              hasSound={true}
              isOpen={true}
              label="👋 Hover to listen!"
              placement="top"
              shouldWrapChildren
              offset={[0, 20]}
            >
              <AudioButton size="sm" isPlaying={isPlaying} />
            </Tooltip>
          ) : (
            <AudioButton size="sm" isPlaying={isPlaying} />
          )}
        </div>
      ) : (
        <KeyboardNudge backgroundColor={states[state].nudgeColor || undefined}>
          {keyToPress}
        </KeyboardNudge>
      )}{" "}
      <Image
        src={`/images/waveforms/waveform-image-${imageNumber}.svg`}
        width={184}
        height={34}
        alt=""
      />
    </Button>
  );
};
