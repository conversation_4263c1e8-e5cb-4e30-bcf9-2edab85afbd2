"use client";

import { Box, Container, Flex } from "@chakra-ui/react";
import { ArrowRightOnRectangleIcon } from "@heroicons/react/24/solid";
import Link from "next/link";
import { useRouter } from "next/router";
import { useState } from "react";

import { But<PERSON> } from "@/components/v2";
import { IS_STUDENT_ENV } from "@/constants/constants";
import { theme as styleTheme } from "@/styles/theme";

import { Logo } from "../Logo/Logo";
import { Modal } from "../Modal/Modal";

export interface PrimaryNavigationProps {
  transparent?: boolean;
  bgColor?: string;
  theme?: "light" | "dark";
  hideLogout?: boolean;
  student?: boolean;
}
export const PrimaryNavigation = ({
  transparent = false,
  bgColor = styleTheme.colors.primary.white.hex,
  theme = "dark",
  hideLogout = false,
  student = false,
}: PrimaryNavigationProps) => {
  const router = useRouter();
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleCloseButtonClick = () => router.push("/logout");
  return (
    <>
      <Box
        data-testid="primary-navigation"
        backgroundColor={transparent ? "transparent" : bgColor}
        py={styleTheme.spacing.sm.px}
        zIndex={500}
        width="100%"
        pos="relative"
      >
        <Container width={"100%"} px={30} maxW={"none"}>
          <Flex alignItems="center">
            <Box
              flex="1"
              display="flex"
              alignItems="center"
              gap={styleTheme.spacing.md.px}
            >
              <Link href="/">
                <Logo
                  color={
                    theme === "dark"
                      ? styleTheme.colors.primary.black.hex
                      : styleTheme.colors.primary.white.hex
                  }
                />
              </Link>
              <Box
                color={
                  theme === "dark"
                    ? styleTheme.colors.primary.black.hex
                    : styleTheme.colors.primary.white.hex
                }
              >
                Back to{" "}
                <Link
                  href="http://talamo.co.uk"
                  style={{ textDecoration: "underline", fontWeight: "bold" }}
                >
                  talamo.co.uk
                </Link>
              </Box>
            </Box>
            {!hideLogout && (
              <Button
                variant="secondary"
                size="sm"
                color="white"
                iconRight={<ArrowRightOnRectangleIcon width={20} height={20} />}
                onClick={() => {
                  setIsModalOpen(true);
                }}
              >
                Log-out
              </Button>
            )}
          </Flex>
        </Container>
      </Box>
      <Modal
        isOpen={isModalOpen}
        setIsOpen={setIsModalOpen}
        title="Log-out?"
        primaryCtaText="Cancel"
        onPrimaryCtaClick={() => setIsModalOpen(false)}
        primaryCtaVariant="secondary"
        secondaryCtaText="Log-out"
        secondaryCtaVariant="primary"
        secondaryCtaColor="red"
        onSecondaryCtaClick={() => handleCloseButtonClick()}
        overlayBackground={`rgba(${styleTheme.colors.secondary.purple_01.rgbRaw}, 0.9)`}
      />
    </>
  );
};
