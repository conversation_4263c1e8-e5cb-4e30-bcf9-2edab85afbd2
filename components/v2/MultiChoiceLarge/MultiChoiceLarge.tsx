import { Box, Button, Image } from "@chakra-ui/react";
import { useEffect, useState } from "react";

import { theme } from "@/styles/theme";

import { KeyboardNudge } from "../KeyboardNudge/KeyboardNudge";
import { Text } from "../Text/Text";

export type ButtonState =
  | "default"
  | "selected"
  | "correct"
  | "incorrect"
  | "disabled";

interface IStates {
  [key: string]: {
    backgroundColor: string;
    borderColor: string;
    textColor: string;
    nudgeColor?: string;
  };
}

export interface IMultiChoiceLargeProps {
  state?: ButtonState;
  label: string;
  disabled?: boolean;
  keyToPress: string | number;
  onButtonClick: () => void;
  image?: string;
}

export const MultiChoiceLarge = ({
  state = "default",
  label,
  disabled = false,
  keyToPress,
  onButtonClick,
  image,
  ...rest
}: IMultiChoiceLargeProps) => {
  const [isHovered, setIsHovered] = useState(false);
  const states: IStates = {
    default: {
      backgroundColor: theme.colors.primary.white.hex,
      borderColor: theme.colors.ui.grey_03.hex,
      textColor: theme.colors.primary.purple.hex,
    },
    selected: {
      backgroundColor: theme.colors.secondary.purple_04.hex,
      borderColor: theme.colors.secondary.purple_02.hex,
      textColor: theme.colors.primary.purple.hex,
      nudgeColor: theme.colors.secondary.purple_02.hex,
    },
    correct: {
      backgroundColor: theme.colors.ui.alert_green_02.hex,
      borderColor: theme.colors.ui.alert_green_01.hex,
      textColor: theme.colors.ui.alert_green_01.hex,
      nudgeColor: theme.colors.ui.alert_green_01.hex,
    },
    incorrect: {
      backgroundColor: theme.colors.ui.alert_red_02.hex,
      borderColor: theme.colors.ui.alert_red_01.hex,
      textColor: theme.colors.ui.alert_red_01.hex,
      nudgeColor: theme.colors.ui.alert_red_01.hex,
    },
    disabled: {
      backgroundColor: theme.colors.primary.white.hex,
      borderColor: theme.colors.ui.grey_03.hex,
      textColor: theme.colors.ui.grey_03.hex,
    },
  };

  const hoverStyles = {
    default: {
      backgroundColor: theme.colors.primary.white.hex,
      borderColor: theme.colors.secondary.purple_02.hex,
    },
    selected: {
      backgroundColor: theme.colors.secondary.purple_05.hex,
      borderColor: theme.colors.secondary.purple_03.hex,
    },
  };

  const handleKeyDown = (event) => {
    if (disabled === true || event.key !== keyToPress.toString()) return;
    onButtonClick();
  };

  useEffect(() => {
    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [disabled]);

  return (
    <Button
      data-testid="multi-choice-large"
      bg={states[state].backgroundColor}
      border={`3px solid ${states[state].borderColor}`}
      color={states[state].textColor}
      isDisabled={disabled}
      onClick={onButtonClick}
      display="flex"
      alignItems="center"
      justifyContent="center"
      w={140}
      height={140}
      py={theme.spacing.md.px}
      px={theme.spacing.sm.px}
      gap={theme.spacing.xs.px}
      fontSize={theme.v2Text.headings.md.fontSize}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      borderRadius={18}
      _hover={
        state === "default" && !disabled
          ? hoverStyles.default
          : state === "selected" && !disabled
            ? hoverStyles.selected
            : {}
      }
      {...rest}
    >
      <Box pos="absolute" top="0px" right="6px" width="auto">
        <KeyboardNudge
          backgroundColor={states[state].nudgeColor || undefined}
          style={{
            paddingBottom: "3px",
            paddingTop: "3px",
            opacity: state === "selected" && isHovered ? 0.3 : 1,
          }}
        >
          {keyToPress}
        </KeyboardNudge>
      </Box>
      {image ? (
        <Image
          maxW={"100px"}
          maxH={"100px"}
          alt={`Stimulus image ${label}`}
          src={image}
        />
      ) : (
        <Text element="h3" variant="lg">
          {label}
        </Text>
      )}
    </Button>
  );
};
