import "@testing-library/jest-dom/extend-expect";

import { render, screen } from "@testing-library/react";
import React from "react";

import { Tooltip } from "./Tooltip";

jest.mock("react-howler", () => {
  return {
    __esModule: true,
    default: () => <div data-testid="mocked-howler"></div>,
  };
});

describe("<Tooltip />", () => {
  it("renders the children content correctly", () => {
    render(<Tooltip>Test Tooltip Content</Tooltip>);
    expect(screen.getByText("Test Tooltip Content")).toBeInTheDocument();
  });

  it("plays a sound if hasSound prop is true", () => {
    render(<Tooltip hasSound={true}>Test Tooltip Content</Tooltip>);
    const soundElement = screen.getByTestId("mocked-howler");
    expect(soundElement).toBeInTheDocument();
  });

  it("does NOT play sound when hasSound is false", () => {
    render(<Tooltip hasSound={false}>Test Tooltip Content</Tooltip>);
    const soundElements = screen.queryAllByTestId("mocked-howler");
    expect(soundElements).toHaveLength(0);
  });

  it("plays sound on tooltip open", () => {
    const { rerender } = render(
      <Tooltip hasSound={true} isOpen={false}>
        Test Tooltip Content
      </Tooltip>
    );
    rerender(
      <Tooltip hasSound={true} isOpen={true}>
        Test Tooltip Content
      </Tooltip>
    );
    const soundElement = screen.getByTestId("mocked-howler");
    expect(soundElement).toBeInTheDocument();
  });
});
