import { Tooltip as ChakraTooltip, TooltipProps } from "@chakra-ui/react";
import { ReactNode, useEffect, useState } from "react";
import ReactHowler from "react-howler";

import { theme } from "@/styles/theme";

export interface ITooltipProps extends TooltipProps {
  colour?: "yellow" | "green" | "red";
  children: ReactNode;
  isOpen?: boolean;
  hasSound?: boolean;
  notifySound?: string;
}

export const Tooltip = ({
  colour = "yellow",
  children,
  isOpen,
  hasSound,
  notifySound = "https://www.datocms-assets.com/103689/1698241008-pop-audio-quiet.mp3",
  ...rest
}: ITooltipProps) => {
  const [isSoundPlaying, setIsSoundPlaying] = useState(false);
  const backgroundColour = {
    yellow: theme.colors.tertiary.yellow_05.hex,
    green: theme.colors.ui.alert_green_02.hex,
    red: theme.colors.ui.alert_red_02.hex,
  };

  useEffect(() => {
    if (hasSound) {
      setIsSoundPlaying(true);
    }
  }, []);

  return (
    <>
      <ChakraTooltip
        bg={backgroundColour[colour]}
        color={theme.colors.primary.black.hex}
        fontSize={theme.v2Text.headings.sm.fontSize}
        fontWeight={theme.v2Text.headings.sm.fontWeight}
        py={theme.spacing.xs.px}
        px={theme.spacing.sm.px}
        hasArrow
        isOpen={isOpen}
        boxShadow="none"
        zIndex={1000}
        {...rest}
      >
        {children}
      </ChakraTooltip>
      {hasSound && <ReactHowler src={notifySound} playing={isSoundPlaying} />}
    </>
  );
};
