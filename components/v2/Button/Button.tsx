"use client";

import { Button as <PERSON><PERSON><PERSON><PERSON>on, ButtonProps } from "@chakra-ui/react";

import { useAssessmentStore } from "@/hooks";
import { theme } from "@/styles/theme";

export type ButtonSize = "xs" | "sm" | "md" | "lg";
export type ButtonVariant = "primary" | "secondary";
export type ButtonColors =
  | "purple"
  | "white"
  | "whiteOutline"
  | "green"
  | "red"
  | "orange";

export interface IButtonProps
  extends ButtonProps,
    React.ButtonHTMLAttributes<HTMLButtonElement> {
  size?: ButtonSize;
  variant?: ButtonVariant;
  color?: ButtonColors;
  disabled?: boolean;
  isPlaying?: boolean;
  iconLeft?: any;
  iconRight?: any;
  onClick?: () => void;
  onKeyDown?: () => void;
  keyToPress?: string;
  children: any;
}

export const Button = ({
  size = "md",
  variant = "primary",
  color = "purple",
  disabled = false,
  iconLeft,
  iconRight,
  onClick,
  onKeyDown,
  keyToPress,
  children,
  ...rest
}: IButtonProps) => {
  const { isCompleteScaleRunning } = useAssessmentStore();

  const fonts = {
    xs: theme.v2Text.headings.xs,
    sm: theme.v2Text.headings.sm,
    md: theme.v2Text.headings.md,
    lg: theme.v2Text.headings.lg,
  };

  const padding = {
    xs: theme.spacing.xs.px,
    sm: theme.spacing.sm.px,
    md: theme.spacing.md.px,
    lg: theme.spacing.lg.px,
  };

  const height = {
    xs: "32px",
    sm: "40px",
    md: "60px",
    lg: "72px",
  };

  const colors = {
    purple: {
      primary: {
        text: theme.colors.primary.white.hex,
        background: theme.colors.primary.purple.hex,
        hover: theme.colors.secondary.purple_02.hex,
      },
      secondary: {
        text: theme.colors.primary.black.hex,
        background: "transparent",
        hover: theme.colors.ui.grey_04.hex,
      },
    },
    white: {
      primary: {
        text: theme.colors.primary.black.hex,
        background: theme.colors.primary.white.hex,
        hover: theme.colors.ui.grey_04.hex,
      },
      secondary: {
        text: theme.colors.primary.black.hex,
        background: theme.colors.primary.white.hex,
        hover: theme.colors.ui.grey_04.hex,
      },
    },
    whiteOutline: {
      primary: {
        text: theme.colors.primary.black.hex,
        background: theme.colors.primary.white.hex,
        hover: theme.colors.ui.grey_04.hex,
      },
      secondary: {
        text: theme.colors.primary.black.hex,
        background: theme.colors.primary.white.hex,
        hover: theme.colors.ui.grey_04.hex,
      },
    },
    green: {
      primary: {
        text: theme.colors.primary.white.hex,
        background: theme.colors.ui.alert_green_01.hex,
        hover: theme.colors.ui.alert_green_01.hover.hex,
      },
      secondary: {
        text: theme.colors.ui.alert_green_01.hex,
        background: theme.colors.primary.white.hex,
        hover: theme.colors.ui.alert_green_02.hex,
      },
    },
    red: {
      primary: {
        text: theme.colors.primary.white.hex,
        background: theme.colors.ui.alert_red_01.hex,
        hover: theme.colors.ui.alert_red_01.hover.hex,
      },
      secondary: {
        text: theme.colors.ui.alert_red_01.hex,
        background: theme.colors.primary.white.hex,
        hover: theme.colors.ui.alert_red_02.hex,
      },
    },
    orange: {
      primary: {
        text: theme.colors.primary.white.hex,
        background: theme.colors.ui.alert_orange_01.hex,
        hover: theme.colors.ui.alert_orange_01.hover.hex,
      },
      secondary: {
        text: theme.colors.ui.alert_orange_01.hex,
        background: theme.colors.primary.white.hex,
        hover: theme.colors.ui.alert_orange_03.hex,
      },
    },
  };

  const handleClick = (): void => {
    if (onClick) {
      onClick();
    }
  };

  const handleKeyPress = (
    event: React.KeyboardEvent<HTMLButtonElement>
  ): void => {
    if (onKeyDown && keyToPress && event.key === keyToPress) {
      onKeyDown();
    }
  };

  return (
    <ChakraButton
      data-testid="button"
      onClick={handleClick}
      onKeyDown={handleKeyPress}
      isDisabled={disabled || isCompleteScaleRunning}
      backgroundColor={colors[color][variant].background}
      border={
        variant === "secondary" && color !== "white"
          ? `1px solid ${colors[color][variant].text}`
          : "none"
      }
      borderRadius={50}
      display="flex"
      alignItems="center"
      color={colors[color][variant].text}
      p={`0 ${padding[size]}`}
      minW={0}
      gap={size === "xs" ? theme.spacing.xxs.px : theme.spacing.xs.px}
      height={height[size]}
      fontSize={fonts[size].fontSize}
      fontWeight={600}
      transition="all 0.1s ease-in"
      _hover={{ backgroundColor: colors[color][variant].hover }}
      _active={{ opacity: 0.9, transform: "scale(0.95)" }}
      _disabled={{
        opacity: 1,
        color: theme.colors.ui.grey_02.hex,
        backgroundColor: theme.colors.ui.grey_04.hex,
        cursor: "not-allowed",
      }}
      {...rest}
    >
      {iconLeft && iconLeft}
      {children}
      {iconRight && iconRight}
    </ChakraButton>
  );
};
