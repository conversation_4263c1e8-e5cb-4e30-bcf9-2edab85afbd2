import {
  Box,
  Flex,
  NumberInput,
  NumberInputField,
  VStack,
} from "@chakra-ui/react";
import Image from "next/image";
import { useEffect, useRef, useState } from "react";

import { FileField, IScaleItemRecord } from "@/data/types";
import { theme } from "@/styles/theme";

export interface ICodingResponseProps {
  numberOfFields: number;
  digitsPerField: number;
  onChange?: (values: (number | null)[]) => void;
  onBlur?: (values: (number | null)[]) => void;
  onComplete?: (values: (number | null)[]) => void;
  results?: boolean[];
  isFocussed?: boolean;
  item: IScaleItemRecord;
}

export const CodingResponse = ({
  numberOfFields = 4,
  digitsPerField = 2,
  onChange,
  onBlur,
  onComplete,
  results,
  isFocussed = false,
  item,
}: ICodingResponseProps) => {
  const inputRefs = useRef<HTMLInputElement[]>([]);
  const [unlockedIndex, setUnlockedIndex] = useState(0);

  const [inputValues, setInputValues] = useState<(number | null)[]>(
    Array(numberOfFields).fill(null)
  );
  const [isCompleted, setIsCompleted] = useState<boolean[]>(
    Array(numberOfFields).fill(false)
  );
  const [isFocused, setIsFocused] = useState<boolean[]>(
    Array(numberOfFields).fill(false)
  );

  useEffect(() => {
    if (onChange) {
      onChange(inputValues);
    }
  }, [inputValues, onChange]);

  useEffect(() => {
    if (isFocussed && inputRefs.current[0]) {
      inputRefs.current[0].focus();
    }
  }, [isFocussed]);

  const handleFocus = (index: number) => {
    const newIsFocused = isFocused.map((_, i) => i === index);
    setIsFocused(newIsFocused);
  };

  const handleKeyDown = (event, index) => {
    if (event.key === "Backspace" && inputRefs.current[index]?.value === "") {
      if (index > 0) {
        const newValues = [...inputValues];
        const newIsCompleted = [...isCompleted];

        newValues[index - 1] = null;
        newIsCompleted[index - 1] = false;

        setInputValues(newValues);
        setIsCompleted(newIsCompleted);

        inputRefs.current[index - 1].focus();
        inputRefs.current[index - 1].value = "";
      }
    }
  };

  const handleBlur = (index: number) => {
    const newIsFocused = isFocused.map(() => false);
    setIsFocused(newIsFocused);

    const latestValues = inputRefs.current.map((inputRef) =>
      inputRef ? Number(inputRef.value) : null
    );
    if (onBlur) {
      onBlur(latestValues);
    }
  };

  const checkCompletion = (newIsCompleted: boolean[], index: number) => {
    if (
      newIsCompleted.every(Boolean) &&
      index === numberOfFields - 1 &&
      onComplete
    ) {
      const latestValues = inputRefs.current.map((inputRef) =>
        inputRef ? Number(inputRef.value) : null
      );
      onComplete(latestValues);
      inputRefs.current.forEach((inputRef) => inputRef?.blur());
    }
  };

  useEffect(() => {
    if (isFocussed && unlockedIndex < numberOfFields) {
      inputRefs.current[unlockedIndex]?.focus();
    }
  }, [unlockedIndex, isFocussed, numberOfFields]);

  const handleChange = (value: number | null, index: number) => {
    const newValues = [...inputValues];
    newValues[index] = value;
    setInputValues(newValues);

    const newIsCompleted = [...isCompleted];
    newIsCompleted[index] =
      (value !== null && value?.toString().length >= digitsPerField) ?? false;
    setIsCompleted(newIsCompleted);

    checkCompletion(newIsCompleted, index);

    if (
      value !== null &&
      value.toString().length >= digitsPerField &&
      index < numberOfFields - 1
    ) {
      setUnlockedIndex(index + 1);
    }
  };

  const getOutlineStyle = (index: number) => {
    const focusedStyle = `3px solid ${theme.colors.primary.purple.hex}`;
    const completedGreenStyle = `3px solid ${theme.colors.ui.alert_green_01.hex}`;
    const completedRedStyle = `3px solid ${theme.colors.ui.alert_red_01.hex}`;
    const transparentStyle = "3px solid transparent";

    if (isFocused[index]) {
      return focusedStyle;
    }

    if (isCompleted[index]) {
      if (results?.[index] !== undefined) {
        return results[index] ? completedGreenStyle : completedRedStyle;
      }
      return focusedStyle;
    }

    return transparentStyle;
  };

  const getTextColor = (isCompleted: boolean, result: boolean | undefined) => {
    const successColor = theme.colors.ui.alert_green_01.hex;
    const incorrectColor = theme.colors.ui.alert_red_01.hex;
    const defaultColor = theme.colors.primary.purple.hex;

    if (!isCompleted) {
      return defaultColor;
    }

    if (result === undefined) {
      return defaultColor;
    }

    return result ? successColor : incorrectColor;
  };

  const getBackgroundColor = (
    isCompleted: boolean,
    result: boolean | undefined
  ) => {
    const successColor = theme.colors.ui.alert_green_02.hex;
    const incorrectColor = theme.colors.ui.alert_red_02.hex;
    const purpleColor = theme.colors.secondary.purple_05.hex;
    const defaultColor = theme.colors.ui.grey_04.hex;

    if (!isCompleted) {
      return defaultColor;
    }

    if (result === undefined) {
      return purpleColor;
    }

    return result ? successColor : incorrectColor;
  };

  const itemAnswers = item.scaleItemResponseValues.split(",");
  const responseMedia = item.scaleItemMediaStimulus;

  return (
    <Flex
      gap={`${theme.spacing.md.px} ${theme.spacing.xs.px}`}
      alignItems="center"
      maxW="830px"
      flexWrap="wrap"
    >
      {Array.from({ length: numberOfFields }).map((_, index) => {
        const itemAnswer = itemAnswers[index];
        const itemMedia = responseMedia.find(
          (media) => media.title === itemAnswer
        );

        return (
          <VStack key={`coding-response-${index}`}>
            <Box
              bgColor={theme.colors.tertiary.yellow_05.hex}
              border={`1px solid rgba(0, 0, 0, 0.05)`}
              borderRadius="10px"
              p="11px 5px"
              w="38px"
              display="flex"
              justifyContent="center"
              alignItems="center"
              transition="opacity 0.2s ease-in"
              opacity={isCompleted[index] ? "0.2" : "1"}
            >
              {itemMedia && (
                <Image src={itemMedia.url} width={30} height={30} alt={""} />
              )}
            </Box>
            <Box
              position="relative"
              sx={{
                "&::after": {
                  content: '""',
                  position: "absolute",
                  bottom: "-15px",
                  left: "50%",
                  transition: "all 0.2s ease-in",
                  transform: isFocused[index]
                    ? "translate(-50%, 0)"
                    : "translate(-50%, 5px)",
                  width: "4px",
                  height: "4px",
                  borderRadius: "4px",
                  backgroundColor: theme.colors.primary.purple.hex,
                  opacity: isFocused[index] ? "1" : "0",
                },
              }}
            >
              <NumberInput
                min={0}
                max={digitsPerField === 1 ? 9 : 99}
                value={inputValues[index] ?? ""}
              >
                <NumberInputField
                  ref={(el) =>
                    (inputRefs.current[index] = el as HTMLInputElement)
                  }
                  padding={0}
                  width="38px"
                  height="50px"
                  paddingInlineEnd={0}
                  paddingInlineStart={0}
                  maxLength={digitsPerField}
                  fontSize={theme.v2Text.headings.lg.fontSize}
                  fontWeight={theme.v2Text.headings.lg.fontWeight}
                  display="flex"
                  alignItems="center"
                  justifyContent="center"
                  textAlign="center"
                  border="none"
                  outlineOffset="-2px"
                  color={getTextColor(isCompleted[index], results?.[index])}
                  backgroundColor={getBackgroundColor(
                    isCompleted[index],
                    results?.[index]
                  )}
                  outline={getOutlineStyle(index)}
                  transition="all 0.2s ease-in"
                  _focus={{
                    backgroundColor: theme.colors.primary.white.hex,
                  }}
                  onKeyDown={(e) => handleKeyDown(e, index)}
                  onKeyUp={() => handleBlur(index)}
                  onFocus={() => handleFocus(index)}
                  onChange={(e) => {
                    const numberValue = e.target.value
                      ? Number(e.target.value)
                      : null;
                    handleChange(numberValue, index);
                  }}
                />
              </NumberInput>
            </Box>
          </VStack>
        );
      })}
    </Flex>
  );
};
