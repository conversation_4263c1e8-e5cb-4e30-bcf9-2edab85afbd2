import { Button } from "@chakra-ui/react";
import { useEffect, useRef, useState } from "react";

import { theme } from "@/styles/theme";

import { AudioButton } from "../AudioButton/AudioButton";
import { KeyboardNudge } from "../KeyboardNudge/KeyboardNudge";

export type ButtonState =
  | "default"
  | "selected"
  | "correct"
  | "incorrect"
  | "disabled";

interface IStates {
  [key: string]: {
    backgroundColor: string;
    borderColor: string;
    nudgeColor?: string;
  };
}

export interface IMultiChoiceTextProps {
  state?: ButtonState;
  label: string;
  disabled?: boolean;
  keyToPress: string;
  onButtonClick: () => void;
  audio?: string;
}

export const MultiChoiceText = ({
  state = "default",
  label,
  disabled = false,
  keyToPress,
  onButtonClick,
  audio,
  ...rest
}: IMultiChoiceTextProps) => {
  const [isPlaying, setIsPlaying] = useState(false);

  const audioRef = useRef(new Audio(audio));

  const states: IStates = {
    default: {
      backgroundColor: theme.colors.primary.white.hex,
      borderColor: theme.colors.ui.grey_03.hex,
    },
    selected: {
      backgroundColor: theme.colors.secondary.purple_04.hex,
      borderColor: theme.colors.secondary.purple_02.hex,
      nudgeColor: theme.colors.secondary.purple_02.hex,
    },
    correct: {
      backgroundColor: theme.colors.ui.alert_green_02.hex,
      borderColor: theme.colors.ui.alert_green_01.hex,
      nudgeColor: theme.colors.ui.alert_green_01.hex,
    },
    incorrect: {
      backgroundColor: theme.colors.ui.alert_red_02.hex,
      borderColor: theme.colors.ui.alert_red_01.hex,
      nudgeColor: theme.colors.ui.alert_red_01.hex,
    },
    disabled: {
      backgroundColor: theme.colors.primary.white.hex,
      borderColor: theme.colors.ui.grey_03.hex,
    },
  };

  const handleKeyDown = (event) => {
    if (disabled === true || event.key !== keyToPress.toString()) return;
    onButtonClick();
  };

  useEffect(() => {
    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [disabled]);

  const handleMouseEnter = () => {
    setIsPlaying(true);
    audioRef.current.play();
  };

  const handleMouseLeave = () => {
    setIsPlaying(false);
    audioRef.current.pause();
    audioRef.current.currentTime = 0;
  };

  return (
    <Button
      data-testid="multi-choice-text"
      bg={states[state].backgroundColor}
      border={`2px solid ${states[state].borderColor}`}
      isDisabled={disabled}
      onClick={onButtonClick}
      display="flex"
      alignItems="center"
      justifyContent="flex-start"
      py={theme.spacing.md.px}
      px={theme.spacing.sm.px}
      gap={theme.spacing.xs.px}
      fontSize={theme.v2Text.headings.md.fontSize}
      {...rest}
    >
      {audio ? (
        <div onMouseEnter={handleMouseEnter} onMouseLeave={handleMouseLeave}>
          <AudioButton size="sm" isPlaying={isPlaying} />
        </div>
      ) : (
        <KeyboardNudge backgroundColor={states[state].nudgeColor || undefined}>
          {keyToPress}
        </KeyboardNudge>
      )}{" "}
      {label}
    </Button>
  );
};
