import {
  Select as ChakraSelect,
  SelectProps as ChakraSelectProps,
} from "@chakra-ui/react";
import React, { forwardRef } from "react";

interface SelectProps extends ChakraSelectProps {
  isSuccess?: boolean;
}

export const Select = forwardRef<HTMLSelectElement, SelectProps>(
  ({ isSuccess, ...props }, ref) => {
    return <ChakraSelect ref={ref} {...props} />;
  }
);

Select.displayName = "Select";
