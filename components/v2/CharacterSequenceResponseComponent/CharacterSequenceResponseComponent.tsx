import { Box, Flex, PinInput, PinInputField } from "@chakra-ui/react";
import { useEffect, useRef, useState } from "react";

import { theme } from "@/styles/theme";

export interface ICharacterSequenceProps {
  numberOfFields: number;
  digitsPerField: number;
  onChange?: (values: (string | null)[]) => void;
  onBlur?: (values: (string | null)[]) => void;
  onComplete?: (values: (string | null)[]) => void;
  results?: boolean[];
  isFocussed?: boolean;
}

export const CharacterSequenceResponseComponent = ({
  numberOfFields = 4,
  digitsPerField = 2,
  onChange,
  onBlur,
  onComplete,
  results,
  isFocussed = false,
}: ICharacterSequenceProps) => {
  const inputRefs = useRef<HTMLInputElement[]>([]);
  const [unlockedIndex, setUnlockedIndex] = useState(0);

  const [inputValues, setInputValues] = useState<(string | null)[]>(
    Array(numberOfFields).fill(null)
  );
  const [isCompleted, setIsCompleted] = useState<boolean[]>(
    Array(numberOfFields).fill(false)
  );
  const [isFocused, setIsFocused] = useState<boolean[]>(
    Array(numberOfFields).fill(false)
  );

  useEffect(() => {
    if (onChange) {
      onChange(inputValues);
    }
  }, [inputValues, onChange]);

  useEffect(() => {
    if (isFocussed && inputRefs.current[0]) {
      inputRefs.current[0].focus();
    }
  }, [isFocussed]);

  const handleFocus = (index: number) => {
    const newIsFocused = isFocused.map((_, i) => i === index);
    setIsFocused(newIsFocused);
  };

  const handleBlur = (index: number) => {
    const newIsFocused = isFocused.map(() => false);
    setIsFocused(newIsFocused);

    const latestValues = inputRefs.current.map((inputRef) =>
      inputRef ? inputRef.value : null
    );

    if (onBlur) {
      onBlur(latestValues);
    }
  };

  const checkCompletion = (newIsCompleted: boolean[], index: number) => {
    if (
      newIsCompleted.every(Boolean) &&
      index === numberOfFields - 1 &&
      onComplete
    ) {
      const latestValues = inputRefs.current.map((inputRef) =>
        inputRef ? inputRef.value : null
      );
      onComplete(latestValues);
      inputRefs.current.forEach((inputRef) => inputRef?.blur());
    }
  };

  useEffect(() => {
    if (isFocussed && unlockedIndex < numberOfFields) {
      inputRefs.current[unlockedIndex]?.focus();
    }
  }, [unlockedIndex, isFocussed, numberOfFields]);

  const handleChange = (value: string | null, index: number) => {
    const newValues = [...inputValues];
    newValues[index] = value;
    setInputValues(newValues);

    const newIsCompleted = [...isCompleted];
    newIsCompleted[index] =
      (value !== null && value.length >= digitsPerField) ?? false;
    setIsCompleted(newIsCompleted);

    checkCompletion(newIsCompleted, index);

    if (value !== null && value.length >= 0 && index < numberOfFields - 1) {
      setUnlockedIndex(index + 1);
    }
  };

  const getOutlineStyle = (index: number) => {
    const focusedStyle = `3px solid ${theme.colors.primary.purple.hex}`;
    const completedGreenStyle = `3px solid ${theme.colors.ui.alert_green_01.hex}`;
    const completedRedStyle = `3px solid ${theme.colors.ui.alert_red_01.hex}`;
    const transparentStyle = "3px solid transparent";

    if (isFocused[index]) {
      return focusedStyle;
    }

    if (isCompleted[index]) {
      if (results?.[index] !== undefined) {
        return results[index] ? completedGreenStyle : completedRedStyle;
      }
      return focusedStyle;
    }

    return transparentStyle;
  };

  const getTextColor = (isCompleted: boolean, result: boolean | undefined) => {
    const successColor = theme.colors.ui.alert_green_01.hex;
    const incorrectColor = theme.colors.ui.alert_red_01.hex;
    const defaultColor = theme.colors.primary.purple.hex;

    if (!isCompleted) {
      return defaultColor;
    }

    if (result === undefined) {
      return defaultColor;
    }

    return result ? successColor : incorrectColor;
  };

  const getBackgroundColor = (
    isCompleted: boolean,
    result: boolean | undefined
  ) => {
    const successColor = theme.colors.ui.alert_green_02.hex;
    const incorrectColor = theme.colors.ui.alert_red_02.hex;
    const purpleColor = theme.colors.secondary.purple_05.hex;
    const defaultColor = theme.colors.ui.grey_04.hex;

    if (!isCompleted) {
      return defaultColor;
    }

    if (result === undefined) {
      return purpleColor;
    }

    return result ? successColor : incorrectColor;
  };

  return (
    <>
      <Flex gap={theme.spacing.sm.px} alignItems="center">
        {Array.from({ length: numberOfFields }).map((_, index) => (
          <Box
            key={`numerical-response-${index}`}
            position="relative"
            sx={{
              "&::after": {
                content: '""',
                position: "absolute",
                bottom: "-20px",
                left: "50%",
                transition: "all 0.2s ease-in",
                transform: isFocused[index]
                  ? "translate(-50%, 0)"
                  : "translate(-50%, 5px)",
                width: "8px",
                height: "8px",
                borderRadius: "8px",
                backgroundColor: theme.colors.primary.purple.hex,
                opacity: isFocused[index] ? "1" : "0",
              },
            }}
          >
            <PinInput
              placeholder=""
              type="alphanumeric"
              value={inputValues[index] ?? ""}
              isDisabled={!isFocussed || index > unlockedIndex}
            >
              <PinInputField
                tabIndex={index}
                ref={(el) =>
                  (inputRefs.current[index] = el as HTMLInputElement)
                }
                padding={0}
                width={digitsPerField === 1 ? "48px" : "80px"}
                height="80px"
                paddingInlineEnd={0}
                paddingInlineStart={0}
                maxLength={digitsPerField}
                fontSize={theme.v2Text.headings.xl.fontSize}
                fontWeight={theme.v2Text.headings.xl.fontWeight}
                display="flex"
                alignItems="center"
                justifyContent="center"
                textAlign="center"
                border="none"
                outlineOffset="-2px"
                color={getTextColor(isCompleted[index], results?.[index])}
                backgroundColor={getBackgroundColor(
                  isCompleted[index],
                  results?.[index]
                )}
                outline={getOutlineStyle(index)}
                transition="all 0.2s ease-in"
                _focus={{
                  backgroundColor: theme.colors.primary.white.hex,
                }}
                onBlur={() => handleBlur(index)}
                onFocus={() => handleFocus(index)}
                onChange={(e) => {
                  const stringValue = e.target.value || null;
                  if (index < inputRefs.current.length - 1) {
                    inputRefs.current[index + 1].focus();
                  }
                  handleChange(stringValue, index);
                  const latestValues = inputRefs.current.map((inputRef) =>
                    inputRef ? inputRef.value : null
                  );
                  if (onBlur) {
                    onBlur(latestValues);
                  }
                }}
              />
            </PinInput>
          </Box>
        ))}
      </Flex>
    </>
  );
};
