import { Button } from "@chakra-ui/react";
import React, { forwardRef, ReactNode } from "react";

import { theme } from "@/styles/theme";

export interface IButtonWrapperProps {
  disabled?: boolean;
  children: ReactNode;
  hoverDisabled?: boolean;
  onClick?: () => void;
}

const ButtonWrapper = forwardRef<HTMLButtonElement, IButtonWrapperProps>(
  ({ children, disabled = false, hoverDisabled = false, onClick }, ref) => {
    return (
      <Button
        ref={ref}
        backgroundColor={theme.colors.secondary.purple_03.rgb}
        disabled={disabled}
        position="relative"
        width="180px"
        height="180px"
        p={0}
        minW={0}
        onClick={onClick}
        borderRadius={theme.border.radius.xxl.px}
        transition="all 0.1s ease-in"
        _hover={
          hoverDisabled
            ? {}
            : { backgroundColor: theme.colors.secondary.purple_04.rgb }
        }
        _active={hoverDisabled ? {} : { opacity: 0.9, transform: "scale(0.9)" }}
        _disabled={{
          opacity: 1,
          backgroundColor: theme.colors.ui.grey_04.hex,
          cursor: "not-allowed",
        }}
      >
        {children}
      </Button>
    );
  }
);

ButtonWrapper.displayName = "ButtonWrapper";

export { ButtonWrapper };
