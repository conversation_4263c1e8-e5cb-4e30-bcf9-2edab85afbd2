import { Box, HStack, VStack } from "@chakra-ui/react";
import {
  ArrowPathIcon,
  MicrophoneIcon,
  PlayIcon,
} from "@heroicons/react/24/solid";
import { motion } from "framer-motion";
import dynamic from "next/dynamic";
import { useEffect, useState } from "react";
import { useAudioRecorder } from "react-audio-voice-recorder";
import ReactHowler from "react-howler";

import { theme } from "@/styles/theme";

import { AudioButton } from "../AudioButton/AudioButton";
import { Text } from "../Text/Text";
import { Tooltip } from "../Tooltip/Tooltip";
import { ButtonWrapper } from "./Components/ButtonWrapper";
import RecordingAnimation from "./Lottie/LottieRecordingAnimation.json";
import SavedAnimation from "./Lottie/LottieSavedAnimation.json";

const Lottie = dynamic(() => import("lottie-react"), { ssr: false });

export enum RecordingState {
  Play,
  Record,
  Loading,
  Recording,
  Saving,
  Saved,
}

export interface IVoiceResponseProps {
  audioClip: string;
  isSaving?: boolean;
  isSample?: boolean;
  isFeedbackPanelVisible?: boolean;
  onRecordingComplete: (blob: Blob) => void;
}

export const VoiceResponse = ({
  audioClip,
  isSaving,
  isSample,
  isFeedbackPanelVisible,
  onRecordingComplete,
}: IVoiceResponseProps) => {
  const { startRecording, stopRecording, recordingBlob } = useAudioRecorder(
    {
      noiseSuppression: true,
      echoCancellation: true,
    },
    undefined,
    { audioBitsPerSecond: 16000 }
  );
  const [timesAudioPlayed, setTimesAudioPlayed] = useState(0);
  const [isAudioPlaying, setIsAudioPlaying] = useState(false);
  const [hasFiredCompletion, setHasFiredCompletion] = useState(false);
  const [hasStoppedRecording, setHasStoppedRecording] = useState(false);
  const [timeoutId, setTimeoutId] = useState<NodeJS.Timeout | null>(null);
  const [hasAudioBeenClicked, setHasAudioBeenClicked] = useState(false);
  const [hasRecordButtonBeenClicked, setHasRecordButtonBeenClicked] =
    useState(false);
  const [recordingState, setRecordingState] = useState<RecordingState>(
    RecordingState.Play
  );

  useEffect(() => {
    if (
      recordingState === RecordingState.Play &&
      timesAudioPlayed > 0 &&
      // timesAudioPlayed <= 2 &&
      !isAudioPlaying
    ) {
      setTimeout(() => {
        setRecordingState(RecordingState.Record);
      }, 750);
    }
  }, [isAudioPlaying, recordingState, timesAudioPlayed]);

  useEffect(() => {
    if (!recordingBlob || hasFiredCompletion) return;
    onRecordingComplete(recordingBlob);
    setHasFiredCompletion(true);
  }, [recordingBlob, onRecordingComplete, hasFiredCompletion]);

  useEffect(() => {
    if (hasStoppedRecording && !isSaving) {
      setRecordingState(RecordingState.Saved);
    }
  }, [isSaving, hasStoppedRecording]);

  const onAudioButtonClick = (): void => {
    setIsAudioPlaying(true);
    setTimesAudioPlayed((prev) => prev + 1);
    setTooltipMessage(null);
    setHasAudioBeenClicked(true);
  };

  const onReplayAudioClick = (): void => {
    setIsAudioPlaying(true);
    setTimesAudioPlayed((prev) => prev + 1);
    setRecordingState(RecordingState.Play);
  };

  const handleRecordClick = () => {
    startRecording();
    setRecordingState(RecordingState.Loading);
    setTooltipMessage(null);
    setHasRecordButtonBeenClicked(true);

    setTimeout(() => {
      setRecordingState(RecordingState.Recording);
    }, 1200);

    if (timeoutId) {
      clearTimeout(timeoutId);
    }

    const recordingTimeoutId = setTimeout(() => {
      handleStopRecording();
    }, 30000);

    setTimeoutId(recordingTimeoutId);
  };

  const handleStopRecording = () => {
    setRecordingState(RecordingState.Saving);
    stopRecording();
    setHasStoppedRecording(true);
    if (timeoutId) {
      clearTimeout(timeoutId);
      setTimeoutId(null);
    }
  };

  const [tooltipMessage, setTooltipMessage] = useState<string | null>(null);

  useEffect(() => {
    const tooltipConfig = {
      [RecordingState.Play]: {
        duration: 5000,
        message: "👋 Click to play!",
      },
      [RecordingState.Record]: {
        duration: isAudioPlaying ? null : 5000,
        message: "👋 Ready? Click to record",
      },
      [RecordingState.Recording]: {
        duration: 5000,
        message: "👋 Press Stop when done",
      },
    };

    const config = tooltipConfig[recordingState];
    if (!config || !config.duration) return;

    if (recordingState === RecordingState.Play && hasAudioBeenClicked) {
      return;
    }

    if (
      recordingState === RecordingState.Record &&
      hasRecordButtonBeenClicked
    ) {
      return;
    }

    const timeout = setTimeout(() => {
      setTooltipMessage(config.message);
    }, config.duration);

    return () => clearTimeout(timeout);
  }, [
    recordingState,
    isAudioPlaying,
    hasAudioBeenClicked,
    hasRecordButtonBeenClicked,
  ]);

  useEffect(() => {
    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, []);

  const renderState = () => {
    switch (recordingState) {
      case RecordingState.Play:
        return (
          <>
            {isSample && tooltipMessage !== null && !isFeedbackPanelVisible ? (
              <Tooltip
                hasSound
                isOpen={true}
                label={tooltipMessage}
                shouldWrapChildren
              >
                <AudioButton
                  data-testid="audio-play-button"
                  isPlaying={isAudioPlaying}
                  size="xl"
                  onClick={onAudioButtonClick}
                />
              </Tooltip>
            ) : (
              <AudioButton
                data-testid="audio-play-button"
                isPlaying={isAudioPlaying}
                size="xl"
                onClick={onAudioButtonClick}
              />
            )}
            <HStack height="24px"></HStack>
          </>
        );

      case RecordingState.Record:
        return (
          <>
            {isSample && tooltipMessage !== null ? (
              <Tooltip
                placement="top"
                hasSound
                isOpen={true}
                label={tooltipMessage}
              >
                <ButtonWrapper
                  data-testid="record-button"
                  onClick={handleRecordClick}
                >
                  <MicrophoneIcon
                    width="110px"
                    height="110px"
                    color={theme.colors.primary.purple.hex}
                  />
                </ButtonWrapper>
              </Tooltip>
            ) : (
              <ButtonWrapper
                data-testid="record-button"
                onClick={handleRecordClick}
              >
                <MicrophoneIcon
                  width="110px"
                  height="110px"
                  color={theme.colors.primary.purple.hex}
                />
              </ButtonWrapper>
            )}
            {recordingState === RecordingState.Record &&
              timesAudioPlayed <= 1 && ( // Replay audio
                <HStack
                  height="24px"
                  alignItems="center"
                  onClick={onReplayAudioClick}
                  style={{ cursor: "pointer" }}
                >
                  <PlayIcon
                    width="24px"
                    height="24px"
                    color={theme.colors.primary.purple.hex}
                  />

                  <Text
                    element="h3"
                    variant="md"
                    color={theme.colors.primary.purple.hex}
                    style={{ textDecoration: "underline" }}
                  >
                    Replay audio
                  </Text>
                </HStack>
              )}
          </>
        );

      case RecordingState.Loading:
        return (
          <>
            <ButtonWrapper
              data-testid="loading"
              hoverDisabled
              onClick={handleRecordClick}
            >
              <motion.div
                animate={{ rotate: [0, 360] }}
                transition={{
                  duration: 1,
                  repeat: Infinity,
                  repeatType: "loop",
                  ease: "linear",
                }}
              >
                <ArrowPathIcon
                  width="82px"
                  height="82px"
                  color={theme.colors.primary.purple.hex}
                />
              </motion.div>
            </ButtonWrapper>
            <Text
              element="h3"
              variant="md"
              color={theme.colors.primary.purple.hex}
              style={{ height: "24px" }}
            >
              One sec...
            </Text>
          </>
        );

      case RecordingState.Recording:
        return (
          <>
            {isSample && tooltipMessage !== null ? (
              <Tooltip placement="top" isOpen={true} label={tooltipMessage}>
                <ButtonWrapper
                  data-testid="stop-recording-button"
                  onClick={handleStopRecording}
                >
                  <Lottie
                    animationData={RecordingAnimation}
                    loop={true}
                    autoplay={true}
                    style={{
                      height: "100px",
                      width: "100px",
                      display: "block",
                    }}
                  />
                </ButtonWrapper>
              </Tooltip>
            ) : (
              <ButtonWrapper
                data-testid="stop-recording-button"
                onClick={handleStopRecording}
              >
                <Lottie
                  animationData={RecordingAnimation}
                  loop={true}
                  autoplay={true}
                  style={{
                    height: "100px",
                    width: "100px",
                    display: "block",
                  }}
                />
              </ButtonWrapper>
            )}
            <HStack
              height="24px"
              alignItems="center"
              onClick={handleStopRecording}
              style={{ cursor: "pointer" }}
            >
              <Box
                width="14px"
                height="14px"
                borderRadius="2px"
                backgroundColor={theme.colors.primary.purple.hex}
              />

              <Text
                element="h3"
                variant="md"
                color={theme.colors.primary.purple.hex}
                style={{ textDecoration: "underline" }}
              >
                Stop recording
              </Text>
            </HStack>
          </>
        );

      case RecordingState.Saving:
        return (
          <>
            <ButtonWrapper data-testid="saving" hoverDisabled>
              <motion.div
                animate={{ rotate: [0, 360] }}
                transition={{
                  duration: 1,
                  repeat: Infinity,
                  repeatType: "loop",
                  ease: "linear",
                }}
              >
                <ArrowPathIcon
                  width="82px"
                  height="82px"
                  color={theme.colors.primary.purple.hex}
                />
              </motion.div>
            </ButtonWrapper>
            <Text
              element="h3"
              variant="md"
              color={theme.colors.primary.purple.hex}
              style={{ height: "24px" }}
            >
              Saving...
            </Text>
          </>
        );

      case RecordingState.Saved:
        return (
          <>
            <ButtonWrapper data-testid="saved" hoverDisabled>
              <Lottie
                animationData={SavedAnimation}
                loop={false}
                autoplay={true}
                style={{
                  height: "110px",
                  width: "110px",
                  display: "block",
                }}
              />
            </ButtonWrapper>
            <Text
              element="h3"
              variant="md"
              color={theme.colors.primary.purple.hex}
              style={{ height: "24px" }}
            >
              Saved!
            </Text>
          </>
        );
    }
  };

  return (
    <VStack gap={theme.spacing.sm.px} alignItems="center">
      {audioClip && (
        <ReactHowler
          src={audioClip}
          playing={isAudioPlaying}
          onEnd={() => setIsAudioPlaying(false)}
        />
      )}
      {renderState()}
    </VStack>
  );
};
