import { Button } from "@chakra-ui/react";
import { XMarkIcon } from "@heroicons/react/24/solid";

import { theme } from "@/styles/theme";

export type ButtonSize = "sm" | "md" | "lg";

export interface ICloseButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  size?: ButtonSize;
  disabled?: boolean;
  onClick?: () => void;
  onKeyDown?: () => void;
  keyToPress?: string;
}

export const CloseButton = ({
  size = "md",
  disabled = false,
  onClick,
  onKeyDown,
  keyToPress,
  ...rest
}: ICloseButtonProps) => {
  const sizes = {
    sm: {
      overall: "24px",
      icon: "20px",
    },
    md: {
      overall: "32px",
      icon: "24px",
    },
    lg: {
      overall: "48px",
      icon: "34px",
    },
  };

  const handleClick = (): void => {
    if (onClick) {
      onClick();
    }
  };

  const handleKeyPress = (
    event: React.KeyboardEvent<HTMLButtonElement>
  ): void => {
    if (onKeyDown && keyToPress && event.key === keyToPress) {
      onKeyDown();
    }
  };

  return (
    <Button
      data-testid="close-button"
      onClick={handleClick}
      onKeyDown={handleKeyPress}
      isDisabled={disabled}
      backgroundColor={theme.colors.primary.white.hex}
      border={`1px solid ${theme.colors.ui.grey_04.hex}`}
      borderRadius={50}
      padding="0"
      display="flex"
      alignItems="center"
      width={sizes[size].overall}
      height={sizes[size].overall}
      minW={0}
      minH={0}
      color={theme.colors.primary.purple.hex}
      transition="all 0.1s ease-in"
      _hover={{
        backgroundColor: theme.colors.secondary.purple_05.hex,
        borderColor: theme.colors.secondary.purple_05.hex,
      }}
      _active={{
        backgroundColor: theme.colors.secondary.purple_05.hex,
        borderColor: theme.colors.secondary.purple_05.hex,
        opacity: 0.9,
        transform: "scale(0.95)",
      }}
      _disabled={{
        opacity: 1,
        color: theme.colors.ui.grey_03.hex,
        backgroundColor: theme.colors.ui.grey_04.hex,
        cursor: "not-allowed",
      }}
      {...rest}
    >
      <XMarkIcon width={sizes[size].icon} height={sizes[size].icon} />
    </Button>
  );
};
