import { Box, Button, Image } from "@chakra-ui/react";
import { LottieRef } from "lottie-react";
import dynamic from "next/dynamic";
import { useEffect, useRef } from "react";

import { theme } from "@/styles/theme";

import { Text } from "../Text/Text";
import CompleteCheckAnimation from "./Lottie/LottieCompleteCheck.json";

const Lottie = dynamic(() => import("lottie-react"), { ssr: false });

export type ScaleItemState = "active" | "complete" | "disabled";

export interface IScaleItemProps {
  state?: ScaleItemState;
  icon: string;
  title: string;
  isSpellCheckOn?: boolean;
  onClick?: () => void;
}

export const ScaleItem = ({
  state = "active",
  icon,
  title,
  onClick,
}: IScaleItemProps) => {
  const lottieRef: LottieRef = useRef(null);

  const handleAnimationLoad = () => {
    if (lottieRef.current && state !== "complete") {
      lottieRef.current.goToAndStop(45, true);
    }
  };

  useEffect(() => {
    if (lottieRef.current && state === "complete") {
      lottieRef.current.goToAndPlay(1, true);
    }
  }, [state]);

  const handleClick = (): void => {
    if (onClick && state === "active") {
      onClick();
    }
  };

  return (
    <Button
      data-testid="scale-item"
      onClick={handleClick}
      isDisabled={state === "disabled"}
      transition="all 0.1s ease-in"
      position="relative"
      display="flex"
      flexDir="column"
      alignItems="center"
      justifyContent="center"
      padding={theme.spacing.md.px}
      bgColor={theme.colors.primary.white.hex}
      borderRadius={theme.border.radius.xl.px}
      width="100%"
      maxW="230px"
      height="auto"
      minH="138px"
      whiteSpace="normal"
      gap={theme.spacing.sm.px}
      _hover={state === "active" ? { transform: "scale(0.98)" } : {}}
      _active={
        state === "active" ? { opacity: 0.9, transform: "scale(0.95)" } : {}
      }
      _disabled={{
        opacity: 0.5,
        cursor: "default",
      }}
    >
      <div className="w-[96px] h-[96px] relative">
        {state === "complete" ? (
          <Lottie
            lottieRef={lottieRef}
            animationData={CompleteCheckAnimation}
            loop={false}
            onDOMLoaded={handleAnimationLoad}
            className="absolute w-[152px] h-[152px] block top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2"
          />
        ) : (
          <Image src={icon} alt={title} w="96px" h="96px" />
        )}
      </div>
      <Text variant="sm" element="h4">
        {title}
      </Text>
    </Button>
  );
};
