import { Box, Container } from "@chakra-ui/react";
import { useEffect, useState } from "react";
import ReactHowler from "react-howler";

import { InstructorIcon } from "@/components/v2";
import { AudioButton } from "@/components/v2/AudioButton/AudioButton";
import {
  Button,
  ButtonColors,
  ButtonVariant,
} from "@/components/v2/Button/Button";
import { theme } from "@/styles/theme";

import { KeyboardNudge } from "../KeyboardNudge/KeyboardNudge";
import { Panel } from "../Panel/Panel";
import { Text } from "../Text/Text";

export type FeedbackState =
  | "resting"
  | "active"
  | "transparent"
  | "transparentResting"
  | "skip"
  | "positive"
  | "nudge"
  | "timeout"
  | "negative";

export type FeedbackMode = "light" | "dark";

interface IStates {
  [key: string]: {
    backgroundColor: string;
    avatarColor?: string;
    buttonVariant: ButtonVariant;
    buttonColor: ButtonColors;
    defaultButtonText: string;
  };
}

export interface IFeedbackPanelProps {
  state?: FeedbackState | string;
  mode?: FeedbackMode;
  audioInstruction?: string;
  audioFile?: string;
  isAudioPlaying?: boolean;
  buttonText?: string;
  onButtonClick?: () => void;
  onKeyDown?: () => void;
  keyToPress?: string;
}

const CONTINUE_TEXT = "Continue";
const SKIP_TEXT = "Skip";
const ARE_YOU_SURE_TEXT = "Are you sure?";

export const FeedbackPanel = ({
  state = "resting",
  mode = "light",
  audioInstruction,
  audioFile,
  isAudioPlaying = false,
  buttonText,
  onButtonClick,
  onKeyDown,
  keyToPress,
}: IFeedbackPanelProps) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [skipClicked, setSkipClicked] = useState(false);

  const onAudioButtonClick = (): void => {
    setIsPlaying(true);
  };

  const states: IStates = {
    resting: {
      backgroundColor: theme.colors.primary.white.hex,
      buttonVariant: "primary",
      buttonColor: "purple",
      defaultButtonText: CONTINUE_TEXT,
    },
    active: {
      backgroundColor: theme.colors.primary.white.hex,
      buttonVariant: "primary",
      buttonColor: "purple",
      defaultButtonText: CONTINUE_TEXT,
    },
    transparent: {
      backgroundColor: "transparent",
      buttonVariant: "primary",
      buttonColor: "purple",
      defaultButtonText: CONTINUE_TEXT,
    },
    transparentResting: {
      backgroundColor: "transparent",
      buttonVariant: "primary",
      buttonColor: "purple",
      defaultButtonText: CONTINUE_TEXT,
    },
    skip: {
      backgroundColor: theme.colors.primary.white.hex,
      buttonVariant: skipClicked ? "primary" : "secondary",
      buttonColor: skipClicked ? "purple" : "purple",
      defaultButtonText: skipClicked ? ARE_YOU_SURE_TEXT : SKIP_TEXT,
    },
    positive: {
      backgroundColor: theme.colors.ui.alert_green_02.hex,
      avatarColor: theme.colors.ui.alert_green_01.hex,
      buttonVariant: "primary",
      buttonColor: "green",
      defaultButtonText: CONTINUE_TEXT,
    },
    nudge: {
      backgroundColor: theme.colors.tertiary.yellow_03.hex,
      avatarColor: theme.colors.tertiary.yellow_01.hex,
      buttonVariant: "secondary",
      buttonColor: "purple",
      defaultButtonText: SKIP_TEXT,
    },
    timeout: {
      backgroundColor: theme.colors.ui.alert_orange_03.hex,
      avatarColor: theme.colors.ui.alert_orange_01.hex,
      buttonVariant: "primary",
      buttonColor: "orange",
      defaultButtonText: CONTINUE_TEXT,
    },
    negative: {
      backgroundColor: theme.colors.ui.alert_red_02.hex,
      avatarColor: theme.colors.ui.alert_red_01.hex,
      buttonVariant: "primary",
      buttonColor: "red",
      defaultButtonText: CONTINUE_TEXT,
    },
  };

  const handleButtonClick = () => {
    if (state === "skip" && !skipClicked) {
      setSkipClicked(true);
    } else {
      setSkipClicked(false);

      if (onButtonClick) {
        onButtonClick();
      }
    }
  };

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === keyToPress) {
        if (onKeyDown) {
          onKeyDown();
        }
      }
    };

    if (onKeyDown && keyToPress) {
      window.addEventListener("keydown", handleKeyDown);
    }

    return () => {
      if (onKeyDown && keyToPress) {
        window.removeEventListener("keydown", handleKeyDown);
      }
    };
  }, [onKeyDown, keyToPress]);

  useEffect(() => {
    if (state !== "skip") {
      setSkipClicked(false);
    }
  }, [state]);

  return (
    <>
      {(state === "positive" ||
        state === "negative" ||
        state === "timeout") && (
        <Box
          pos="fixed"
          top={0}
          left={0}
          width="100%"
          height="100%"
          bgColor="rgba(255,255,255,0.4)"
          zIndex={100}
        />
      )}
      <Box
        data-testid="feedback-panel"
        id={state}
        py="12px"
        px="12px"
        backgroundColor={states[state].backgroundColor}
        pos="relative"
        zIndex={200}
        height={112}
        display="flex"
        alignItems="center"
        opacity={state === "transparentResting" ? 0.5 : 1}
      >
        <Container
          display="flex"
          alignItems="center"
          justifyContent="space-between"
          maxW={theme.max_page_width_v2}
          gap={theme.spacing.lg.px}
        >
          <Box display="flex" alignItems="center" gap={theme.spacing.sm.px}>
            {audioInstruction && (
              <>
                <InstructorIcon bgColor={states[state].avatarColor} />
                <Panel flex style={{ flex: 1, padding: theme.spacing.sm.px }}>
                  <AudioButton
                    isPlaying={isAudioPlaying}
                    size="ms"
                    alignCenter
                    label={audioInstruction}
                    onClick={onAudioButtonClick}
                  />
                  {audioFile && (
                    <ReactHowler
                      src={audioFile}
                      playing={isPlaying}
                      onEnd={() => setIsPlaying(false)}
                    />
                  )}
                </Panel>
              </>
            )}
          </Box>
          <Box display="flex" alignItems="center" gap={theme.spacing.sm.px}>
            <Button
              size="md"
              variant={states[state].buttonVariant}
              color={states[state].buttonColor}
              disabled={state === "resting" || state === "transparentResting"}
              onClick={handleButtonClick}
              onKeyDown={onKeyDown}
              keyToPress={keyToPress}
            >
              {skipClicked
                ? ARE_YOU_SURE_TEXT
                : buttonText || states[state].defaultButtonText}
            </Button>
            <Box minW="100px">
              {onKeyDown && keyToPress && (
                <Box
                  display="flex"
                  alignItems="center"
                  gap={theme.spacing.xxs.px}
                >
                  <Text
                    element="span"
                    variant="xs"
                    color={
                      mode === "light"
                        ? theme.colors.ui.grey_01.hex
                        : theme.colors.primary.white.hex
                    }
                  >
                    Press
                  </Text>
                  <KeyboardNudge>{keyToPress}</KeyboardNudge>
                </Box>
              )}
            </Box>
          </Box>
        </Container>
      </Box>
    </>
  );
};
