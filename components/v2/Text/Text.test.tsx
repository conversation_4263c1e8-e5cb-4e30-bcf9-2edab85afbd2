import "@testing-library/jest-dom/extend-expect";

import { render, screen } from "@testing-library/react";

import { theme } from "@/styles/theme";

import { Text } from "./Text";

describe("<Text>", () => {
  it("renders component", () => {
    render(<Text>Test Text</Text>);
    expect(screen.getByText("Test Text")).toBeInTheDocument();
  });

  it("renders with a default variant and element", () => {
    render(<Text>Test Text</Text>);
    const textElement = screen.getByText("Test Text");
    expect(textElement).toHaveStyle(`
      font-size: ${theme.v2Text.paragraph.md.fontSize};
      font-weight: ${theme.v2Text.paragraph.md.fontWeight};
      line-height: ${theme.v2Text.paragraph.md.lineHeight};
    `);
  });

  it("renders with given variant", () => {
    render(<Text variant="xl">Test Text</Text>);
    const textElement = screen.getByText("Test Text");
    expect(textElement).toHaveStyle(`
      font-size: ${theme.v2Text.paragraph.xl.fontSize};
      font-weight: ${theme.v2Text.paragraph.xl.fontWeight};
      line-height: ${theme.v2Text.paragraph.xl.lineHeight};
    `);
  });

  it("renders with given element", () => {
    render(<Text element="h1">Test Text</Text>);
    const textElement = screen.getByText("Test Text");
    expect(textElement.tagName).toBe("H1");
  });

  it("renders with given color", () => {
    render(<Text color="#123456">Test Text</Text>);
    const textElement = screen.getByText("Test Text");
    expect(textElement).toHaveStyle(`
      color: #123456;
    `);
  });

  it("renders with custom props", () => {
    render(<Text id="custom-id">Test Text</Text>);
    const textElement = screen.getByText("Test Text");
    expect(textElement).toHaveAttribute("id", "custom-id");
  });
});
