import {
  Box,
  Heading as <PERSON><PERSON>Heading,
  Text as ChakraText,
  TextProps as ChakraTextProps,
} from "@chakra-ui/react";
import { Children, HTMLAttributes, ReactElement, ReactNode } from "react";

import { theme } from "@/styles/theme";

export type TextVariant =
  | "xxs"
  | "4xs"
  | "xs"
  | "sm"
  | "md"
  | "mdsm"
  | "mdmd"
  | "lg"
  | "xl"
  | "2xl"
  | "3xl"
  | "4xl"
  | "5xl"
  | "6xl";

export type TextElement =
  | "h1"
  | "h2"
  | "h3"
  | "h4"
  | "h5"
  | "h6"
  | "p"
  | "span";

export interface ITextProps extends ChakraTextProps {
  variant?: TextVariant;
  element?: TextElement;
  color?: string;
  unbold?: boolean;
  children: ReactNode;
}

const parseString = (str: string): (string | ReactElement)[] => {
  const output: (string | ReactElement)[] = [];
  let lastIndex = 0;
  const regex = /{{(.*?)}}/g;
  let match;
  while ((match = regex.exec(str)) !== null) {
    const [matched, value] = match;
    if (lastIndex !== match.index) {
      output.push(str.slice(lastIndex, match.index));
    }
    output.push(
      <Box as="span" color={theme.colors.primary.purple.hex} key={match.index}>
        {value}
      </Box>
    );
    lastIndex = regex.lastIndex;
  }
  if (lastIndex < str.length) {
    output.push(str.slice(lastIndex));
  }
  return output;
};

export const Text = ({
  variant = "md",
  element = "p",
  children,
  color = theme.colors.primary.black.hex,
  unbold = false,
  ...rest
}: ITextProps) => {
  const Tag = ["h1", "h2", "h3", "h4", "h5", "h6"].includes(element)
    ? ChakraHeading
    : ChakraText;

  const tagProps =
    Tag === ChakraHeading
      ? {
          as: element,
          color,
          fontSize: theme.v2Text.headings[variant].fontSize,
          fontWeight: theme.v2Text.headings[variant].fontWeight,
          lineHeight: theme.v2Text.headings[variant].lineHeight,
        }
      : {
          as: element,
          color,
          fontSize: theme.v2Text.paragraph[variant].fontSize,
          fontWeight: theme.v2Text.paragraph[variant].fontWeight,
          lineHeight: theme.v2Text.paragraph[variant].lineHeight,
        };

  const processedChildren = Children.map(children, (child) => {
    if (typeof child === "string") {
      return <>{parseString(child)}</>;
    }
    return child;
  });

  return (
    <Tag
      data-testid="text"
      {...tagProps}
      {...rest}
      {...(unbold && { fontWeight: 400 })}
    >
      {processedChildren}
    </Tag>
  );
};
