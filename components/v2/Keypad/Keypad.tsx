import { Button } from "@chakra-ui/react";
import { useEffect, useRef, useState } from "react";

import { theme } from "@/styles/theme";

export type ButtonState =
  | "default"
  | "selected"
  | "correct"
  | "incorrect"
  | "disabled";

interface IStates {
  [key: string]: {
    backgroundColor: string;
    borderColor: string;
    nudgeColor?: string;
    opacity: number;
  };
}

export interface IKeypadProps {
  state?: ButtonState;
  label: string;
  disabled?: boolean;
  keyToPress: string;
  onButtonClick: () => void;
}

export const Keypad = ({
  state = "default",
  label,
  disabled = false,
  keyToPress,
  onButtonClick,
  ...rest
}: IKeypadProps) => {
  const states: IStates = {
    default: {
      backgroundColor: "linear-gradient(180deg, #C8D8E5 0%, #7191AF 25.52%)",
      borderColor: "rgba(0, 0, 0, 0.10)",
      opacity: 1,
    },
    selected: {
      backgroundColor: theme.colors.secondary.purple_02.hex,
      borderColor: "rgba(0, 0, 0, 0.10)",
      nudgeColor: theme.colors.secondary.purple_02.hex,
      opacity: 1,
    },
    correct: {
      backgroundColor: theme.colors.ui.alert_green_02.hex,
      borderColor: theme.colors.ui.alert_green_01.hex,
      nudgeColor: theme.colors.ui.alert_green_01.hex,
      opacity: 1,
    },
    incorrect: {
      backgroundColor: theme.colors.ui.alert_red_02.hex,
      borderColor: theme.colors.ui.alert_red_01.hex,
      nudgeColor: theme.colors.ui.alert_red_01.hex,
      opacity: 1,
    },
    disabled: {
      backgroundColor: "linear-gradient(180deg, #C8D8E5 0%, #7191AF 25.52%)",
      borderColor: "rgba(0, 0, 0, 0.10)",
      opacity: 0.5,
    },
  };

  const handleKeyDown = (event) => {
    if (disabled === true || event.key !== keyToPress.toString()) return;
    onButtonClick();
  };

  useEffect(() => {
    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [disabled]);

  return (
    <Button
      data-testid="keypad"
      w={74}
      h={74}
      bg={states[state].backgroundColor}
      boxShadow={`inset 0px 0px 0px 3px ${states[state].borderColor}`}
      color={"white"}
      borderRadius="20px"
      isDisabled={disabled}
      onClick={onButtonClick}
      display="flex"
      alignItems="center"
      justifyContent="center"
      py={theme.spacing.md.px}
      px={theme.spacing.sm.px}
      gap={theme.spacing.xs.px}
      fontSize={theme.v2Text.headings.md.fontSize}
      transition="all 0.2s ease-in"
      opacity={states[state].opacity}
      _hover={{
        bg: `${states[state].backgroundColor}`,
      }}
      {...rest}
    >
      {label}
    </Button>
  );
};
