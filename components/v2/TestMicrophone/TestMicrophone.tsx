import { Box, HStack } from "@chakra-ui/react";
import { ArrowPathIcon, MicrophoneIcon } from "@heroicons/react/24/solid";
import { motion } from "framer-motion";
import dynamic from "next/dynamic";
import { useEffect, useState } from "react";
import { useAudioRecorder } from "react-audio-voice-recorder";

import { theme } from "@/styles/theme";

import { Text } from "../Text/Text";
import { ButtonWrapper } from "./Components/ButtonWrapper";
import { MicTestWrapper } from "./Components/MicTestWrapper";
import RecordingAnimation from "./Lottie/LottieRecordingAnimation.json";

const Lottie = dynamic(() => import("lottie-react"), { ssr: false });

export enum RecordingState {
  Record,
  Loading,
  Recording,
  Checking,
  Saved,
}

export interface ITestMicrophoneProps {
  isSaving?: boolean;
  onRecordingStarted: () => void;
  onRecordingComplete: (blob: Blob) => void;
}

export const TestMicrophone = ({
  isSaving,
  onRecordingStarted,
  onRecordingComplete,
}: ITestMicrophoneProps) => {
  const { startRecording, stopRecording, recordingBlob } = useAudioRecorder(
    {
      noiseSuppression: true,
      echoCancellation: true,
    },
    undefined,
    { audioBitsPerSecond: 16000 }
  );
  const [hasFiredCompletion, setHasFiredCompletion] = useState(false);
  const [hasStoppedRecording, setHasStoppedRecording] = useState(false);
  const [timeoutId, setTimeoutId] = useState<NodeJS.Timeout | null>(null);
  const [recordingState, setRecordingState] = useState<RecordingState>(
    RecordingState.Record
  );

  useEffect(() => {
    if (!recordingBlob || hasFiredCompletion) return;
    onRecordingComplete(recordingBlob);
    setHasFiredCompletion(true);
  }, [recordingBlob, onRecordingComplete, hasFiredCompletion]);

  const handleRecordClick = () => {
    startRecording();
    setRecordingState(RecordingState.Loading);

    setTimeout(() => {
      setRecordingState(RecordingState.Recording);
      onRecordingStarted();
    }, 1200);

    const recordingTimeoutId = setTimeout(() => {
      handleStopRecording();
    }, 30000);

    setTimeoutId(recordingTimeoutId);
  };

  const handleStopRecording = () => {
    setRecordingState(RecordingState.Checking);
    stopRecording();
    setHasStoppedRecording(true);
    if (timeoutId) clearTimeout(timeoutId);
    setTimeoutId(null);
  };

  const renderState = () => {
    switch (recordingState) {
      case RecordingState.Record:
        return (
          <>
            <ButtonWrapper
              data-testid="record-button"
              onClick={handleRecordClick}
            >
              <MicrophoneIcon
                width="48px"
                height="48px"
                color={theme.colors.primary.purple.hex}
              />
            </ButtonWrapper>
            <Text
              element="h3"
              variant="md"
              color={theme.colors.primary.purple.hex}
              style={{ textDecoration: "underline", cursor: "pointer" }}
              onClick={handleRecordClick}
            >
              Test microphone
            </Text>
          </>
        );

      case RecordingState.Loading:
        return (
          <>
            <ButtonWrapper data-testid="loading" hoverDisabled>
              <motion.div
                animate={{ rotate: [0, 360] }}
                transition={{
                  duration: 1,
                  repeat: Infinity,
                  repeatType: "loop",
                  ease: "linear",
                }}
              >
                <ArrowPathIcon
                  width="52px"
                  height="52px"
                  color={theme.colors.primary.purple.hex}
                />
              </motion.div>
            </ButtonWrapper>
            <Text
              element="h3"
              variant="md"
              color={theme.colors.primary.purple.hex}
              style={{ height: "24px" }}
            >
              One sec...
            </Text>
          </>
        );

      case RecordingState.Recording:
        return (
          <>
            <ButtonWrapper
              data-testid="stop-recording-button"
              onClick={handleStopRecording}
            >
              <Lottie
                animationData={RecordingAnimation}
                loop={true}
                autoplay={true}
                style={{
                  height: "80px",
                  width: "80px",
                  display: "block",
                }}
              />
            </ButtonWrapper>
            <HStack
              height="24px"
              alignItems="center"
              onClick={handleStopRecording}
              style={{ cursor: "pointer" }}
            >
              <Box
                width="14px"
                height="14px"
                borderRadius="2px"
                backgroundColor={theme.colors.primary.purple.hex}
              />

              <Text
                element="h3"
                variant="md"
                color={theme.colors.primary.purple.hex}
                style={{ textDecoration: "underline" }}
              >
                Stop recording
              </Text>
            </HStack>
          </>
        );

      case RecordingState.Checking:
        return (
          <>
            <ButtonWrapper data-testid="saving" hoverDisabled>
              <motion.div
                animate={{ rotate: [0, 360] }}
                transition={{
                  duration: 1,
                  repeat: Infinity,
                  repeatType: "loop",
                  ease: "linear",
                }}
              >
                <ArrowPathIcon
                  width="62px"
                  height="62px"
                  color={theme.colors.primary.purple.hex}
                />
              </motion.div>
            </ButtonWrapper>
            <Text
              element="h3"
              variant="md"
              color={theme.colors.primary.purple.hex}
              style={{ height: "24px" }}
            >
              Checking!
            </Text>
          </>
        );
    }
  };

  return (
    <HStack gap={theme.spacing.sm.px} alignItems="center">
      <MicTestWrapper>{renderState()}</MicTestWrapper>
    </HStack>
  );
};
