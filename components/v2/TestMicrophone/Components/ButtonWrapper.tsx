import { Button } from "@chakra-ui/react";
import { ReactNode } from "react";

import { theme } from "@/styles/theme";

export interface IButtonWrapperProps {
  disabled?: boolean;
  children: ReactNode;
  hoverDisabled?: boolean;
  onClick?: () => void;
  success?: boolean;
  error?: boolean;
}

export const ButtonWrapper = ({
  children,
  disabled = false,
  hoverDisabled = false,
  onClick,
  success,
  error,
}: IButtonWrapperProps) => {
  return (
    <Button
      backgroundColor={
        error
          ? theme.colors.ui.alert_red_01.hex
          : success
            ? theme.colors.ui.alert_green_01.hex
            : theme.colors.secondary.purple_03.rgb
      }
      disabled={disabled}
      width="96px"
      height="96px"
      p={0}
      minW={0}
      onClick={onClick}
      borderRadius={theme.border.radius.xxl.px}
      transition="all 0.1s ease-in"
      _hover={
        hoverDisabled
          ? {}
          : { backgroundColor: theme.colors.secondary.purple_04.rgb }
      }
      _active={hoverDisabled ? {} : { opacity: 0.9, transform: "scale(0.9)" }}
      _disabled={{
        opacity: 1,
        backgroundColor: theme.colors.ui.grey_04.hex,
        cursor: "not-allowed",
      }}
    >
      {children}
    </Button>
  );
};
