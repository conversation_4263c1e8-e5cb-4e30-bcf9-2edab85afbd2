import { Box } from "@chakra-ui/react";
import { ReactNode } from "react";

import { theme } from "@/styles/theme";

export interface IMicTestWrapperProps {
  children: ReactNode;
  success?: boolean;
  error?: boolean;
}

export const MicTestWrapper = ({
  children,
  success,
  error,
}: IMicTestWrapperProps) => {
  return (
    <Box
      display={"flex"}
      backgroundColor={
        error
          ? theme.colors.ui.alert_red_02.hex
          : success
            ? theme.colors.ui.alert_green_02.hex
            : theme.colors.secondary.purple_05.rgb
      }
      borderRadius={theme.border.radius.xxl.px}
      alignItems={"center"}
      gap={theme.spacing.md.px}
      width={"500px"}
    >
      {children}
    </Box>
  );
};
