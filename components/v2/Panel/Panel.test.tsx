import "@testing-library/jest-dom/extend-expect";

import { render, screen } from "@testing-library/react";

import { theme } from "@/styles/theme";

import { Panel } from "./Panel";

describe("<Panel />", () => {
  it("renders without crashing", () => {
    render(<Panel flex={true}>Test Content</Panel>);
    const panelElement = screen.getByTestId("panel");
    expect(panelElement).toBeInTheDocument();
  });

  it("renders its children", () => {
    render(<Panel flex={true}>Test Content</Panel>);
    const panelElement = screen.getByTestId("panel");
    expect(panelElement).toHaveTextContent("Test Content");
  });
});
