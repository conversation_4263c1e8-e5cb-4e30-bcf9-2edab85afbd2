"use client";
import { Box } from "@chakra-ui/react";
import React, { forwardRef, HTMLAttributes, ReactNode } from "react";

import { marchingAntsAnimation } from "@/styles/animations";
import { theme } from "@/styles/theme";

export interface IPanelProps extends HTMLAttributes<HTMLElement> {
  backgroundColor?: string;
  borderRadius?: string;
  preset?: "dropzone" | "panel" | "error" | "success" | "outline" | "warning";
  px?: string;
  py?: string;
  flex?: boolean;
  alignCenter?: boolean;
  children: ReactNode;
  isAnimating?: boolean;
}

const Panel = forwardRef<HTMLDivElement, IPanelProps>(
  (
    {
      backgroundColor = theme.colors.primary.white.hex,
      borderRadius = theme.border.radius.ml.px,
      preset,
      px = theme.spacing.xs.px,
      py = theme.spacing.xs.px,
      flex = true,
      alignCenter = true,
      children,
      isAnimating,
      ...rest
    },
    ref
  ) => {
    let presetStyles = {};
    let animationStyles = {};

    if (preset === "dropzone") {
      presetStyles = {
        display: "flex",
        flexDir: "column",
        justifyContent: "center",
        backgroundImage: `linear-gradient(${theme.colors.secondary.purple_05.hex}, ${theme.colors.secondary.purple_05.hex}), repeating-linear-gradient(-45deg, ${theme.colors.primary.purple.hex} 0, ${theme.colors.primary.purple.hex} 12.5%, transparent 0, transparent 25%, ${theme.colors.primary.purple.hex} 0, ${theme.colors.primary.purple.hex} 37.5%, transparent 0,transparent 50%)`,
        backgroundSize: "2em 2em",
        backgroundClip: "padding-box, border-box",
        border: "2px solid rgba(0,0,0,0)",
        p: "1rem",
        w: "100%",
        minH: "430px",
      };
    }

    if (preset === "panel") {
      presetStyles = {
        display: "flex",
        flexDir: "column",
        justifyContent: "center",
        minH: "430px",
        backgroundColor: theme.colors.secondary.purple_05.hex,
      };
    }

    if (preset === "warning") {
      presetStyles = {
        display: "flex",
        flexDir: "column",
        justifyContent: "center",
        minH: "430px",
        backgroundColor: theme.colors.tertiary.yellow_03.hex,
      };
    }

    if (preset === "error") {
      presetStyles = {
        display: "flex",
        flexDir: "column",
        justifyContent: "center",
        minH: "430px",
        backgroundColor: theme.colors.ui.alert_red_02.hex,
      };
    }

    if (preset === "success") {
      presetStyles = {
        display: "flex",
        flexDir: "column",
        justifyContent: "center",
        minH: "430px",
        backgroundColor: theme.colors.ui.alert_green_02.hex,
      };
    }

    if (preset === "outline") {
      presetStyles = {
        display: "flex",
        flexDir: "column",
        alignItems: "flex-start",
        w: "100%",
        border: "1px solid rgba(0, 0 ,0 ,0.1)",
        borderRadius: theme.border.radius.lg.px,
        px: theme.spacing.lg.px,
        py: theme.spacing.lg.px,
      };
    }

    if (isAnimating) {
      animationStyles = {
        animation: `${marchingAntsAnimation} 30s linear infinite`,
        backgroundImage: `linear-gradient(${theme.colors.secondary.purple_04.hex}, ${theme.colors.secondary.purple_04.hex}), repeating-linear-gradient(-45deg, ${theme.colors.primary.purple.hex} 0, ${theme.colors.primary.purple.hex} 12.5%, transparent 0, transparent 25%, ${theme.colors.primary.purple.hex} 0, ${theme.colors.primary.purple.hex} 37.5%, transparent 0,transparent 50%)`,
      };
    }

    return (
      <Box
        ref={ref}
        data-testid="panel"
        backgroundColor={backgroundColor}
        px={px}
        py={py}
        borderRadius={borderRadius}
        display={flex ? "inline-flex" : "inline-block"}
        alignItems={alignCenter === true ? "center" : ""}
        sx={{
          ...animationStyles,
        }}
        {...presetStyles}
        {...rest}
      >
        {children}
      </Box>
    );
  }
);

Panel.displayName = "Panel";

export { Panel };
