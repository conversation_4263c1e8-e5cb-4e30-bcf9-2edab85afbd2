import { Box, Button, HStack } from "@chakra-ui/react";
import dynamic from "next/dynamic";
import React, { forwardRef } from "react";

import { Text } from "@/components/v2/Text/Text";
import { theme } from "@/styles/theme";

import AudioAnimation from "./Lottie/LottieAudioButton.json";

const Lottie = dynamic(() => import("lottie-react"), { ssr: false });

export interface IAudioButtonProps {
  size: "sm" | "md" | "ms" | "lg" | "xl";
  alignCenter?: boolean;
  disabled?: boolean;
  isPlaying?: boolean;
  onClick?: () => void;
  onKeyDown?: () => void;
  keyToPress?: string;
  label?: string;
}

const AudioButton = forwardRef<HTMLButtonElement, IAudioButtonProps>(
  (
    {
      size,
      alignCenter = false,
      disabled = false,
      isPlaying = false,
      onClick,
      onKeyDown,
      keyToPress,
      label,
      ...rest
    },
    ref
  ) => {
    const dimensions = {
      sm: 24,
      ms: 32,
      md: 40,
      lg: 128,
      xl: 180,
    };

    const borderRadius = {
      sm: theme.border.radius.sm.px,
      ms: theme.border.radius.md.px,
      md: theme.border.radius.md.px,
      lg: theme.border.radius.xl.px,
      xl: theme.border.radius.xxl.px,
    };

    const handleClick = (): void => {
      if (onClick) {
        onClick();
      }
    };

    const handleKeyPress = (
      event: React.KeyboardEvent<HTMLButtonElement>
    ): void => {
      if (onKeyDown && keyToPress && event.key === keyToPress) {
        onKeyDown();
      }
    };

    return (
      <HStack
        alignItems={alignCenter ? "center" : "flex-start"}
        spacing={
          size === "lg"
            ? theme.spacing.ml.px
            : size === "xl"
              ? theme.spacing.ml.px
              : size === "md"
                ? theme.spacing.sm.px
                : theme.spacing.xs.px
        }
      >
        <Box>
          <Button
            ref={ref}
            data-testid="audio-button"
            aria-label='Audio button"'
            onClick={handleClick}
            onKeyDown={handleKeyPress}
            tabIndex={0}
            isDisabled={disabled}
            backgroundColor={theme.colors.secondary.purple_03.rgb}
            width={`${dimensions[size]}px`}
            height={`${dimensions[size]}px`}
            p={0}
            minW={0}
            borderRadius={borderRadius[size]}
            transition="all 0.1s ease-in"
            _hover={{ backgroundColor: theme.colors.secondary.purple_04.rgb }}
            _active={{ opacity: 0.9, transform: "scale(0.9)" }}
            _disabled={{
              opacity: 1,
              backgroundColor: theme.colors.ui.grey_04.hex,
              cursor: "not-allowed",
            }}
            css={{
              "svg path": {
                fill: disabled
                  ? theme.colors.ui.grey_03.hex
                  : theme.colors.primary.purple.hex,
                stroke: disabled
                  ? theme.colors.ui.grey_03.hex
                  : theme.colors.primary.purple.hex,
              },
            }}
            {...rest}
          >
            <Lottie
              key={isPlaying ? "playing" : "stopped"}
              animationData={AudioAnimation}
              loop={true}
              autoplay={isPlaying}
              style={{
                height: `${dimensions[size]}px`,
                width: `${dimensions[size]}px`,
                display: "block",
              }}
            />
          </Button>
        </Box>
        {label && (
          <Box>
            <Text
              element="h3"
              variant={
                size === "lg"
                  ? "xl"
                  : size === "md"
                    ? "md"
                    : size === "ms"
                      ? "sm"
                      : "xs"
              }
            >
              {label}
            </Text>
          </Box>
        )}
      </HStack>
    );
  }
);

AudioButton.displayName = "AudioButton";

export { AudioButton };
