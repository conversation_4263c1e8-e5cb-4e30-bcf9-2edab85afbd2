import "@testing-library/jest-dom/extend-expect";

import { render, screen } from "@testing-library/react";

import { KeyboardNudge } from "./KeyboardNudge";

describe("<KeyboardNudge />", () => {
  it("should render without crashing", () => {
    render(<KeyboardNudge>Enter</KeyboardNudge>);
    const kbdElement = screen.getByTestId("keyboard-nudge");
    expect(kbdElement).toBeInTheDocument();
  });

  it("should render its children", () => {
    render(<KeyboardNudge>Enter</KeyboardNudge>);
    const kbdElement = screen.getByTestId("keyboard-nudge");
    expect(kbdElement).toHaveTextContent("Enter");
  });

  it("should have full opacity when active is true", () => {
    render(<KeyboardNudge active={true}>Enter</KeyboardNudge>);
    const kbdElement = screen.getByTestId("keyboard-nudge");
    expect(kbdElement).toHaveStyle(`opacity: 1`);
  });

  it("should have 0.5 opacity when active is false", () => {
    render(<KeyboardNudge active={false}>Enter</KeyboardNudge>);
    const kbdElement = screen.getByTestId("keyboard-nudge");
    expect(kbdElement).toHaveStyle(`opacity: 0.5`);
  });
});
