import { Kbd } from "@chakra-ui/react";
import { HTMLAttributes, ReactNode } from "react";

import { theme } from "@/styles/theme";

export interface IKeyboardNudgeProps extends HTMLAttributes<HTMLElement> {
  active?: boolean;
  children: ReactNode;
  backgroundColor?: string;
}

export const KeyboardNudge = ({
  active = true,
  children,
  backgroundColor = theme.gradients.blue_grey,
  ...rest
}: IKeyboardNudgeProps) => {
  return (
    <Kbd
      data-testid="keyboard-nudge"
      border="none"
      borderRadius={theme.border.radius.sm.px}
      fontSize={theme.v2Text.headings.xs.fontSize}
      fontWeight={theme.v2Text.headings.xs.fontWeight}
      lineHeight={theme.v2Text.headings.xs.lineHeight}
      py={theme.spacing.xxxs.px}
      px={theme.spacing.xxs.px}
      pb={0}
      fontFamily="inherit"
      background={backgroundColor}
      color={theme.colors.primary.white.hex}
      boxShadow="inset 0 0 0 1px rgba(0, 0, 0, 0.10)"
      opacity={active ? 1 : 0.5}
      {...rest}
    >
      {children}
    </Kbd>
  );
};
