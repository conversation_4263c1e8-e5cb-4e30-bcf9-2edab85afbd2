import {
  Modal as <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@chakra-ui/react";
import styled from "@emotion/styled";
import { ReactNode } from "react";

import { theme } from "@/styles/theme";

import { Button, ButtonColors, ButtonVariant } from "../Button/Button";
import { CloseButton } from "../CloseButton/CloseButton";

export interface IModalProps {
  title?: string;
  isOpen: boolean;
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
  description?: string;
  primaryCtaText?: string;
  primaryCtaVariant?: ButtonVariant;
  primaryCtaColor?: ButtonColors;
  onPrimaryCtaClick?: () => void;
  secondaryCtaText?: string;
  secondaryCtaVariant?: ButtonVariant;
  secondaryCtaColor?: ButtonColors;
  onSecondaryCtaClick?: () => void;
  onModalClose?: () => void;
  overlayBackground?: string;
  showCloseBtn?: boolean;
  size?: string;
  children?: ReactNode;
}
const StyledDescription = styled.div`
  p {
    margin-bottom: 10px;
  }
  ul {
    margin: 0 0 20px 15px;
  }
  a {
    text-decoration: underline;
  }
  @media (max-width: ${theme.breakpoints.portable}) {
  }
`;

export const Modal = ({
  isOpen = false,
  setIsOpen,
  title,
  description,
  primaryCtaText,
  primaryCtaVariant = "primary",
  primaryCtaColor = "purple",
  onPrimaryCtaClick,
  secondaryCtaText,
  secondaryCtaVariant = "secondary",
  secondaryCtaColor = "purple",
  onSecondaryCtaClick,
  onModalClose,
  overlayBackground,
  showCloseBtn = false,
  size,
  children,
  ...rest
}: IModalProps) => {
  return (
    <ChakraModal
      isOpen={isOpen}
      isCentered
      size={size}
      {...rest}
      onClose={onModalClose ? onModalClose : () => {}}
    >
      <ModalOverlay backgroundColor={overlayBackground && overlayBackground} />
      <ModalContent
        boxShadow="none"
        p="30px"
        gap="32px"
        color={theme.colors.primary.black.hex}
      >
        {title && (
          <ModalHeader fontSize={theme.v2Text.headings.md.fontSize} p={0}>
            {title}
          </ModalHeader>
        )}
        {description && (
          <ModalBody fontSize={theme.v2Text.paragraph.lg.fontSize} p={0}>
            <StyledDescription
              dangerouslySetInnerHTML={{ __html: description }}
            />
            {children}
          </ModalBody>
        )}
        {children && (
          <ModalBody fontSize={theme.v2Text.paragraph.lg.fontSize} p={0}>
            {children}
          </ModalBody>
        )}
        {(primaryCtaText || secondaryCtaText) && (
          <ModalFooter justifyContent="initial" gap={theme.spacing.xs.px}>
            {primaryCtaText && (
              <Button
                size="md"
                variant={primaryCtaVariant}
                color={primaryCtaColor}
                onClick={onPrimaryCtaClick}
                style={{ flexGrow: 1 }}
              >
                {primaryCtaText}
              </Button>
            )}
            {secondaryCtaText && (
              <Button
                size="md"
                variant={secondaryCtaVariant}
                color={secondaryCtaColor}
                onClick={onSecondaryCtaClick}
                style={{ flexGrow: 1 }}
              >
                {secondaryCtaText}
              </Button>
            )}
          </ModalFooter>
        )}
        {showCloseBtn === true && (
          <CloseButton
            size="lg"
            onClick={() => setIsOpen(false)}
            style={{
              position: "absolute",
              right: theme.spacing.xs.px,
              top: theme.spacing.xs.px,
            }}
          />
        )}
      </ModalContent>
    </ChakraModal>
  );
};
