"use client";

import { Box } from "@chakra-ui/react";
import { HandThumbDownIcon, HandThumbUpIcon } from "@heroicons/react/24/solid";
import { useEffect, useState } from "react";

import { theme } from "@/styles/theme";

import { Text } from "../v2";

const ICON_SIZE = 17;

type Feedback = "positive" | "negative";

export interface IFeedbackThumbsProps {
  feedback?: Feedback;
  onPositiveClick?: () => void;
  onNegativeClick?: () => void;
  disabled?: boolean;
  hideLabel?: boolean;
}

export const FeedbackThumbs = ({
  feedback,
  onPositiveClick,
  onNegativeClick,
  disabled = false,
  hideLabel = false,
}: IFeedbackThumbsProps) => {
  const [showLabel, setShowLabel] = useState(!feedback);

  useEffect(() => {
    if (feedback) {
      setShowLabel(false);
    }
  }, [feedback]);

  const positiveClickHandler = () => {
    if (disabled || !onPositiveClick) return;
    setShowLabel(false);
    onPositiveClick();
  };

  const negativeClickHandler = () => {
    if (disabled || !onNegativeClick) return;
    setShowLabel(false);
    onNegativeClick();
  };

  return (
    <Box display="flex" gap="8px" alignItems="center">
      {!hideLabel && (
        <Box
          mb="-2px"
          style={{
            opacity: showLabel ? 1 : 0,
            transition: "opacity 600ms",
          }}
        >
          <Text element="h6" variant="xs">
            Is this useful?
          </Text>
        </Box>
      )}

      <Box
        px="8px"
        py="4px"
        cursor="pointer"
        bgColor={
          feedback === "positive"
            ? theme.colors.ui.alert_green_02.hex
            : theme.colors.ui.grey_04.hex
        }
        borderRadius="100px"
        onClick={positiveClickHandler}
        _hover={{
          bgColor:
            feedback !== "positive" ? theme.colors.ui.grey_03.hex : undefined,
        }}
      >
        <HandThumbUpIcon
          width={ICON_SIZE}
          height={ICON_SIZE}
          color={
            feedback === "positive"
              ? theme.colors.ui.alert_green_01.hex
              : theme.colors.primary.black.hex
          }
        />
      </Box>
      <Box
        px="8px"
        py="4px"
        cursor="pointer"
        bgColor={
          feedback === "negative"
            ? theme.colors.ui.alert_red_02.hex
            : theme.colors.ui.grey_04.hex
        }
        borderRadius="100px"
        onClick={negativeClickHandler}
        _hover={{
          bgColor:
            feedback !== "negative" ? theme.colors.ui.grey_03.hex : undefined,
        }}
      >
        <HandThumbDownIcon
          width={ICON_SIZE}
          height={ICON_SIZE}
          color={
            feedback === "negative"
              ? theme.colors.ui.alert_red_01.hex
              : theme.colors.primary.black.hex
          }
        />
      </Box>
    </Box>
  );
};
