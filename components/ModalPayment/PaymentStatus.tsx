import { Box } from "@chakra-ui/react";
import { CheckCircleIcon, XCircleIcon } from "@heroicons/react/24/solid";

import { theme } from "@/styles/theme";

import { Button, Text } from "../v2";

interface IPaymentStatusProps {
  setIsModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
  setPaymentStatus: React.Dispatch<React.SetStateAction<string>>;
  state: "success" | "error";
  failureReason?: string;
}

export const PaymentStatus = ({
  setIsModalOpen,
  setPaymentStatus,
  state,
  failureReason,
}: IPaymentStatusProps) => {
  const iconSize = 127;
  const heading = state === "success" ? "Success!" : "Failed!";
  const text =
    state === "success"
      ? "Your payment has gone through. A receipt will be sent to your email. You can now close this and begin the screener."
      : "Looks like there’s an issue. Message <EMAIL> if you need any help.";
  const icon =
    state === "success" ? (
      <CheckCircleIcon
        width={iconSize}
        height={iconSize}
        color={theme.colors.ui.alert_green_01.hex}
      />
    ) : (
      <XCircleIcon
        width={iconSize}
        height={iconSize}
        color={theme.colors.ui.alert_red_01.hex}
      />
    );
  return (
    <>
      <Box display="flex" gap={34} py="80px">
        <Box pt="12px">{icon}</Box>
        <Box>
          <Text element="h2" variant="xl">
            {heading}
          </Text>
          {failureReason && (
            <Text element="p" variant="lg" mt="18px">
              {failureReason}
            </Text>
          )}
          <Text element="p" variant="lg" mt="18px">
            {text}
          </Text>
        </Box>
      </Box>
      <Box
        display="flex"
        justifyContent="center"
        mb="64px"
        gap={theme.spacing.lg.px}
      >
        {state === "success" && (
          <Button
            size="lg"
            variant="secondary"
            onClick={() => {
              setIsModalOpen(false);
            }}
          >
            Close
          </Button>
        )}
        {state === "error" && (
          <>
            <Button
              size="lg"
              variant="secondary"
              onClick={() => {
                setIsModalOpen(false);
              }}
            >
              Cancel
            </Button>
            <Button
              size="lg"
              variant="primary"
              onClick={() => {
                setPaymentStatus("payment");
              }}
            >
              Try again
            </Button>
          </>
        )}
      </Box>
    </>
  );
};
