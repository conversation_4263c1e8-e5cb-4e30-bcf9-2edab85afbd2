import { Box } from "@chakra-ui/react";
import { Elements } from "@stripe/react-stripe-js";
import { loadStripe } from "@stripe/stripe-js";
import { useUser } from "@supabase/auth-helpers-react";
import { useEffect, useState } from "react";

import { StripeForm } from "@/components/StripeForm/StripeForm";
import { STRIPE_ASSESSMENT_PURCHASE_PRICE } from "@/constants/constants";

import { Text } from "../v2";

interface IModalPaymentProps {
  setPaymentSuccess: React.Dispatch<React.SetStateAction<boolean>>;
  setIsModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
}

export const ModalPayment = ({
  setPaymentSuccess,
  setIsModalOpen,
}: IModalPaymentProps) => {
  const user = useUser();
  const [clientSecret, setClientSecret] = useState("");
  const [paymentIntent, setPaymentIntent] = useState(null);
  const stripePromise = loadStripe(
    process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY || ""
  );
  useEffect(() => {
    if (user && !paymentIntent) {
      fetch("api/stripe/create-intent", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          amount: STRIPE_ASSESSMENT_PURCHASE_PRICE,
          payment_intent_id: paymentIntent,
          supabase_user_id: user.id,
          supabase_email: user.email,
        }),
      })
        .then((res) => res.json())
        .then((data) => {
          setClientSecret(data.client_secret);
          setPaymentIntent(data.id);
        });
    }
  }, [user, paymentIntent]);

  if (!user) {
    return null;
  }

  const userEmail = user.email;

  const confirmParams = {
    return_url: `${process.env.NEXT_PUBLIC_DOMAIN}/dashboard`,
    receipt_email: userEmail,
    payment_method_data: {
      billing_details: {
        email: userEmail,
      },
    },
  };

  return (
    <Box>
      <Box minH="300px">
        {clientSecret && stripePromise ? (
          <Elements
            stripe={stripePromise}
            options={{
              clientSecret: clientSecret,
              appearance: {
                theme: "stripe",
              },
              loader: "never",
            }}
          >
            <StripeForm
              confirmParams={confirmParams}
              setPaymentSuccess={setPaymentSuccess}
              setIsModalOpen={setIsModalOpen}
            />
          </Elements>
        ) : (
          <></>
        )}
      </Box>
    </Box>
  );
};
