import { Image, VStack } from "@chakra-ui/react";

import { Button, Text } from "@/components";
import { IScaleRecord } from "@/data/types";
import { theme } from "@/styles/theme";

type ScaleCardProps = {
  isUnlocked?: boolean;
  isComplete: boolean;
  routerFn: { push: (args?: any) => any };
  scale: IScaleRecord;
  scaleURL: string;
  buttonLabel: string;
};
export function ScaleCard({
  isUnlocked = false,
  isComplete = false,
  routerFn,
  scale,
  scaleURL,
  buttonLabel,
}: ScaleCardProps) {
  const defaultStyles = {
    transform: "scale(1)",
    transition: "all 0.05s ease-out",
    padding: theme.spacing.sm.rem,
    boxShadow: theme.shadow.medium,
  };
  const hoverStyles = {
    border: `3px solid ${theme.colors.primary.purple.hex}`,
    transform: "scale(1.05)",
    transition: "all 0.1s ease-in",
    padding: theme.spacing.xs.rem,
    boxShadow: theme.shadow.huge,
  };
  const getStyles = () => {
    if (!isUnlocked) {
      return { display: "none" };
    }
    if (isComplete) {
      return { ...defaultStyles, opacity: 0.5, cursor: "unset" };
    }
    return {
      ":hover": hoverStyles,
      ":not(:hover)": defaultStyles,
    };
  };
  return (
    <VStack
      key={scale?.id}
      bgColor={theme.colors.ui.grey_04.hex}
      borderRadius={theme.border.radius.md.rem}
      cursor="pointer"
      css={getStyles()}
      onClick={() => (isComplete ? null : routerFn?.push(scaleURL))}
    >
      <>
        <Image
          alt={scale.scaleTitle as string}
          src={scale.scaleStart?.url}
          w={{ base: "100%", md: "240px" }}
          h={"240px"}
        />
        <Text.P_LG>{scale.scaleTitle}</Text.P_LG>
        <Button
          disabled={isComplete}
          mt={theme.spacing.md.rem}
          onClick={() => routerFn?.push(scaleURL)}
        >
          {isComplete ? "COMPLETE" : buttonLabel}
        </Button>
      </>
    </VStack>
  );
}
