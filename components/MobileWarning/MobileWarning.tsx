import { Box, Image } from "@chakra-ui/react";
import router from "next/router";
import { useState } from "react";
import ReactHowler from "react-howler";

import { Backdrop } from "@/components";
import { AudioButton, Button, Panel } from "@/components/v2";
import static_content from "@/data/static-content.json";
import { theme } from "@/styles/theme";

export function MobileWarning() {
  const [isAudioOn, setAudioOn] = useState(false);
  const audio =
    "https://www.datocms-assets.com/103689/1698743438-general-device-switch-prompt.mp3";
  const img = "/images/mobile-warning.svg";

  return (
    <>
      <Backdrop position="relative">
        <Box
          height="100%"
          display="flex"
          justifyContent="center"
          alignItems="center"
          flexDir="column"
          padding={theme.spacing.md.px}
        >
          <Box>
            <Box
              bgColor={theme.colors.primary.white.hex}
              borderRadius={theme.border.radius.xxl.px}
              maxWidth={theme.max_onboarding_width}
              overflow="hidden"
            >
              <Box
                padding={theme.spacing.sm.px}
                style={{ paddingBottom: "0px" }}
              >
                <Panel
                  backgroundColor={theme.colors.secondary.purple_05.hex}
                  flex={false}
                  px={theme.border.radius.lg.px}
                  style={{ cursor: "pointer" }}
                  py={theme.border.radius.lg.px}
                  onClick={() => {
                    setAudioOn(true);
                  }}
                >
                  <AudioButton
                    size="md"
                    isPlaying={isAudioOn}
                    label="You’ll need to switch to a desktop or laptop!"
                  />
                </Panel>
              </Box>
              <Box
                display="flex"
                justifyContent="center"
                mt={theme.spacing.md.px}
              >
                <Image
                  src={img}
                  w="153"
                  alt={static_content.onboarding.instructions}
                />
              </Box>
            </Box>
          </Box>{" "}
          <Box mt={theme.spacing.ml.px}>
            <Button width="full" onClick={() => router.push("/dashboard")}>
              Quit test
            </Button>
          </Box>
        </Box>
      </Backdrop>
      {audio && (
        <ReactHowler
          src={audio}
          playing={isAudioOn}
          onEnd={() => {
            setAudioOn(false);
          }}
        />
      )}
    </>
  );
}
