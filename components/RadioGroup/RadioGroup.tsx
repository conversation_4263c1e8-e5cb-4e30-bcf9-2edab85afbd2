import {
  RadioGroup as ChakraRadioGroup,
  RadioGroupProps as ChakraRadioGroupProps,
} from "@chakra-ui/react";
import React, { forwardRef } from "react";

type RadioGroupProps = ChakraRadioGroupProps;

export const RadioGroup = forwardRef<HTMLDivElement, RadioGroupProps>(
  ({ ...props }, ref) => {
    return <ChakraRadioGroup ref={ref} {...props} />;
  }
);

RadioGroup.displayName = "RadioGroup";
