"use client";

import {
  FormLabel as ChakraFormLabel,
  FormLabelProps as ChakraFormLabelProps,
} from "@chakra-ui/react";

import { theme } from "@/styles/theme";

type FormLabelProps = ChakraFormLabelProps;

export const FormLabel = ({ ...props }: FormLabelProps) => {
  return (
    <ChakraFormLabel
      alignSelf="flex-start"
      m={0}
      mt={theme.spacing.sm.px}
      mb={theme.spacing.xs.px}
      {...props}
    />
  );
};
