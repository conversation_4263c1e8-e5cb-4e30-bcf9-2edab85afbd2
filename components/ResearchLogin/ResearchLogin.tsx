import { Box, Card, HStack, Input, VStack } from "@chakra-ui/react";
import { createClientComponentClient } from "@supabase/auth-helpers-nextjs";
import { useUser } from "@supabase/auth-helpers-react";
import axios from "axios";
import { useRouter } from "next/router";
import { useContext, useState } from "react";

import { InputField as InputStyled } from "@/components/InputField/InputField";
import { Text } from "@/components/v2/Text/Text";
import { UserContext } from "@/context/user-context";
import { AuthLayout } from "@/layouts";
import { theme } from "@/styles/theme";

import { Button } from "../v2";
import { Loader } from "../v2/Loader/Loader";

const RESEARCH_PASSWORD = process.env.NEXT_PUBLIC_RESEARCH_PASSWORD;

export const ResearchLogin = () => {
  const formStates = {
    PASSWORD_NOT_VERIFIED: 1,
    PASSWORD_VERIFIED: 2,
    LOGGED_IN: 3,
  };

  const [formState, setFormState] = useState(formStates.PASSWORD_NOT_VERIFIED);
  const [password, setPassword] = useState("");
  const [showLoginError, setShowLoginError] = useState(false);
  const [isLoggingIn, setIsLoggingIn] = useState(false);
  const supabase = createClientComponentClient();
  const [email, setEmail] = useState("");
  const router = useRouter();
  const user = useUser();
  const { user: userContext } = useContext(UserContext);
  const [_, setUserData] = useState<object>({});

  const signIn = async (email) => {
    let signInError: any = null;
    const response = await axios
      .post("/api/create-research-user", {
        method: "POST",
        data: {
          email: `${email.trim()}@talamo.co.uk`,
          password: "talamopass",
        },
        headers: {
          "Content-Type": "application/json",
        },
      })
      .catch((err) => {
        signInError = err;
      });
    if (signInError) {
      setShowLoginError(true);
    }
    setUserData(response?.data?.data);
    setIsLoggingIn(true);
    window.location.href = `/dashboard/?research=true`;
  };

  const getButtonLabel = (user: any, currentFormState: number) => {
    if (!!user) {
      return "Continue";
    }
    if (!user && currentFormState === formStates.PASSWORD_NOT_VERIFIED) {
      return "Next";
    }
    if (!user && currentFormState === formStates.PASSWORD_VERIFIED) {
      return "Login";
    }
  };

  return (
    <>
      {isLoggingIn ? (
        <Loader variant="huzzah" loadingText="Thinking" isActive />
      ) : (
        <>
          <AuthLayout variant="login">
            <Card
              w={{ sm: "100%", md: "464px" }}
              border="none"
              borderRadius={theme.border.radius.md.px}
              bg={theme.colors.primary.white.hex}
              flex={1}
              overflow="hidden"
              variant="outline"
              p={"32px"}
              css={{
                boxShadow: theme.shadow.box,
              }}
            >
              <HStack mt={theme.spacing.sm.rem} justifyContent="center">
                <Text
                  variant="2xl"
                  element="h2"
                  mb={theme.spacing.sm.px}
                  textAlign="center"
                >
                  Login
                </Text>
              </HStack>
              {userContext?.metadata?.is_research && (
                <VStack mt={theme.spacing.md.rem}>
                  <Text
                    element="h3"
                    variant="xs"
                    textAlign="left"
                    alignSelf="flex-start"
                    m={0}
                  >
                    You are already logged in.
                  </Text>
                  <Input
                    disabled
                    _placeholder={{
                      color: "black",
                      fontWeight: "500",
                    }}
                    fontWeight="500"
                    css={{
                      padding: "20px 12px",
                      border: "2px solid " + theme.colors.primary.purple.hex,
                      minWidth: "164px",
                      flex: 1,
                    }}
                    type="text"
                    placeholder={userContext?.email}
                    onChange={(e) => setPassword(e.target.value)}
                  />
                </VStack>
              )}
              {!userContext?.metadata?.is_research &&
                formState === formStates.PASSWORD_VERIFIED && (
                  <VStack mb={theme.spacing.sm.rem}>
                    <Text
                      element="h3"
                      variant="xs"
                      textAlign="left"
                      alignSelf="flex-start"
                      m={0}
                    >
                      ID
                    </Text>
                    <Input
                      _placeholder={{
                        color: "black",
                        fontWeight: "500",
                      }}
                      fontWeight="500"
                      css={{
                        padding: "20px 12px",
                        border: "2px solid " + theme.colors.primary.purple.hex,
                        minWidth: "164px",
                        flex: 1,
                      }}
                      type="email"
                      onChange={(e) => setEmail(e.target.value)}
                    />
                  </VStack>
                )}
              {!userContext?.metadata?.is_research &&
                formState === formStates.PASSWORD_NOT_VERIFIED && (
                  <VStack mt={theme.spacing.sm.rem} alignItems="flex-start">
                    <Text
                      element="h3"
                      variant="xs"
                      textAlign="left"
                      alignSelf="flex-start"
                      m={0}
                    >
                      Password
                    </Text>
                    <InputStyled
                      type="password"
                      onChange={(e) => setPassword(e.target.value)}
                    />
                    {showLoginError && (
                      <Text
                        element="p"
                        variant="md"
                        textAlign="left"
                        alignSelf="flex-start"
                        m={0}
                        color={theme.colors.ui.alert_red_01.hex}
                      >
                        Please enter the correct password
                      </Text>
                    )}
                  </VStack>
                )}
              <Box>
                <VStack>
                  <Button
                    mt={theme.spacing.md.px}
                    onClick={() => {
                      if (userContext?.metadata?.is_research) {
                        setIsLoggingIn(true);
                        return router.push(`/dashboard`);
                      }
                      switch (formState) {
                        case !userContext?.metadata?.is_research &&
                          formStates.PASSWORD_NOT_VERIFIED:
                          if (password === RESEARCH_PASSWORD) {
                            setFormState(formStates.PASSWORD_VERIFIED);
                            return;
                          } else {
                            alert("Password incorrect!");
                            return;
                          }
                        case !userContext?.metadata?.is_research &&
                          formStates.PASSWORD_VERIFIED:
                          signIn(email);
                          return;
                      }
                    }}
                  >
                    {getButtonLabel(user, formState)}
                  </Button>
                </VStack>
              </Box>
            </Card>
          </AuthLayout>
        </>
      )}
    </>
  );
};
