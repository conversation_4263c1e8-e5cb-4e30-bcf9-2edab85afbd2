"use client";

import { Input } from "@chakra-ui/react";
import styled from "@emotion/styled";
import { forwardRef, useState } from "react";

import { theme } from "@/styles/theme";

type InputFieldStylesProps = {
  isFilled?: boolean;
};

const InputFieldStyles = styled(Input)<InputFieldStylesProps>`
  width: 100%;
  border-width: 3px;
  font-size: ${theme.v2Text.paragraph.md.fontSize};
  font-weight: ${(props) => (props?.isFilled ? 600 : 500)};
  padding: ${theme.spacing.sm.px};
  height: auto;
  border-color: ${(props) =>
    props?.isFilled
      ? `${theme.colors.primary.purple.hex};`
      : `${theme.colors.ui.grey_03.hex}`};
  border-radius: ${theme.border.radius.xs.rem};
  background-color: ${(props) =>
    props?.isFilled
      ? `${theme.colors.secondary.purple_04.hex}`
      : `${theme.colors.ui.grey_04.hex}`};
  :focus,
  :hover {
    font-weight: ${(props) => (props?.isFilled ? 600 : 500)};
    border-color: ${theme.colors.primary.purple.hex};
    background-color: ${theme.colors.secondary.purple_04.hex};
    border-width: 3px;
  }
`;

export const InputField = forwardRef<
  HTMLInputElement,
  {
    dispatch?: (args?: any) => any;
    dispatchAction?: string;
    onChangeFn?: (args: any) => any;
    [key: string]: any;
  }
>(({ dispatch, dispatchAction, onChangeFn, ...props }, ref) => {
  const [value, setValue] = useState("");
  return (
    <InputFieldStyles
      ref={ref}
      onChange={(e) => {
        if (dispatch) {
          dispatch({ type: dispatchAction, payload: e.target.value });
          return;
        }
        if (onChangeFn) {
          onChangeFn(e.target.value);
          return;
        }
        setValue(e.target.value);
      }}
      {...props}
    />
  );
});

InputField.displayName = "InputField";
