"use client";
import { Box, Container } from "@chakra-ui/react";

import { StarsBg } from "@/components/StarsBg/StarsBg";
import { theme } from "@/styles/theme";

type AdminLayoutProps = {
  children: React.ReactNode;
  header: React.ReactNode;
  hero?: React.ReactNode;
  tabs?: React.ReactNode;
};
const AdminLayout = ({ children, header, hero, tabs }: AdminLayoutProps) => {
  return (
    <>
      <Box display="flex" flexDir="column" minH="100vh">
        <Box>{header}</Box>
        <Box mt={theme.spacing.lg2.px} flex="1" display="flex" flexDir="column">
          {hero && (
            <Container maxW={theme.max_admin_width}>
              <Box>{hero}</Box>
            </Container>
          )}
          {tabs && <Container maxW={theme.max_admin_width}>{tabs}</Container>}
          <Box
            flex="1"
            textColor={theme.colors.primary.black.hex}
            bgColor={theme.colors.primary.white.hex}
            py={theme.spacing.lg.px}
            borderTopLeftRadius={theme.border.radius["4xl"].px}
            borderTopRightRadius={theme.border.radius["4xl"].px}
          >
            <Container maxW={theme.max_admin_width}>{children}</Container>
          </Box>
        </Box>
      </Box>
      <StarsBg zIndex={-1} />
    </>
  );
};

export { AdminLayout };
