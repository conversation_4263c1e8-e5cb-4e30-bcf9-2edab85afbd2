:root {
  /* Font weight */
  --weight-xs: 200;
  --weight-sm: 400;
  --weight-md: 500;
  --weight-lg: 600;
  --weight-xl: 800;
  /* Font sizes */
  --size-xs: 0.825rem;
  --size-sm: 1rem;
  --size-md: 1.2rem;
  --size-lg: 2.2rem;
  --size-xl: 3rem;
  --size-xxl: 4rem;
}
/* CSS Resets */
* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}
html {
  font-size: 16px;
}
html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  overscroll-behavior-y: none;
}
/* Font utility classes */
.size-xxs {
  font-size: var(--size-xs);
}
.size-xs {
  font-size: var(--size-sm);
}
.size-md {
  font-size: var(--size-md);
}
.size-lg {
  font-size: var(--size-lg);
}
.size-xl {
  font-size: var(--size-xl);
}
.size-xxl {
  font-size: var(--size-xxl);
}
.weight-xl {
  font-weight: var(--weight-xl);
}
.weight-lg {
  font-weight: var(--weight-lg);
}
.weight-md {
  font-weight: var(--weight-md);
}
.weight-sm {
  font-weight: var(--weight-sm);
}
.weight-xs {
  font-weight: var(--weight-xs);
}
.sticky-table-column {
  position: sticky;
  left: 0;
  background-color: white;
  z-index: 1;
}
.icon-2 path {
  stroke: currentColor !important;
  stroke-width: 2px !important;
}
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: purple white;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: purple;
  border-radius: 10px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background-color: white;
}
