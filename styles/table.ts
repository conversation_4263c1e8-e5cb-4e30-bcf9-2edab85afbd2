import { defineStyle, defineStyleConfig } from "@chakra-ui/react";

import { theme } from "./theme";

const simpleTableVariant = defineStyle(() => {
  return {
    table: {
      th: {
        textAlign: "left",
        textTransform: "none",
        letterSpacing: "0",
        p: theme.spacing.xs.px,
        color: theme.colors.primary.black.rgb,
        fontSize: `10px`,
        userSelect: "none",
      },
      td: {
        fontSize: `14px`,
        px: theme.spacing.xs.px,
        py: theme.spacing.xxs.px,
        whiteSpace: "nowrap",
      },
    },
  };
});

export const Table = defineStyleConfig({
  variants: {
    simple: simpleTableVariant,
  },
});
