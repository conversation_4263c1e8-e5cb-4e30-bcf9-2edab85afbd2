@font-face {
  font-family: "Objectivity";
  src: url("/fonts/Objectivity-Black.woff2") format("woff2");
  font-weight: 900;
  font-style: normal;
}

@font-face {
  font-family: "Objectivity";
  src: url("/fonts/Objectivity-ExtraBold.woff2") format("woff2");
  font-weight: 700;
  font-style: normal;
}

@font-face {
  font-family: "Objectivity";
  src: url("/fonts/Objectivity-Bold.woff2") format("woff2");
  font-weight: 600;
  font-style: normal;
}

@font-face {
  font-family: "Objectivity";
  src: url("/fonts/Objectivity-Medium.woff2") format("woff2");
  font-weight: 500;
  font-style: normal;
}

@font-face {
  font-family: "Objectivity";
  src: url("/fonts/Objectivity-Regular.woff2") format("woff2");
  font-weight: 400;
  font-style: normal;
}

@font-face {
  font-family: "Objectivity";
  src: url("/fonts/Objectivity-Light.woff2") format("woff2");
  font-weight: 200;
  font-style: normal;
}

@font-face {
  font-family: "Objectivity";
  src: url("/fonts/Objectivity-Thin.woff2") format("woff2");
  font-weight: 100;
  font-style: normal;
}

@font-face {
  font-family: "Objectivity";
  src: url("/fonts/Objectivity-BlackSlanted.woff2") format("woff2");
  font-weight: 900;
  font-style: italic;
}

@font-face {
  font-family: "Objectivity";
  src: url("/fonts/Objectivity-ExtraBoldSlanted.woff2") format("woff2");
  font-weight: 700;
  font-style: italic;
}

@font-face {
  font-family: "Objectivity";
  src: url("/fonts/Objectivity-BoldSlanted.woff2") format("woff2");
  font-weight: 600;
  font-style: italic;
}

@font-face {
  font-family: "Objectivity";
  src: url("/fonts/Objectivity-RegularSlanted.woff2") format("woff2");
  font-weight: 400;
  font-style: italic;
}

@font-face {
  font-family: "Objectivity";
  src: url("/fonts/Objectivity-LightSlanted.woff2") format("woff2");
  font-weight: 200;
  font-style: italic;
}

:root {
  --font-objectivity: "Objectivity", sans-serif;
}

html {
  font-family: var(--font-objectivity);
}
