/* 
    Talamo design system link : 
    https://www.figma.com/file/uGGLnnRVDgydNo249dxDXx/Library?type=design&node-id=554-3417&t=VT0O9aGY6XoZGX08-0 
*/
export const theme = {
  max_onboarding_width: "768px",
  max_site_width: "1440px",
  max_page_width: "1216px",
  max_page_width_v2: "1110px",
  max_admin_width: "1240px",
  max_admin_width_narrow: "950px",
  border: {
    radius: {
      button: { px: "140px", rem: "8.75rem" },
      xxs: { px: "2px", rem: "0.25rem" },
      xs: { px: "4px", rem: "0.25rem" },
      sm: { px: "6px", rem: "0.4rem" },
      md: { px: "8px", rem: "0.5rem" },
      ml: { px: "10px", rem: "0.625rem" },
      lg: { px: "16px", rem: "1rem" },
      xl: { px: "24px", rem: "1.5rem" },
      xxl: { px: "32px", rem: "2rem" },
      "4xl": { px: "48px", rem: "3rem" },
    },
  },
  shadow: {
    box: `-2px 2px 6px rgba(0, 0, 0, 0.14)`,
    medium: `1px 4px 23px rgba(0, 0, 0, 0.18)`,
    medium_hover: `3px 6px 32px rgba(0, 0, 0, 0.26)`,
    huge: `-14px 24px 60px rgba(0, 0, 0, 0.2)`,
  },
  colors: {
    blockout: { hex: "#333", rgb: "rgb(51, 51, 51)" },
    primary: {
      black: { hex: "#111124", rgb: "rgb(17, 17, 36)" },
      white: { hex: "#FFFFFC", rgb: "rgb(255, 255, 252)" },
      purple: {
        hex: "#814EF2",
        rgb: "rgb(129, 78, 242)",
        hover: { hex: "#AB8BF2", rgb: "rgb(171, 139, 242)" },
      },
      pink: { hex: "#FD5C70", rgb: "rgb(253, 92, 112)" },
      dark_pink: { hex: "#DB5B49", rgb: "rgb(219, 91, 73)" },
      yellow: { hex: "#FFCB3C", rgb: "rgb(255, 203, 60)" },
    },
    secondary: {
      purple_00: { hex: "#5058F3", rgb: "rgb(80, 88, 243)" },
      purple_01: {
        hex: "#35276D",
        rgb: "rgb(53, 39, 109)",
        rgbRaw: "53, 39, 109",
      },
      purple_02: { hex: "#AB8BF2", rgb: "rgb(171, 139, 242)" },
      purple_03: { hex: "#E4D8FE", rgb: "rgb(228, 216, 254)" },
      purple_04: { hex: "#F7EEFF", rgb: "rgb(247, 238, 255)" },
      purple_05: { hex: "#F9F6FF", rgb: "rgb(249, 246, 255)" },
    },
    tertiary: {
      pink_01: { hex: "#FE7B8B", rgb: "rgb(254, 123, 139)" },
      pink_04: { hex: "#FE7B8B", rgb: "rgb(254, 123, 139)" },
      pink_07: { hex: "#FFCAD0", rgb: "rgb(255, 202, 208)" },
      yellow_01: { hex: "#FFD769", rgb: "rgb(255, 215, 105)" },
      yellow_02: { hex: "#FFF0C7", rgb: "rgb(255, 215, 105)" },
      yellow_03: { hex: "#FFF9E9", rgb: "rgb(255, 249, 233)" },
      yellow_05: { hex: "#FFF0C7", rgb: "rgb(255, 240, 199)" },
      yellow_06: { hex: "#FFF9E9", rgb: "rgb(255, 249, 233)" },
      grey_01: { hex: "#9F9F9F" },
    },
    ui: {
      grey_01: { hex: "#52606D", rgb: "rgb(82, 96, 109)" },
      grey_02: { hex: "#B8B8B8", rgb: "rgb(184, 184, 184)" },
      grey_03: { hex: "#D9D9D9", rgb: "rgb(217, 217, 217)" },
      grey_04: { hex: "#F7F7F8", rgb: "rgb(247, 247, 248)" },
      alert_orange_01: {
        hex: "#F98119",
        rgb: "rgb(249, 129, 25)",
        hover: { hex: "#F6A55D", rgb: "rgb(246, 165, 93)" },
      },
      alert_orange_02: { hex: "#FFCFA3", rgb: "rgb(255, 207, 163)" },
      alert_orange_03: { hex: "#FFEAD7", rgb: "rgb(255, 234, 215)" },
      alert_green_01: {
        hex: "#44B342",
        rgb: "rgb(68, 179, 66)",
        hover: { hex: "#6FD06D", rgb: "rgb(111, 208, 109)" },
      },
      alert_green_02: { hex: "#EBFFD7", rgb: "rgb(235, 255, 215)" },
      alert_red_01: {
        hex: "#E74848",
        rgb: "rgb(231, 72, 72)",
        hover: { hex: "#F17373", rgb: "rgb(241, 115, 115)" },
      },
      alert_red_02: { hex: "#FFEFEF", rgb: "rgb(255, 239, 239)" },
      link: { hex: "#006FD6", rgb: "rgba(0, 111, 214, 1)" },
    },
  },
  gradients: {
    blue_grey: "linear-gradient(180deg, #C8D8E5 0%, #7191AF 25.52%)",
  },
  text: {
    hero_regular: {
      desktop: {
        fontWeight: "400",
        fontSize: "4.25rem",
        lineHeight: "4.75rem",
      },
      mobile: {
        fontWeight: "400",
        fontSize: "2.875rem",
        lineHeight: "3.375rem",
      },
    },
    hero_bold: {
      desktop: {
        fontWeight: "600",
        fontSize: "4.25rem",
        lineHeight: "4.75rem",
      },
      mobile: {
        fontWeight: "600",
        fontSize: "2.875rem",
        lineHeight: "3.375rem",
      },
    },
    h1: {
      desktop: {
        fontWeight: "500",
        fontSize: "2.875rem",
        lineHeight: "3.375rem",
      },
      mobile: {
        fontWeight: "500",
        fontSize: "2.25rem",
        lineHeight: "2.875rem",
      },
    },
    h2: {
      fontWeight: 500,
      fontSize: "1.7rem",
      lineHeight: "2.375rem",
    },
    h3: {
      fontWeight: "600",
      fontSize: "1.5rem",
      lineHeight: "2rem",
    },
    h4: {
      fontWeight: "600",
      fontSize: "1.125rem",
      lineHeight: "1.625rem",
    },
    h5: {
      fontWeight: "600",
      fontSize: "1rem",
      lineHeight: "1.5rem",
    },
    h6: {
      fontWeight: "600",
      fontSize: "0.875rem",
      lineHeight: "1.375rem",
    },
    hyperlink: {
      underline: true,
    },
    paragraph_huge: {
      fontWeight: "400",
      fontSize: "1.5rem",
      lineHeight: "2rem",
    },
    paragraph_large: {
      fontWeight: "400",
      fontSize: "1.125rem",
      lineHeight: "1.625rem",
    },
    paragraph_regular: {
      fontWeight: "400",
      fontSize: "1rem",
      lineHeight: "1.5rem",
    },
    paragraph_small: {
      fontWeight: "400",
      fontSize: "0.875rem",
      lineHeight: "1.375rem",
    },
    paragraph_extra_small: {
      fontWeight: "400",
      fontSize: "0.75rem",
      lineHeight: "1.35rem",
    },
  },
  v2Text: {
    headings: {
      xxs: {
        fontWeight: "600",
        fontSize: "12px",
        lineHeight: "22.4px",
      },
      xs: {
        fontWeight: "600",
        fontSize: "14px",
        lineHeight: "22.4px",
      },
      sm: {
        fontWeight: "600",
        fontSize: "18px",
        lineHeight: "24.96px",
      },
      mdsm: {
        fontWeight: "600",
        fontSize: "16px",
        lineHeight: "24.96px",
      },
      mdmd: {
        fontWeight: "600",
        fontSize: "20px",
        lineHeight: "28px",
      },
      md: {
        fontWeight: "600",
        fontSize: "24px",
        lineHeight: "33px",
      },
      lg: {
        fontWeight: "600",
        fontSize: "30px",
        lineHeight: "42px",
      },
      xl: {
        fontWeight: "600",
        fontSize: "36px",
        lineHeight: "50px",
      },
      "2xl": {
        fontWeight: "600",
        fontSize: "48px",
        lineHeight: "66.8px",
      },
      "3xl": {
        fontWeight: "600",
        fontSize: "60px",
        lineHeight: "84px",
      },
      "4xl": {
        fontWeight: "600",
        fontSize: "60px",
        lineHeight: "84px",
      },
      "5xl": {
        fontWeight: "600",
        fontSize: "60px",
        lineHeight: "84px",
      },
      "6xl": {
        fontWeight: "600",
        fontSize: "60px",
        lineHeight: "84px",
      },
    },
    paragraph: {
      "4xs": {
        fontWeight: "400",
        fontSize: "10px",
        lineHeight: "16px",
      },
      xxs: {
        fontWeight: "600",
        fontSize: "10px",
        lineHeight: "16px",
      },
      xs: {
        fontWeight: "400",
        fontSize: "12px",
        lineHeight: "18px",
      },
      sm: {
        fontWeight: "400",
        fontSize: "14px",
        lineHeight: "21.12px",
      },
      md: {
        fontWeight: "400",
        fontSize: "16px",
        lineHeight: "24px",
      },
      lg: {
        fontWeight: "400",
        fontSize: "18px",
        lineHeight: "26.8px",
      },
      xl: {
        fontWeight: "400",
        fontSize: "20px",
        lineHeight: "30px",
      },
      "2xl": {
        fontWeight: "400",
        fontSize: "24px",
        lineHeight: "36px",
      },
      "3xl": {
        fontWeight: "400",
        fontSize: "30px",
        lineHeight: "35.68px",
      },
      "4xl": {
        fontWeight: "400",
        fontSize: "36px",
        lineHeight: "54px",
      },
      "5xl": {
        fontWeight: "400",
        fontSize: "48px",
        lineHeight: "72px",
      },
      "6xl": {
        fontWeight: "400",
        fontSize: "60px",
        lineHeight: "90px",
      },
    },
  },
  breakpoints: {
    portable: "708px",
  },
  spacing: {
    custom: {
      authentication: { px: "32px", rem: "2rem" },
    },
    xxxs: { px: "2px", rem: "0.13rem" },
    xxs: { px: "6px", rem: "0.4rem" },
    xs: { px: "8px", rem: "0.5rem" },
    sm: { px: "16px", rem: "1rem" },
    ms: { px: "20px", rem: "1.5rem" },
    md: { px: "24px", rem: "1.5rem" },
    ml: { px: "32px", rem: "2rem" },
    lg: { px: "48px", rem: "3rem" },
    lg2: { px: "64px", rem: "4rem" },
    xl: { px: "96px", rem: "6rem" },
    xxl: { px: "128px", rem: "8rem" },
  },
};
