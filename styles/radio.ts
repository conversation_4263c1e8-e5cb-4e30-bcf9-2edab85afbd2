import { defineStyleConfig } from "@chakra-ui/react";

import { theme } from "./theme";

export const Radio = defineStyleConfig({
  variants: {
    primary: ({ colorScheme = "primary" }) => ({
      borderRadius: "100px",
      color: `${colorScheme}.500`,
      control: {
        _checked: {
          borderColor: theme.colors.primary.purple.hex,
          background: theme.colors.primary.purple.hex,
        },
      },
    }),
  },
  defaultProps: {
    variant: "primary",
    colorScheme: "primary",
  },
});
