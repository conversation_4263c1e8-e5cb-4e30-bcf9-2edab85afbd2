import { extendTheme } from "@chakra-ui/react";

import { Checkbox } from "./checkbox";
import { Input } from "./input";
import { FormLabel } from "./label";
import { Radio } from "./radio";
import { Select } from "./select";
import { Switch } from "./switch";
import { Table } from "./table";
import { Tabs } from "./tabs";

export const extendedTheme = extendTheme({
  fonts: {
    heading: "var(--font-objectivity)",
    body: "var(--font-objectivity)",
  },
  zIndices: {
    tooltip: 1000,
  },
  components: {
    Heading: {
      sizes: null,
    },
    Checkbox,
    Input,
    Select,
    FormLabel,
    Radio,
    Switch,
    Table,
    Tabs,
  },
  styles: {
    global: {
      ".sticky-right": {
        position: "sticky",
        right: 0,
        width: "50px",
      },
      ".sticky-left": {
        position: "sticky",
        left: 0,
      },
    },
  },
});
