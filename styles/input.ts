import { defineStyleConfig } from "@chakra-ui/react";

import { theme } from "./theme";

export const Input = defineStyleConfig({
  baseStyle: {
    borderRadius: "4px",
  },
  variants: {
    outline: {
      field: {
        borderColor: theme.colors.ui.grey_01.hex,
        borderWidth: "2px",
        fontSize: theme.v2Text.paragraph.md.fontSize,
        color: theme.colors.primary.black.hex,
        padding: theme.spacing.sm.px,
        height: "auto",
        outline: "none",
        _hover: {
          borderColor: theme.colors.primary.purple.hex,
          borderWidth: "2px",
        },
        _focus: {
          borderColor: theme.colors.primary.purple.hex,
          boxShadow: "none",
        },
        _invalid: {
          color: theme.colors.ui.alert_red_01.hex,
          borderColor: theme.colors.ui.alert_red_01.hex,
          boxShadow: `0 0 0 1px ${theme.colors.ui.alert_red_01.hex}`,
          backgroundColor: theme.colors.ui.alert_red_02.hex,
          _hover: {
            borderColor: theme.colors.ui.alert_red_01.hex,
          },
          _focus: {
            borderColor: theme.colors.ui.alert_red_01.hex,
          },
        },
      },
    },
  },
});
