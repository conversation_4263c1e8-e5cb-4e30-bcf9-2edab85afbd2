import { defineStyleConfig } from "@chakra-ui/react";

import { theme } from "./theme";

export const Tabs = defineStyleConfig({
  variants: {
    "soft-rounded": {
      tab: {
        bg: theme.colors.ui.grey_04.hex,
        color: theme.colors.primary.black.hex,
        borderRadius: "8px",
        fontWeight: "400",
        fontSize: "14px",
        lineHeight: "21.12px",
        padding: "8px 12px",
        _selected: {
          bg: theme.colors.primary.purple.hex,
          color: "white",
        },
      },
      tablist: {
        gap: "11px",
      },
      tabpanel: {
        padding: `${theme.spacing.md.px} 0 0 0`,
        borderRadius: "md",
      },
    },
  },
});
