import { defineStyleConfig } from "@chakra-ui/react";

import { theme } from "./theme";

export const Select = defineStyleConfig({
  variants: {
    outline: {
      field: {
        borderColor: theme.colors.primary.black.rgb,
        borderWidth: "2px",
        fontSize: theme.v2Text.paragraph.md.fontSize,
        color: theme.colors.primary.black.hex,
        padding: theme.spacing.ms.px,
        height: "auto",
        outline: "none",
        _hover: {
          borderColor: theme.colors.primary.purple.hex,
          borderWidth: "2px",
        },
        _focus: {
          borderColor: theme.colors.primary.purple.hex,
          boxShadow: "none",
        },
      },
    },
  },
});
