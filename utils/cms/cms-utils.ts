import { IS_PROD } from "@/constants/constants";

type CMSContentReq = {
  isClient?: boolean;
  query: string;
  locale?: string;
};

export async function getCMSContent({
  isClient = false,
  query,
}: CMSContentReq) {
  let _data: any = null;
  let error: any = null;
  try {
    _data = await fetch("https://graphql.datocms.com/", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-Environment": IS_PROD ? "main" : "main",
        Accept: "application/json",
        Authorization: `Bearer ${
          isClient
            ? process.env.NEXT_PUBLIC_DATO_CMS_API_KEY
            : process.env.DATO_CMS_API_KEY
        }`,
      },
      body: JSON.stringify({
        query,
      }),
    });
    _data = await _data.json();
  } catch (err) {
    console.log(err);
    error = err;
    throw new Error(error);
  }
  return { data: _data, error };
}
