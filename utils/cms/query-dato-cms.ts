import type { TypedDocumentNode } from "@graphql-typed-document-node/core";
import { print } from "graphql";

export default async function queryDatoCMS<
  TResult = unknown,
  TVariables = Record<string, unknown>,
>(
  document: TypedDocumentNode<TResult, TVariables>,
  variables?: TVariables,
  isDraft?: boolean
): Promise<TResult> {
  if (!process.env.NEXT_PUBLIC_DATO_CMS_API_KEY) {
    throw new Error(
      "Missing DatoCMS API token: make sure a NEXT_PUBLIC_DATO_CMS_API_KEY environment variable is set!"
    );
  }

  const headers: HeadersInit = {
    "Content-Type": "application/json",
    Accept: "application/json",
    "X-Exclude-Invalid": "true",
    Authorization: `Bearer ${process.env.NEXT_PUBLIC_DATO_CMS_API_KEY}`,
    //"Cache-Control": "public, max-age=600, stale-while-revalidate=300",
    "Cache-Control": "no-store",
  };

  if (isDraft) headers["X-Include-Drafts"] = "true";

  const response = await fetch("https://graphql.datocms.com/", {
    cache: "no-store",
    //cache: "default",
    next: { tags: ["datocms"] },
    method: "POST",
    headers,
    body: JSON.stringify({ query: print(document), variables }),
  });

  if (!response.ok) {
    const body = await response.text();

    throw new Error(`Invalid status code: ${response.status}\n${body}`);
  }

  const body = (await response.json()) as
    | { data: TResult }
    | { errors: unknown[] };

  if ("errors" in body) {
    throw new Error(`Invalid GraphQL request: ${body.errors}`);
  }

  return body.data;
}
