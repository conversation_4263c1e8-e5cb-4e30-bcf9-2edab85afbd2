export const findDiff = (obj1, obj2) => {
  const allKeys = new Set([...Object.keys(obj1), ...Object.keys(obj2)]);
  return Array.from(allKeys).reduce((acc, key) => {
    if (obj1[key] !== obj2[key]) {
      if (typeof obj1[key] === "object" && typeof obj2[key] === "object") {
        const nestedDiff = findDiff(obj1[key], obj2[key]);
        if (Object.keys(nestedDiff).length > 0) {
          acc[key] = nestedDiff;
        }
      } else {
        acc[key] = { old: obj1[key], new: obj2[key] };
      }
    }
    return acc;
  }, {});
};
