import { WebhookPayload } from "@/types/webhook";
import axios, { AxiosResponse } from "axios";

export interface WebhookConfig {
  username: string;
  password: string;
  timeout: number;
}

export interface WebhookResponse {
  status: number;
  statusText: string;
  data: any;
}

const username = process.env.N8N_WEBHOOK__AUTH_USERNAME as string;
const password = process.env.N8N_WEBHOOK__AUTH_PASSWORD as string;

/**
 * Default webhook configuration
 */
const DEFAULT_WEBHOOK_CONFIG: WebhookConfig = {
  username,
  password,
  timeout: 10000 // 10 seconds
};

/**
 * Send data to external webhook with basic authentication
 */
export async function sendWebhookRequest(
  webhookUrl: string,
  payload: WebhookPayload,
  config: Partial<WebhookConfig> = {}
): Promise<WebhookResponse> {
  const webhookConfig = { ...DEFAULT_WEBHOOK_CONFIG, ...config };

  try {
    const response: AxiosResponse = await axios.post(webhookUrl, payload, {
      auth: {
        username: webhookConfig.username,
        password: webhookConfig.password
      },
      headers: {
        'Content-Type': 'application/json',
      },
      timeout: webhookConfig.timeout,
    });

    return {
      status: response.status,
      statusText: response.statusText,
      data: response.data
    };

  } catch (error: any) {
    console.error("Webhook request failed:", error.message);

    // Handle different types of axios errors
    if (error.response) {
      // The request was made and the server responded with an error status
      throw new Error(`Webhook request failed: Server responded with ${error.response.status}: ${error.response.statusText}`);
    } else if (error.request) {
      // The request was made but no response was received
      throw new Error(`Webhook request timeout or no response: ${error.message}`);
    } else {
      // Something else happened in setting up the request
      throw new Error(`Webhook request setup failed: ${error.message}`);
    }
  }
}

/**
 * Validate webhook payload before sending
 */
export function validateWebhookPayload(payload: WebhookPayload): boolean {
  try {
    if (!Array.isArray(payload) || payload.length === 0) {
      console.error("Webhook payload must be a non-empty array");
      return false;
    }

    const item = payload[0];

    // Check required fields
    const requiredFields = ['metadata', 'scores', 'percentiles', 'context'];
    for (const field of requiredFields) {
      if (!item[field]) {
        console.error(`Missing required field in webhook payload: ${field}`);
        return false;
      }
    }

    // Check metadata fields
    const requiredMetadata = ['schoolId', 'age', 'userId', 'dateTaken', 'assessmentId'];
    for (const field of requiredMetadata) {
      if (!item.metadata[field]) {
        console.error(`Missing required metadata field: ${field}`);
        return false;
      }
    }

    return true;
  } catch (error) {
    console.error("Error validating webhook payload:", error);
    return false;
  }
}

/**
 * Create a retry mechanism for webhook requests
 */
export async function sendWebhookRequestWithRetry(
  webhookUrl: string,
  payload: WebhookPayload,
  config: Partial<WebhookConfig> = {},
  maxRetries: number = 3,
  retryDelay: number = 1000
): Promise<WebhookResponse> {
  let lastError: Error;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await sendWebhookRequest(webhookUrl, payload, config);
    } catch (error) {
      lastError = error as Error;
      console.warn(`Webhook request attempt ${attempt} failed:`, error);

      if (attempt < maxRetries) {
        console.log(`Retrying in ${retryDelay}ms...`);
        await new Promise(resolve => setTimeout(resolve, retryDelay));
        retryDelay *= 2; // Exponential backoff
      }
    }
  }

  throw lastError!;
}

/**
 * Test webhook connectivity
 */
export async function testWebhookConnectivity(
  webhookUrl: string,
  config: Partial<WebhookConfig> = {}
): Promise<boolean> {
  try {
    // Create a minimal test payload
    const testPayload: WebhookPayload = [{
      metadata: {
        schoolId: 0,
        age: 0,
        userId: "test",
        dateTaken: new Date().toISOString(),
        assessmentId: "test"
      },
      scores: {
        verbalReasoning: 0,
        visualReasoning: 0,
        workingMemory: 0,
        phonologicalAwareness: 0,
        processingSpeed: 0,
        spelling: 0,
        readingSpeed: 0,
        readingComprehension: 0
      },
      percentiles: {
        verbalReasoning: 0,
        visualReasoning: 0,
        workingMemory: 0,
        phonologicalAwareness: 0,
        processingSpeed: 0,
        spelling: 0,
        readingSpeed: 0,
        readingComprehension: 0
      },
      context: {
        reportedPerformance: {
          spelling: 3,
          reading: 3,
          phonics: 3,
          verbal: 3,
          speed: 3,
          academic: 3,
          disruptive: 1
        },
        studentContext: {
          adhd: "none",
          autism: "none",
          dyscalculia: "none",
          parentDyslexia: "none"
        },
        schoolContext: {
          schoolType: "state",
          deprivation: 30,
          paidInterventions: false
        }
      }
    }];

    await sendWebhookRequest(webhookUrl, testPayload, {
      ...config,
      timeout: 5000 // Shorter timeout for connectivity test
    });

    return true;
  } catch (error) {
    console.error("Webhook connectivity test failed:", error);
    return false;
  }
} 