import queryDatoCMS from "@/utils/cms/query-dato-cms";
import { 
  ReportRecommendationsCountDocument,
  ReportRecommendationsDocument,
  ReportRecommendationsQuery
} from "@/types/graphql/graphql";
import { DATO_PAGING_LIMIT } from "@/constants/constants";
import { RecommendationCategory, ScoringType } from "@/types/recommendations";
import { calculateAge } from "@/utils/students/calculateAge";
import { StudentData, ParsedAssessmentData } from "./assessment.utils";

export interface FilteredRecommendation {
  id: string;
  title: string;
  description?: string | null;
  category: string;
  testArea?: string | null;
  recommendationType: string;
  duration?: string | null;
  primaryLink?: {
    title: string;
    url?: string | null;
    description?: string | null;
  } | null;
}

export interface GroupedRecommendations {
  total: number;
  byCategory: {
    inTheClassroom: number;
    extraSupport: number;
    atHome: number;
    highPriority: number;
  };
  recommendations: {
    inTheClassroom: FilteredRecommendation[];
    extraSupport: FilteredRecommendation[];
    atHome: FilteredRecommendation[];
    highPriority: FilteredRecommendation[];
  };
}

/**
 * Fetch all recommendations from Dato CMS
 */
export async function fetchAllRecommendations(): Promise<ReportRecommendationsQuery["allRecommendations"]> {
  try {
    // Get total count of recommendations
    const recommendationsCount = await queryDatoCMS(
      ReportRecommendationsCountDocument,
      {},
      false
    );
    const numRecommendations = recommendationsCount._allRecommendationsMeta.count;

    // Fetch all recommendations in batches
    let allRecommendations: ReportRecommendationsQuery["allRecommendations"] = [];
    for (let skip = 0; skip < numRecommendations; skip += DATO_PAGING_LIMIT) {
      const batch = await queryDatoCMS(
        ReportRecommendationsDocument,
        { first: DATO_PAGING_LIMIT, skip },
        false
      );
      allRecommendations = [...allRecommendations, ...batch.allRecommendations];
    }

    return allRecommendations;
  } catch (error) {
    console.error("Error fetching recommendations from CMS:", error);
    throw new Error("Failed to fetch recommendations");
  }
}

/**
 * Filter recommendations based on student profile
 */
export function filterRecommendationsByProfile(
  studentData: StudentData,
  parsedData: ParsedAssessmentData,
  recommendations: ReportRecommendationsQuery["allRecommendations"]
): ReportRecommendationsQuery["allRecommendations"] {
  const studentAge = calculateAge(studentData.date_of_birth);
  
  const reportDataForFiltering = {
    result_data: parsedData.resultData,
    analysis: parsedData.analysisData
  };

  return getFilteredRecommendations(
    studentAge,
    reportDataForFiltering,
    recommendations
  );
}

/**
 * Group recommendations by category and format for response
 */
export function groupRecommendationsByCategory(
  filteredRecommendations: ReportRecommendationsQuery["allRecommendations"]
): GroupedRecommendations {
  const groupedRecommendations = {
    inTheClassroom: filteredRecommendations.filter(rec => rec.category === RecommendationCategory.Classroom),
    extraSupport: filteredRecommendations.filter(rec => rec.category === RecommendationCategory.ExtraSupport),
    atHome: filteredRecommendations.filter(rec => rec.category === RecommendationCategory.AtHome),
    highPriority: filteredRecommendations.filter(rec => rec.category === RecommendationCategory.HighPriority)
  };

  return {
    total: filteredRecommendations.length,
    byCategory: {
      inTheClassroom: groupedRecommendations.inTheClassroom.length,
      extraSupport: groupedRecommendations.extraSupport.length,
      atHome: groupedRecommendations.atHome.length,
      highPriority: groupedRecommendations.highPriority.length
    },
    recommendations: {
      inTheClassroom: formatRecommendations(groupedRecommendations.inTheClassroom),
      extraSupport: formatRecommendations(groupedRecommendations.extraSupport),
      atHome: formatRecommendations(groupedRecommendations.atHome),
      highPriority: formatRecommendations(groupedRecommendations.highPriority)
    }
  };
}

/**
 * Format recommendations for API response
 */
function formatRecommendations(
  recommendations: ReportRecommendationsQuery["allRecommendations"]
): FilteredRecommendation[] {
  return recommendations.map(rec => ({
    id: rec.id,
    title: rec.title,
    description: rec.description,
    category: rec.category,
    testArea: rec.testArea,
    recommendationType: rec.recommendationType,
    duration: rec.duration,
    primaryLink: rec.primaryLink
  }));
}

// Helper function to get test area score
function getTestAreaScore(testArea: string, reportData: any): number {
  return reportData.result_data.cognitiveProfile[testArea]?.composite?.standardScore || 100;
}

/**
 * Filter recommendations based on student profile (adapted from existing logic)
 */
function getFilteredRecommendations(
  studentAge: number,
  reportData: any,
  recommendations: ReportRecommendationsQuery["allRecommendations"]
): ReportRecommendationsQuery["allRecommendations"] {
  
  const filterByAge = (rec: any): boolean =>
    Array.isArray(rec.relevantAges) &&
    rec.relevantAges.includes(studentAge.toString());

  const filterByScoring = (rec: any): boolean => {
    const { scoringType, testArea, maximumScore, minimumScore } = rec;
    const dyslexiaRisk = reportData.analysis.dyslexiaRisk;
    const levelOfNeedRisk = reportData.analysis.levelOfNeedRisk;

    if (
      scoringType === ScoringType.Test &&
      testArea &&
      maximumScore !== undefined &&
      minimumScore !== undefined
    ) {
      const score = getTestAreaScore(testArea, reportData);
      if (score < minimumScore || score >= maximumScore) {
        return false;
      }
    }
    
    if (
      scoringType === ScoringType.DyslexiaRisk &&
      (dyslexiaRisk < minimumScore || dyslexiaRisk >= maximumScore)
    ) {
      return false;
    }
    
    if (
      scoringType === ScoringType.LevelOfNeedRisk &&
      (levelOfNeedRisk < minimumScore || levelOfNeedRisk >= maximumScore)
    ) {
      return false;
    }
    
    return true;
  };

  const recommendationsList = recommendations
    .filter(filterByAge)
    .filter(filterByScoring)
    .filter(
      (rec, index, self) => index === self.findIndex((r) => r.id === rec.id)
    );

  // Sort recommendations by priority
  recommendationsList.sort((a, b) => {
    // Level of need risk has highest priority
    if (
      a.scoringType === ScoringType.LevelOfNeedRisk &&
      b.scoringType !== ScoringType.LevelOfNeedRisk
    ) {
      return -1;
    }
    if (
      b.scoringType === ScoringType.LevelOfNeedRisk &&
      a.scoringType !== ScoringType.LevelOfNeedRisk
    ) {
      return 1;
    }

    // Dyslexia risk has second priority
    if (
      a.scoringType === ScoringType.DyslexiaRisk &&
      b.scoringType !== ScoringType.DyslexiaRisk
    ) {
      return -1;
    }
    if (
      b.scoringType === ScoringType.DyslexiaRisk &&
      a.scoringType !== ScoringType.DyslexiaRisk
    ) {
      return 1;
    }

    // Sort by test scores (lowest first)
    const scoreA = a.testArea ? getTestAreaScore(a.testArea, reportData) : 100;
    const scoreB = b.testArea ? getTestAreaScore(b.testArea, reportData) : 100;

    if (scoreA !== scoreB) {
      return scoreA - scoreB;
    }

    // Finally sort alphabetically by test area
    return (a.testArea || "").localeCompare(b.testArea || "");
  });

  return recommendationsList;
} 