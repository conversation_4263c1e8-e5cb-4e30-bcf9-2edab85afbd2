import { SupabaseClient } from "@supabase/supabase-js";
import { WebhookPayload } from "@/types/webhook";
import { calculateAge } from "@/utils/students/calculateAge";

export interface AssessmentData {
  id: string;
  user_id: string;
  analysis: string;
  result_data: string;
  student_code: string;
  assessment_meta: string;
  school_id: number;
  assessment_id: string;
  student: any;
}

export interface ParsedAssessmentData {
  assessmentMeta: any;
  resultData: any;
  analysisData: any;
}

export interface StudentData {
  student_id: number;
  first_names: string;
  surname: string;
  school_id: number;
  date_of_birth: string;
  adhd: string;
  dyslexia: string;
  spelling_ability: number;
  reading_ability: number;
  academic_ability: string;
  processing_speed: number;
  year: number;
}

export interface SchoolData {
  school_id: number;
  school_name: string;
  school_type: string;
  school_age: string;
  location: string;
}

/**
 * Fetch assessment data from Supabase with related student and school data
 */
export async function fetchAssessmentData(
  supabase: SupabaseClient,
  reportId: string
): Promise<AssessmentData | null> {
  try {
    const { data: reportData, error: reportError } = await supabase
      .from("studentReports")
      .select(`
        id,
        user_id,
        analysis,
        result_data,
        student_code,
        assessment_meta,
        school_id,
        assessment_id,
        student:student_code (
          student_id,
          first_names,
          surname,
          school_id,
          date_of_birth,
          adhd,
          dyslexia,
          spelling_ability,
          reading_ability,
          academic_ability,
          processing_speed,
          year
        )
      `)
      .eq("id", reportId)
      .single();

    if (reportError || !reportData) {
      console.error("Error fetching assessment data:", reportError);
      return null;
    }

    return reportData as AssessmentData;
  } catch (error) {
    console.error("Unexpected error in fetchAssessmentData:", error);
    return null;
  }
}

/**
 * Fetch school data by school ID
 */
export async function fetchSchoolData(
  supabase: SupabaseClient,
  schoolId: number
): Promise<SchoolData | null> {
  try {
    const { data: schoolData, error: schoolError } = await supabase
      .from("school")
      .select("school_id, school_name, school_type, school_age, location")
      .eq("school_id", schoolId)
      .single();

    if (schoolError || !schoolData) {
      console.error("Error fetching school data:", schoolError);
      return null;
    }

    return schoolData as SchoolData;
  } catch (error) {
    console.error("Unexpected error in fetchSchoolData:", error);
    return null;
  }
}

/**
 * Validate and extract student data from assessment response
 */
export function validateStudentData(assessmentData: AssessmentData): StudentData | null {
  try {
    // Handle student data (might be array from Supabase relation)
    const studentData = Array.isArray(assessmentData.student) 
      ? assessmentData.student[0] 
      : assessmentData.student;

    if (!studentData) {
      console.error("Student data not found in assessment");
      return null;
    }

    return studentData as StudentData;
  } catch (error) {
    console.error("Error validating student data:", error);
    return null;
  }
}

/**
 * Parse JSON fields from assessment data with proper error handling
 */
export function parseAssessmentJsonFields(assessmentData: AssessmentData): ParsedAssessmentData {
  const parseJsonField = (field: string, fieldName: string): any => {
    try {
      return typeof field === 'string' ? JSON.parse(field) : field || {};
    } catch (error) {
      console.error(`Error parsing ${fieldName}:`, error);
      return {};
    }
  };

  return {
    assessmentMeta: parseJsonField(assessmentData.assessment_meta, 'assessment_meta'),
    resultData: parseJsonField(assessmentData.result_data, 'result_data'),
    analysisData: parseJsonField(assessmentData.analysis, 'analysis')
  };
}

/**
 * Construct webhook payload according to the required format
 */
export function constructWebhookPayload(
  assessmentData: AssessmentData,
  studentData: StudentData,
  parsedData: ParsedAssessmentData,
  recommendations: any,
  requestId: string,
  schoolData?: SchoolData
): WebhookPayload {
  const { assessmentMeta, resultData, analysisData } = parsedData;

  return [{
    metadata: {
      schoolId: assessmentData.school_id,
      age: calculateAge(studentData.date_of_birth),
      userId: assessmentData.user_id || "unknown",
      dateTaken: assessmentMeta.dateTaken || new Date().toISOString(),
      assessmentId: assessmentData.assessment_id || "unknown",
      requestId: requestId
    },
    scores: extractCognitiveScores(resultData),
    percentiles: extractCognitivePercentiles(resultData),
    context: {
      reportedPerformance: extractReportedPerformance(studentData, assessmentMeta),
      studentContext: extractStudentContext(studentData),
      schoolContext: extractSchoolContext(schoolData, assessmentMeta)
    },
    recommendations
  }];
}

/**
 * Extract cognitive scores from result data
 */
function extractCognitiveScores(resultData: any): any {
  const cognitiveProfile = resultData.cognitiveProfile || {};
  
  return {
    verbalReasoning: cognitiveProfile.verbalReasoning?.composite?.standardScore || 0,
    visualReasoning: cognitiveProfile.visualReasoning?.composite?.standardScore || 0,
    workingMemory: cognitiveProfile.workingMemory?.composite?.standardScore || 0,
    phonologicalAwareness: cognitiveProfile.phonologicalAwareness?.composite?.standardScore || 0,
    processingSpeed: cognitiveProfile.processingSpeed?.composite?.standardScore || 0,
    spelling: cognitiveProfile.spelling?.composite?.standardScore || 0,
    readingSpeed: cognitiveProfile.readingSpeed?.composite?.standardScore || 0,
    readingComprehension: cognitiveProfile.readingComprehension?.composite?.standardScore || 0
  };
}

/**
 * Extract cognitive percentiles from result data
 */
function extractCognitivePercentiles(resultData: any): any {
  const cognitiveProfile = resultData.cognitiveProfile || {};
  
  return {
    verbalReasoning: cognitiveProfile.verbalReasoning?.composite?.percentileRank || 0,
    visualReasoning: cognitiveProfile.visualReasoning?.composite?.percentileRank || 0,
    workingMemory: cognitiveProfile.workingMemory?.composite?.percentileRank || 0,
    phonologicalAwareness: cognitiveProfile.phonologicalAwareness?.composite?.percentileRank || 0,
    processingSpeed: cognitiveProfile.processingSpeed?.composite?.percentileRank || 0,
    spelling: cognitiveProfile.spelling?.composite?.percentileRank || 0,
    readingSpeed: cognitiveProfile.readingSpeed?.composite?.percentileRank || 0,
    readingComprehension: cognitiveProfile.readingComprehension?.composite?.percentileRank || 0
  };
}

/**
 * Extract reported performance context
 */
function extractReportedPerformance(studentData: StudentData, assessmentMeta: any): any {
  return {
    spelling: studentData.spelling_ability || 3,
    reading: studentData.reading_ability || 3,
    phonics: assessmentMeta.phonics_performance || 3,
    verbal: assessmentMeta.verbal_performance || 3,
    speed: studentData.processing_speed || 3,
    academic: parseInt(studentData.academic_ability) || 3,
    disruptive: assessmentMeta.disruptive_behavior || 1
  };
}

/**
 * Extract student context information
 */
function extractStudentContext(studentData: StudentData): any {
  return {
    adhd: studentData.adhd?.toLowerCase().includes('diagnosed') ? "diagnosed" :
          studentData.adhd?.toLowerCase().includes('suspected') ? "suspected" : "none",
    autism: "none", // Field not available in student table
    dyscalculia: "none", // Field not available in student table
    parentDyslexia: "none" // Parent dyslexia data not available in current student schema
  };
}

/**
 * Extract school context information
 */
function extractSchoolContext(schoolData: SchoolData | undefined, assessmentMeta: any): any {
  return {
    schoolType: schoolData?.school_type?.toLowerCase() || "state",
    deprivation: assessmentMeta.deprivation_index || 30,
    paidInterventions: assessmentMeta.paid_interventions || false
  };
} 