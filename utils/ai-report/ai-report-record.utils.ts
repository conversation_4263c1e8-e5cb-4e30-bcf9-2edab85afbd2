import { SupabaseClient } from "@supabase/supabase-js";

export interface AIReportRecord {
  id: string;
  report_id: number;
  recommendation_list?: any;
  risk_analysis?: any;
  status: string;
  workflow_metadata?: any;
  error_message?: string;
  created_at: string;
  updated_at: string;
}

/**
 * Check if an AI report record exists for the given report_id
 */
export async function findAIReportRecord(
  supabase: SupabaseClient,
  reportId: string
): Promise<AIReportRecord | null> {
  try {
    const { data, error } = await supabase
      .from("ai_report")
      .select("*")
      .eq("report_id", parseInt(reportId))
      .order("created_at", { ascending: false })
      .limit(1)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        // No rows returned - record doesn't exist
        return null;
      }
      console.error("Error finding AI report record:", error);
      return null;
    }

    return data as AIReportRecord;
  } catch (error) {
    console.error("Unexpected error in findAIReportRecord:", error);
    return null;
  }
}

/**
 * Create a new AI report record with PENDING status
 */
export async function createAIReportRecord(
  supabase: SupabaseClient,
  reportId: string
): Promise<AIReportRecord | null> {
  try {
    const { data, error } = await supabase
      .from("ai_report")
      .insert({
        report_id: parseInt(reportId),
        status: 'PENDING',
        workflow_metadata: {
          created_by: 'ai-report-service',
          created_at: new Date().toISOString()
        }
      })
      .select()
      .single();

    if (error) {
      console.error("Error creating AI report record:", error);
      return null;
    }

    return data as AIReportRecord;
  } catch (error) {
    console.error("Unexpected error in createAIReportRecord:", error);
    return null;
  }
}

/**
 * Update AI report record status
 */
export async function updateAIReportStatus(
  supabase: SupabaseClient,
  aiReportId: string,
  status: string,
  additionalData?: {
    recommendation_list?: any;
    risk_analysis?: any;
    workflow_metadata?: any;
    error_message?: string;
  }
): Promise<boolean> {
  try {
    const updateData: any = {
      status: status,
      updated_at: new Date().toISOString()
    };

    // Add additional data if provided
    if (additionalData) {
      if (additionalData.recommendation_list) {
        updateData.recommendation_list = additionalData.recommendation_list;
      }
      if (additionalData.risk_analysis) {
        updateData.risk_analysis = additionalData.risk_analysis;
      }
      if (additionalData.workflow_metadata) {
        updateData.workflow_metadata = additionalData.workflow_metadata;
      }
      if (additionalData.error_message) {
        updateData.error_message = additionalData.error_message;
      }
    }

    const { error } = await supabase
      .from("ai_report")
      .update(updateData)
      .eq("id", aiReportId);

    if (error) {
      console.error("Error updating AI report status:", error);
      return false;
    }

    return true;
  } catch (error) {
    console.error("Unexpected error in updateAIReportStatus:", error);
    return false;
  }
}

/**
 * Get or create AI report record - ensures a record exists with PENDING status
 */
export async function getOrCreateAIReportRecord(
  supabase: SupabaseClient,
  reportId: string
): Promise<AIReportRecord | null> {
  try {
    // First, try to find existing record
    let aiReportRecord = await findAIReportRecord(supabase, reportId);

    if (aiReportRecord) {
      // Record exists, update status to PENDING
      console.log(`Found existing AI report record: ${aiReportRecord.id}`);
      
      const updated = await updateAIReportStatus(
        supabase, 
        aiReportRecord.id, 
        'PENDING',
        {
          workflow_metadata: {
            ...aiReportRecord.workflow_metadata,
            restarted_at: new Date().toISOString(),
            restart_count: (aiReportRecord.workflow_metadata?.restart_count || 0) + 1
          }
        }
      );

      if (updated) {
        // Return updated record
        return {
          ...aiReportRecord,
          status: 'PENDING',
          updated_at: new Date().toISOString()
        };
      } else {
        console.error("Failed to update existing AI report record to PENDING");
        return aiReportRecord; // Return as-is if update failed
      }
    } else {
      // Record doesn't exist, create new one
      console.log(`Creating new AI report record for report: ${reportId}`);
      aiReportRecord = await createAIReportRecord(supabase, reportId);
      
      if (aiReportRecord) {
        console.log(`Created new AI report record: ${aiReportRecord.id}`);
      } else {
        console.error("Failed to create new AI report record");
      }
      
      return aiReportRecord;
    }
  } catch (error) {
    console.error("Unexpected error in getOrCreateAIReportRecord:", error);
    return null;
  }
} 