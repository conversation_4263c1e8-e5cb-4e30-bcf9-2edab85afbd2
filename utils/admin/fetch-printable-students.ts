import { PrintableStudent } from "@/types/admin";
import { supabase } from "@/utils/supabase";

export const fetchStudentData = async (
  studentIds: number[]
): Promise<PrintableStudent[]> => {
  const { data, error } = await supabase
    .from("student")
    .select(
      `student_id, 
      student_code, 
      first_names, 
      year,
      surname`
    )
    .in("student_id", studentIds);

  if (error) {
    throw error;
  }

  return data;
};
