import { pdf } from "@react-pdf/renderer";
import { format, parseISO } from "date-fns";
import { createRoot } from "react-dom/client";

import { AnalysisChartPDF } from "@/components/Admin/AnalysisChart/AnalysisChartPDF";
import { PrintReportPDF } from "@/components/Admin/PrintReport/PrintReportPDF";
import { fetchAnalysisById } from "@/hooks/queries/useAnalysisByIdQuery";
import { PDFType } from "@/types/analysis";

interface IPrintReportInput {
  studentCode: string;
  type: PDFType;
}

export const printReport = async ({ studentCode, type }: IPrintReportInput) => {
  try {
    const student = await fetchAnalysisById({ studentCode });

    const resources = await fetch(`/api/pdf-report`, { cache: "no-cache" });
    const {
      pdfResources,
      riskLevelResources,
      recommendationCopy,
      allRecommendations,
      recommendationIcons,
      areasOfNeed,
      areasOfNeedFallback,
    } = await resources.json();

    const chartBase64 = await new Promise<string>((resolve, reject) => {
      const container = document.createElement("div");
      container.style.fontFamily = "Arial, sans-serif";
      container.style.position = "absolute";
      container.style.left = "-9999px";
      document.body.appendChild(container);

      const root = createRoot(container);

      document.fonts.ready.then(() => {
        root.render(
          <AnalysisChartPDF
            reportData={student}
            onBase64Ready={(base64) => {
              if (base64) {
                resolve(base64);
              } else {
                reject(new Error("Base64 generation failed."));
              }
              setTimeout(() => root.unmount(), 0);
              container.remove();
            }}
          />
        );
      });

      setTimeout(() => {
        reject(new Error("Base64 generation timed out."));
        root.unmount();
        container.remove();
      }, 10000);
    });

    const blob = await pdf(
      <PrintReportPDF
        studentCode={studentCode}
        studentData={student}
        type={type}
        pdfResources={pdfResources}
        chartBase64={chartBase64}
        riskLevelResources={riskLevelResources}
        recommendationCopy={recommendationCopy}
        recommendations={allRecommendations}
        recommendationIcons={recommendationIcons}
        areasOfNeed={areasOfNeed}
        areasOfNeedFallback={areasOfNeedFallback}
      />
    ).toBlob();

    const dateTaken = format(
      parseISO(student?.assessment_meta.dateTaken || ""),
      "ddMMyy"
    );

    const link = document.createElement("a");
    link.href = URL.createObjectURL(blob);
    link.download = `${student?.student.surname
      .toLowerCase()
      .replace(/\s+/g, "-")}-${student?.student.first_names
      .toLowerCase()
      .replace(
        /\s+/g,
        "-"
      )}-talamo-report-${type.toLowerCase()}-${dateTaken}.pdf`;
    link.click();
  } catch (error) {
    console.error("Error generating PDF:", error);
  }
};
