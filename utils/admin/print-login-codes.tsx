import { pdf } from "@react-pdf/renderer";

import { PrintableIds } from "@/components/Admin/PrintStudentIds/PrintStudentIds";
import { PrintableStudent } from "@/types/admin";

import { fetchStudentData } from "./fetch-printable-students";

export const printLoginCodes = async (studentIds) => {
  try {
    const data: PrintableStudent[] = await fetchStudentData(studentIds);
    const blob = await pdf(<PrintableIds data={data} />).toBlob();
    const link = document.createElement("a");
    link.href = URL.createObjectURL(blob);
    link.download = "student-login-codes.pdf";
    link.click();
  } catch (error) {
    console.error("Error generating PDF:", error);
  }
};
