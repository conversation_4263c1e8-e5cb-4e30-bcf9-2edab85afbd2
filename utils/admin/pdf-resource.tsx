import { PDFType } from "@/types/analysis";
import { RiskLevelsQuery } from "@/types/graphql/graphql";

export type PDFContent = {
  id: string;
  key: string;
  teacherValue?: string;
  value?: string;
  parentValue?: string;
};

type Replacements = Record<string, string | number>;

export const getPDFContent = (
  data: PDFContent[],
  key: string,
  type: PDFType,
  replacements?: Replacements
): string | undefined => {
  const content = data.find((item) => item.key === key);
  if (!content) {
    return undefined;
  }

  const selectedValue =
    type === PDFType.Teacher
      ? content.teacherValue || content.value
      : type === PDFType.Parent
        ? content.parentValue || content.value
        : content.value;

  return replacements
    ? Object.entries(replacements).reduce((result, [key, value]) => {
        const stringValue = String(value);

        result = result?.replace(
          new RegExp(`{{${key}}}'s`, "g"),
          stringValue.endsWith("s") ? `${stringValue}'` : `${stringValue}'s`
        );

        result = result?.replace(new RegExp(`{{${key}}}`, "g"), stringValue);

        return result;
      }, selectedValue)
    : selectedValue;
};

export const getRiskLevelContent = (
  cards: RiskLevelsQuery["allRiskLevels"],
  type: PDFType,
  replacements?: Replacements
): RiskLevelsQuery["allRiskLevels"] => {
  return cards.map((card) => {
    const processedDescription =
      type === PDFType.Teacher
        ? card.descriptionTeacher || card.description
        : type === PDFType.Parent
          ? card.descriptionParent || card.description
          : card.description;

    const updatedDescription = replacements
      ? Object.entries(replacements).reduce((result, [key, value]) => {
          const stringValue = String(value);

          result = result.replace(
            new RegExp(`{{${key}}}'s`, "g"),
            stringValue.endsWith("s") ? `${stringValue}'` : `${stringValue}'s`
          );

          result = result.replace(new RegExp(`{{${key}}}`, "g"), stringValue);

          return result;
        }, processedDescription)
      : processedDescription;

    const { descriptionParent, descriptionTeacher, ...rest } = card;

    return {
      ...rest,
      description: parseText(updatedDescription),
    };
  });
};

export const parseText = (html?: string): string => {
  if (!html) return "";
  const div = document.createElement("div");
  div.innerHTML = html;
  return div.textContent || div.innerText || "";
};
