type Replacements = { [key: string]: string | number };

export const replaceString = (
  template: string,
  replacements: Replacements
): string =>
  Object.keys(replacements).reduce((str, key) => {
    const value = String(replacements[key]);

    str = str.replace(
      new RegExp(`{{${key}}}('|&#39;)s`, "g"),
      value.endsWith("s") ? `${value}'` : `${value}'s`
    );

    str = str.replace(new RegExp(`{{${key}}}`, "g"), value);

    return str;
  }, template);
