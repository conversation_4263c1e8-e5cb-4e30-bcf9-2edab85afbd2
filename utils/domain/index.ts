/* eslint-disable @typescript-eslint/no-require-imports */
let isAppRouter = false;
try {
  require("next/headers");
  isAppRouter = true;
} catch (err) {}

export const getEnvironment = (
  domain: string | null
):
  | "development"
  | "test"
  | "production"
  | "research"
  | "localhost"
  | "unknown" => {
  if (!domain) return "unknown";

  if (domain.includes("dev.talamo.co.uk")) {
    return "development";
  } else if (domain.includes("test.talamo.co.uk")) {
    return "test";
  } else if (domain.includes("app.talamo.co.uk")) {
    return "production";
  } else if (domain.includes("research.talamo.co.uk")) {
    return "research";
  } else if (domain.includes("localhost")) {
    return "localhost";
  }

  return "unknown";
};

export const detectEnvironment = (
  req?: any
):
  | "development"
  | "test"
  | "production"
  | "research"
  | "localhost"
  | "unknown" => {
  let domain: string | null = null;

  if (typeof window !== "undefined") {
    domain = window.location.hostname;
  } else if (isAppRouter) {
    const { headers } = require("next/headers");
    const headersList = headers();
    domain = headersList.get("host") || null;
  } else if (req?.headers) {
    domain = req.headers.host || null;
  }

  return getEnvironment(domain);
};
