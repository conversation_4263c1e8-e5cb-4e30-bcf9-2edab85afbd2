export const sanitizeAgainstSQLInjection = (text: string): string => {
  let _sanitized: string = text.toLowerCase();
  _sanitized = _sanitized.replace("drop ", "");
  _sanitized = _sanitized.replace("drop table", "");
  _sanitized = _sanitized.replace("select ", "");
  _sanitized = _sanitized.replace("select * from", "");
  _sanitized = _sanitized.replace("grant ", "");
  _sanitized = _sanitized.replace("<script>", "");
  _sanitized = _sanitized.replace("?php", "");
  _sanitized = _sanitized.replace("insert ", "");
  _sanitized = _sanitized.replace(/[^a-zA-Z0-9@_\.\-]/g, "");
  return _sanitized;
};

export const sanitize = (value: any): string[] | string => {
  // Handle arrays
  if (Array.isArray(value)) {
    const sanitized = value.map((item: any) => {
      const _item = String(item);
      return sanitize(_item);
    });
    return sanitized as string[];
  }
  // Called recursively for arrays
  // Sanitization :: First round
  let _sanitized = value.replace(/[^a-zA-Z0-9|\-|\(|\)|\s]/g, "");
  // Sanitization :: Second round
  _sanitized = _sanitized.replace(
    /DROPTABLE|(DROP\ TABLE)|insert\ into|dbcollection|query|SELECT FROM|SELECT\s\s+FROM/gi,
    ""
  );
  // Sanitization :: Third round
  _sanitized = _sanitized.replace(
    /\<|\>|\|\|\;|\?|\!|^|\%|\$|\_|\£|\#|script|\*/gi,
    ""
  );
  const result = _sanitized.trim();
  return result;
};
