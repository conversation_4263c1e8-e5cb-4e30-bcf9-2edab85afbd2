import crypto from "crypto";

export const generateStudentCode = ({
  firstname,
  surname,
  dateOfBirth,
  schoolId,
}) => {
  const inputString = `${firstname}${surname}${dateOfBirth}${schoolId}`;
  const hash = crypto.createHash("sha256").update(inputString).digest("hex");
  const base62 = "123456789abcdefghijklmnopqrstuvwxyz";
  let uniqueCode = "";

  for (let i = 0; i < 8; i++) {
    const part = hash.substring(i * 2, i * 2 + 2);
    const int = parseInt(part, 16);
    uniqueCode += base62[int % base62.length];
  }

  return uniqueCode;
};
