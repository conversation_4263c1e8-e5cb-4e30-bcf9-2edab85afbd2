import {
  createPagesServerClient,
  SupabaseClient,
} from "@supabase/auth-helpers-nextjs";
import { NextApiRequest, NextApiResponse } from "next";

import { IS_PROD } from "@/constants/constants";
import { supabaseConfig } from "@/data/constants";

export async function initSupabase({
  req,
  res,
}: {
  req: NextApiRequest;
  res: NextApiResponse;
}): Promise<SupabaseClient> {
  const supabase: SupabaseClient = await createPagesServerClient(
    { req, res },
    {
      supabaseUrl: supabaseConfig.supabaseUrl,
      supabaseKey: supabaseConfig.supabaseKey,
    }
  );

  return supabase;
}

export function getStorageURL({ publicURL }: { publicURL?: boolean }): string {
  const _publicURL = supabaseConfig.supabaseUrl + `/storage/v1/object/public`;
  const _privateURL = supabaseConfig.supabaseUrl + `/storage/v1/object/sign`;

  const url = publicURL ? _publicURL : _privateURL;
  return url;
}
