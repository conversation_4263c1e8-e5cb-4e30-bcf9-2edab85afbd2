export const basicAuth = (req, res, next) => {
  const authHeader = req.headers.authorization || "";
  const [type, credentials] = authHeader.split(" ");

  if (type === "Basic") {
    const [username, password] = Buffer.from(credentials, "base64")
      .toString()
      .split(":");
    if (
      username === process.env.API_AUTH_USERNAME &&
      password === process.env.API_AUTH_PASSWORD
    ) {
      return next();
    }
  }

  res.status(401).json({ message: "Unauthorized" });
};

export const withBasicAuth = (handler) => (req, res) => {
  basicAuth(req, res, () => handler(req, res));
};
