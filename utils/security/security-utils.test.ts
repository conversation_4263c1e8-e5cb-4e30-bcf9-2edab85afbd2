import { sanitize } from "./security-utils";

describe("Security Utils", () => {
  it("Should sanitize an SQL injection string", () => {
    const testString = "<PERSON> has a cat DROP TABLE SELECT * FROM";
    const sanitizedString = sanitize(testString);
    expect(sanitizedString).toEqual("<PERSON> has a cat");
  });
  it("Should sanitize an SQL injection array", () => {
    const testArray = [
      "DROP TABLE",
      "INSERT INTO",
      "SELECT * FROM",
      "$query",
      "db.collection",
    ];
    const sanitizedArray = sanitize(testArray);
    expect(sanitizedArray).toEqual(["", "", "", "", ""]);
  });
});
