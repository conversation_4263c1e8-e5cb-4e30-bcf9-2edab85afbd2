import { Box } from "@chakra-ui/react";
import { ArrowTopRightOnSquareIcon } from "@heroicons/react/24/solid";
import parse, { DOMNode, domToReact, Element } from "html-react-parser";
import Link from "next/link";

import { Text } from "@/components/v2/Text/Text";
import { theme } from "@/styles/theme";
import { isDomNodeElement } from "@/utils/admin/is-dom-node-element";
import { replaceString } from "@/utils/replacement/replacement";

export const renderTitleWithLinks = (title: string, studentName: string) => {
  const processedTitle = replaceString(title, { name: studentName });
  return parse(processedTitle, {
    replace: (domNode: DOMNode) => {
      if (isDomNodeElement(domNode) && domNode.name === "a") {
        return (
          <Link
            href={domNode.attribs.href}
            target="_blank"
            key={domNode.attribs.href}
          >
            <Text
              as="span"
              element="h4"
              variant="mdmd"
              color={theme.colors.ui.link.hex}
              cursor="pointer"
              _hover={{ textDecoration: "underline" }}
              margin={0}
              display="inline-flex"
            >
              {domToReact(domNode.children as DOMNode[])}
              <Box as="span" ml="4px" mt="4px">
                <ArrowTopRightOnSquareIcon width={16} height={16} />
              </Box>
            </Text>
          </Link>
        );
      }
    },
  });
};
