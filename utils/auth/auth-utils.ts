import { SupabaseClient } from "@supabase/auth-helpers-nextjs";

export function getGroupFromEmail(emailWithGroupPrefix: string): string {
  const defaultGroup = "A";
  const _prefixedEmail = emailWithGroupPrefix.toLowerCase();
  const prefix: string[] = _prefixedEmail.split("_");
  if (prefix[0] === _prefixedEmail) return defaultGroup;
  if (prefix[0] === "gg") {
    return "G";
  }
  const group = prefix[0].replace("g", "").toUpperCase();
  return group;
}

export function authRoles() {
  return {
    ADMIN: 10,
    ORG: 2,
    USER: 1,
    TENANT: 0,
    INVALID: -1,
  };
}

export function redirect(to: string, permanent?: boolean) {
  return {
    redirect: {
      permanent: permanent || false,
      destination: to,
    },
  };
}

export async function isUserAuthorized({
  supabase,
  userId,
  targetRole,
}: {
  supabase: SupabaseClient;
  userId: string;
  targetRole: number;
}): Promise<boolean> {
  if (!targetRole) {
    return false;
  }
  let data: any = null;
  let error: any = null;
  try {
    const response = await supabase
      .from("user_data")
      .select("permissions")
      .eq("id", userId)
      .single();
    data = response?.data?.permissions;
  } catch (err) {
    error = err;
    throw new Error(error);
  }
  const isPermitted = data >= targetRole;
  return isPermitted as boolean;
}
