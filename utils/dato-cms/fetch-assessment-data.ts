import "dotenv/config";

import fs from "fs";
import { GraphQLClient } from "graphql-request";

import { GetNovaAssessmentDocument } from "../../types/graphql/graphql";

const client = new GraphQLClient("https://graphql.datocms.com", {
  headers: {
    Authorization: `Bearer ${process.env.DATO_CMS_API_KEY}`,
  },
});

const fetchAssessment = async (id: string) => {
  console.log(`🚀 Fetching assessment data for ID: ${id}...`);

  try {
    const data = await client.request(GetNovaAssessmentDocument, { id });

    console.log("✅ API request successful!");

    return data;
  } catch (error) {
    console.error("❌ Error fetching assessment data:", error);
    throw error;
  }
};

const saveDataToFile = (data: unknown, fileName: string) => {
  const dir = "data/generated";
  if (!fs.existsSync(dir)) {
    console.log(`📁 Creating directory: "${dir}"`);
    fs.mkdirSync(dir, { recursive: true });
  }

  const filePath = `${dir}/${fileName}`;
  fs.writeFileSync(filePath, JSON.stringify(data, null, 2));

  console.log(`💾 Data written to "${filePath}"`);
};

const runAsync = async () => {
  try {
    const assessmentId = process.env.NEXT_PUBLIC_NOVA_ASSESSMENT_ID!;
    const data = await fetchAssessment(assessmentId);
    saveDataToFile(data, "novaAssessment.json");
  } catch (error) {
    console.error("❌ Process failed:", error);
  }
};

runAsync();
