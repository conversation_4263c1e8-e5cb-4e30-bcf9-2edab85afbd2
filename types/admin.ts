export enum AdminRole {
  Administrator = "Administrator",
  Viewer = "Viewer",
  OrgAdmin = "OrgAdmin",
  SchoolAdmin = "SchoolAdmin",
}

export enum InviteStatus {
  Pending = "Pending",
  Accepted = "Accepted",
}

export interface County {
  abbreviation: string;
  country: string;
  name: string;
}

export enum CreditStatus {
  Unassigned = "Unassigned",
  Assigned = "Assigned",
  InProgress = "In Progress",
  Redeemed = "Redeemed",
  Expired = "Expired",
}

export interface PrintableStudent {
  student_id: number;
  student_code: string;
  first_names: string;
  year: number;
  surname: string;
}
