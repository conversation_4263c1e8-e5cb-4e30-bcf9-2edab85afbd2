export interface WebhookMetadata {
  schoolId: number;
  age: number;
  userId: string;
  dateTaken: string;
  assessmentId: string;
  requestId: string;
}

export interface AssessmentScores {
  verbalReasoning: number;
  visualReasoning: number;
  workingMemory: number;
  phonologicalAwareness: number;
  processingSpeed: number;
  spelling: number;
  readingSpeed: number;
  readingComprehension: number;
}

export interface AssessmentPercentiles {
  verbalReasoning: number;
  visualReasoning: number;
  workingMemory: number;
  phonologicalAwareness: number;
  processingSpeed: number;
  spelling: number;
  readingSpeed: number;
  readingComprehension: number;
}

export interface ReportedPerformance {
  spelling: number;
  reading: number;
  phonics: number;
  verbal: number;
  speed: number;
  academic: number;
  disruptive: number;
}

export interface StudentContext {
  adhd: "none" | "suspected" | "diagnosed";
  autism: "none" | "suspected" | "diagnosed";
  dyscalculia: "none" | "suspected" | "diagnosed";
  parentDyslexia: "none" | "mother" | "father" | "both";
}

export interface SchoolContext {
  schoolType: "state" | "private" | "academy" | "independent";
  deprivation: number;
  paidInterventions: boolean;
}

export interface AssessmentContext {
  reportedPerformance: ReportedPerformance;
  studentContext: StudentContext;
  schoolContext: SchoolContext;
}

export interface Recommendation {
  id: string;
  title: string;
  description: string;
  category: string;
  testArea: string;
  recommendationType: string;
  duration: string;
  primaryLink: string;
}

export interface Recommendations {
  inTheClassroom: Recommendation[];
  extraSupport: Recommendation[];
  atHome: Recommendation[];
  highPriority: Recommendation[];
}

export interface WebhookPayloadItem {
  metadata: WebhookMetadata;
  scores: AssessmentScores;
  percentiles: AssessmentPercentiles;
  context: AssessmentContext;
  recommendations: Recommendations;
}

export type WebhookPayload = WebhookPayloadItem[]; 