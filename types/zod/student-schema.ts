import { z } from "zod";

export const diagnosisStatus = z.enum([
  "Diagnosed",
  "Suspected",
  "None",
  "Don't know",
  "-",
]);

export const numberEnum = z.enum(["1", "2", "3", "4", "5", "-", "Don't know"]);

export const yearEnum = z.enum([
  "2",
  "3",
  "4",
  "5",
  "6",
  "7",
  "8",
  "9",
  "10",
  "11",
  "12",
]);

export const studentSchema = z.object({
  Surname: z.string({
    message: "Surname is required",
  }),
  "First names": z.string({
    message: "First name is required",
  }),
  "Date of birth": z.string({
    message: "Date of birth must be in dd/mm/yyyy format",
  }),
  "Year group": yearEnum,
  "Dyslexia status": diagnosisStatus,
  "ADHD status": diagnosisStatus,
  "Other conditions": z.string().optional(),
  "Reading ability": numberEnum,
  "Spelling ability": numberEnum,
  "Academic ability": numberEnum,
  "Processing speed": numberEnum,
});
