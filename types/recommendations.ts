export enum RecommendationCategory {
  HighPriority = "HighPriority",
  Classroom = "Classroom",
  ExtraSupport = "ExtraSupport",
  AtHome = "AtHome",
}

export enum ScoringType {
  Test = "test",
  LevelOfNeedRisk = "levelOfNeedRisk",
  DyslexiaRisk = "dyslexiaRisk",
}

export enum TestArea {
  PhonologicalAwareness = "phonologicalAwareness",
  ProcessingSpeed = "processingSpeed",
  ReadingComprehension = "readingComprehension",
  ReadingSpeed = "readingSpeed",
  Spelling = "spelling",
  VisualReasoning = "visualReasoning",
  VerbalReasoning = "verbalReasoning",
  WorkingMemory = "workingMemory",
}
