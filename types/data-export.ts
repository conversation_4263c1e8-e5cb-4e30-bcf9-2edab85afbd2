interface ScaleItem {
  scaleItemTitle: string;
}

interface ScaleItemGroup {
  scaleItemGroupTitle: string;
  id: string;
  scaleItemGroupItems: ScaleItem[];
}

interface Scale {
  id: string;
  scaleTitle: string;
  scaleItemGroups: ScaleItemGroup[];
}

type Scales = Scale[];

interface Answer {
  correct?: boolean;
  incorrect?: boolean;
  consecutiveIncorrect?: boolean;
  endScaleMaxIncorrect?: boolean;
  score?: number;
  answer?: {
    meta?: {
      data?: {
        results?: Array<{ hypothesis_score: number }>;
      };
    };
  };
  [key: string]: any;
}

interface User {
  id: string;
  user_id: string;
  answers: Record<string, Answer[]>;
}

interface ProcessedRecord {
  id: string;
  email?: string;
  user_id: string;
  [key: string]: any;
}

type QueryParams = {
  table: string;
  id: string;
};
