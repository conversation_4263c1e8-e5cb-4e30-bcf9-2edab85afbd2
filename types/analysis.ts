export interface ConfidenceInterval {
  lowerBound: number;
  upperBound: number;
}

export interface Scale {
  scaleId: string;
  rawScore: number;
  scaleName: string;
  standardScore: number;
  percentileRank: number;
  unusualResults: boolean;
  confidenceInterval: ConfidenceInterval;
}

export interface Composite {
  standardScore: number;
  percentileRank: number;
  unusualResults: boolean;
  confidenceInterval: ConfidenceInterval;
}

export interface CognitiveProfileArea {
  scales: Scale[];
  composite: Composite;
}

export interface CognitiveProfile {
  spelling: CognitiveProfileArea;
  readingSpeed: CognitiveProfileArea;
  workingMemory: CognitiveProfileArea;
  processingSpeed: CognitiveProfileArea;
  verbalReasoning: CognitiveProfileArea;
  visualReasoning: CognitiveProfileArea;
  readingComprehension: CognitiveProfileArea;
  phonologicalAwareness: CognitiveProfileArea;
}

export interface AnalysisDetail {
  levelOfNeedRisk: 1 | 2 | 3 | 4 | 5;
  dyslexiaRisk: 1 | 2 | 3 | 4 | 5;
  dyslexiaScore: number;
  levelOfNeedScore: number;
}

export interface AssessmentMeta {
  schoolId: number;
  dateTaken: string;
  assessmentId: string;
  assessmentName: string;
}

export interface ResultData {
  cognitiveProfile: CognitiveProfile;
}

export interface Student {
  first_names: string;
  surname: string;
  year: number;
  date_of_birth: string;
}

export interface RecommendationsFeedback {
  recommendationId: string;
  feedbackType: "positive" | "negative";
  feedback?: string;
}

export interface AnalysisData {
  id: number;
  analysis: AnalysisDetail;
  result_data: ResultData;
  hide_report: boolean;
  student: Student;
  student_code: string;
  assessment_meta: AssessmentMeta;
  recommendations_feedback: RecommendationsFeedback[];
}

export interface FeedbackData {
  id: string;
  recommendations_feedback: RecommendationsFeedback[];
}

export enum PDFType {
  Full = "Full",
  Teacher = "Teacher",
  Parent = "Parent",
}
