import localFont from "next/font/local";

export const objectivity = localFont({
  src: [
    {
      path: "./Objectivity-Black.woff2",
      weight: "900",
      style: "normal",
    },
    {
      path: "./Objectivity-ExtraBold.woff2",
      weight: "700",
      style: "normal",
    },
    {
      path: "./Objectivity-Bold.woff2",
      weight: "600",
      style: "normal",
    },
    {
      path: "./Objectivity-Medium.woff2",
      weight: "500",
      style: "normal",
    },
    {
      path: "./Objectivity-Regular.woff2",
      weight: "400",
      style: "normal",
    },
    {
      path: "./Objectivity-Light.woff2",
      weight: "200",
      style: "normal",
    },
    {
      path: "./Objectivity-Thin.woff2",
      weight: "100",
      style: "normal",
    },
    {
      path: "./Objectivity-BlackSlanted.woff2",
      weight: "900",
      style: "italic",
    },
    {
      path: "./Objectivity-ExtraBoldSlanted.woff2",
      weight: "700",
      style: "italic",
    },
    {
      path: "./Objectivity-BoldSlanted.woff2",
      weight: "600",
      style: "italic",
    },
    {
      path: "./Objectivity-RegularSlanted.woff2",
      weight: "400",
      style: "italic",
    },
    {
      path: "./Objectivity-LightSlanted.woff2",
      weight: "200",
      style: "italic",
    },
  ],
  display: "swap",
});
