//Talamo constants
const HAS_RESEARCH_LS =
  typeof window !== "undefined"
    ? localStorage.getItem("talamo-research-mode") === "true"
    : false;
const HAS_STUDENT_LS =
  typeof window !== "undefined"
    ? localStorage.getItem("talamo-student-mode") === "true"
    : false;
//const HAS_RESEARCH_LS = true;
export const STRIPE_ASSESSMENT_PURCHASE_PRICE = 6900;
export const STRIPE_ASSESSMENT_CURRENCY = "gbp";
export const STRIPE_ASSESSMENT_DESCRIPTION = "Talamo";
export const IS_PROD = process.env.NEXT_PUBLIC_VERCEL_ENV === "production";
export const IS_RESEARCH_ENV =
  process.env.NEXT_PUBLIC_VERCEL_GIT_COMMIT_REF === "research" ||
  HAS_RESEARCH_LS;
//export const IS_STUDENT_ENV = HAS_STUDENT_LS;
export const IS_STUDENT_ENV =
  process.env.NEXT_PUBLIC_VERCEL_GIT_COMMIT_REF === "test" || HAS_STUDENT_LS;
export const DEV_ENV_PASSWORD = "build-bounce-sax";
export const GA_MEASUREMENT_ID = "GTM-M3L8R583";
export const BACKGROUND_ASSESSMENT_ID = "UHsDywjxQcC11ROsT7CjSw";
export const CHILD_ASSESSMENT_ID = "173241767";
//export const RESEARCH_TEST_1_ASSESSMENT_ID = "bcWznWbLQXiHf3IGKn7AlA"; //Used for Moon Hall
export const RESEARCH_TEST_1_ASSESSMENT_ID = "B4lbrH-2R0CEedVIrM6org";
export const RESEARCH_TEST_2_ASSESSMENT_ID = "BRp-3eAhQfKJH6a9yoyW7g";
export const ORDER_EMAIL_ADDRESS = "<EMAIL>";
export const TALAMO_CREW_STORAGE_KEY = "tal-tech-team";
export const BREVO_API_URL = "https://api.brevo.com/v3";
export const BASE_URL = process.env.NEXT_PUBLIC_URL || "http://localhost:3000";
export const SCHOOL_TEST_ID = "VJdDtxjFSP-ghIeC2fvKRQ";
export const SCHOOL_TEST_NAME = "Dyslexia & cognitive test";
export const DATO_PAGING_LIMIT = 100;
