import { createPagesServerClient } from "@supabase/auth-helpers-nextjs";

import { Loading } from "@/components/Loading/Loading";
import { redirect } from "@/utils";

/*
 * SSR
 */
export async function getServerSideProps(ctx) {
  // // --------------------------------------------
  // // 1/1) User Check | Do we have a registered user who is logged in?
  // // --------------------------------------------
  // const supabase = createPagesServerClient(ctx);
  // const {
  //   data: { session },
  // } = await supabase.auth.getSession();
  // const {
  //   data: { user },
  // } = await supabase.auth.getUser();

  // if (!session || !user) return redirect(`/login?ref=invalid_session`);

  return {
    redirect: {
      destination: "/dashboard",
      permanent: false,
    },
  };
}

export default function HomePage() {
  <Loading />;
}
