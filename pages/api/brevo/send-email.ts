/* eslint-disable @typescript-eslint/no-non-null-asserted-optional-chain */
import type { NextApiRequest, NextApiResponse } from "next";

import { BREVO_API_URL } from "@/constants/constants";

const templateMapping = {
  welcome: 1,
  report: 2,
  reportReady: 6,
  schoolVerification: 20,
  schoolWelcome: 17,
};

export async function sendEmail({
  email,
  type,
  bcc,
  params,
}: {
  email: string;
  type: string;
  bcc?: string;
  params?: Record<string, any>;
}) {
  const templateId = templateMapping[type];
  const apiKey = process.env.BREVO_API_KEY || "";
  const apiUrl = `${BREVO_API_URL}/smtp/email`;

  const emailData = {
    sender: { name: "<PERSON><PERSON><PERSON>", email: "<EMAIL>" },
    to: [{ email: email }],
    bcc: bcc ? [{ email: bcc }] : undefined,
    templateId,
    params,
  };

  try {
    const response = await fetch(apiUrl, {
      method: "POST",
      headers: {
        accept: "application/json",
        "api-key": api<PERSON>ey,
        "content-type": "application/json",
      },
      body: JSON.stringify(emailData),
    });

    const data = await response.json();
    return { success: true, data };
  } catch (error) {
    return { success: false, error };
  }
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const bearerToken = req["headers"]?.["authorization"]?.split("Bearer ")[1];
  const isInternal = req["headers"]?.["x-internal"];
  if (
    !isInternal &&
    (!bearerToken || bearerToken !== process.env?.API_TOKEN!)
  ) {
    return res
      .status(401)
      .json({ message: "Please provide a valid bearer token" });
  }

  if (req.method === "POST") {
    const { email, type, bcc, params } = req.body;

    const result = await sendEmail({ email, type, bcc, params });

    if (result.success) {
      return res
        .status(200)
        .json({ message: "Success", response: result.data });
    } else {
      return res.status(500).json({ error: result.error });
    }
  } else {
    return res.status(405).json({ error: "Method not allowed." });
  }
}
