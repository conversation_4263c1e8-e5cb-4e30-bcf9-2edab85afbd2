/* eslint-disable @typescript-eslint/no-non-null-asserted-optional-chain */
import type { NextApiRequest, NextApiResponse } from "next";

import { BREVO_API_URL } from "@/constants/constants";

export default function handler(req: NextApiRequest, res: NextApiResponse) {
  const bearerToken = req["headers"]?.["authorization"]?.split("Bearer ")[1];
  const isInternal = req["headers"]?.["x-internal"];
  const LIST_ID = 5;
  if (
    !isInternal &&
    (!bearerToken || bearerToken !== process.env?.API_TOKEN!)
  ) {
    return res
      .status(401)
      .json({ message: "Please provide a valid bearer token" });
  }

  if (req.method === "POST") {
    const { email } = req.body;

    const apiKey = process.env.BREVO_API_KEY || "";
    const apiUrl = `${BREVO_API_URL}/contacts`;

    const signupData = {
      email: email,
      listIds: [LIST_ID],
    };

    const jsonData = JSON.stringify(signupData);
    const headers = new Headers();
    headers.append("accept", "application/json");
    headers.append("api-key", apiKey);
    headers.append("content-type", "application/json");

    const requestOptions = {
      method: "POST",
      headers: headers,
      body: jsonData,
    };

    fetch(apiUrl, requestOptions)
      .then((response) => response.json())
      .then((data) => {
        return res.status(200).json({ message: "success", response: data });
      })
      .catch((error) => {
        return res.status(500).json({ error: error });
      });
  } else {
    return res.status(405).json({ error: "Method not allowed." });
  }
}
