import { createPagesServerClient } from "@supabase/auth-helpers-nextjs";
import Stripe from "stripe";

import {
  CHILD_ASSESSMENT_ID,
  STRIPE_ASSESSMENT_CURRENCY,
  STRIPE_ASSESSMENT_DESCRIPTION,
} from "@/constants/constants";

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || "", {
  apiVersion: "2025-01-27.acacia",
});

const handler = async (req, res) => {
  const { amount, payment_intent_id, supabase_user_id, supabase_email } =
    req.body;
  const supabase = createPagesServerClient({ req, res });

  if (!supabase.auth.getSession()) {
    return res.status(401).send("Unauthorized");
  }

  //Create payment intent
  if (payment_intent_id) {
    try {
      const current_intent =
        await stripe.paymentIntents.retrieve(payment_intent_id);
      if (current_intent) {
        const updated_intent = await stripe.paymentIntents.update(
          payment_intent_id,
          {
            amount: amount,
          }
        );
        res.status(200).json({ ...updated_intent });
        return;
      }
    } catch (e: any) {
      if (e.code !== "resource_missing") {
        const errorMessage =
          e instanceof Error ? e.message : "Internal server error";
        res.status(500).json({ statusCode: 500, message: errorMessage });
        return;
      }
    }
  }
  try {
    const params = {
      amount: amount,
      currency: STRIPE_ASSESSMENT_CURRENCY,
      description: STRIPE_ASSESSMENT_DESCRIPTION,
      automatic_payment_methods: {
        enabled: true,
      },
      receipt_email: supabase_email,
      metadata: {
        supabase_user_id: supabase_user_id,
        supabase_email: supabase_email,
        environment: process.env.NEXT_PUBLIC_VERCEL_ENV || "",
        assessment_id: CHILD_ASSESSMENT_ID || "",
      },
    };
    const payment_intent = await stripe.paymentIntents.create(params);
    res.status(200).json({ ...payment_intent });
  } catch (err) {
    const errorMessage =
      err instanceof Error ? err.message : "Internal server error";
    res.status(500).json({ statusCode: 500, message: errorMessage });
  }
};
export default handler;
