import { createPagesServerClient } from "@supabase/auth-helpers-nextjs";
import { buffer, RequestHandler } from "micro";
import { NextApiRequest, NextApiResponse } from "next";
import Stripe from "stripe";

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || "", {
  apiVersion: "2025-01-27.acacia",
});

const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET || "";

export const config = {
  api: {
    bodyParser: false,
  },
};

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const supabase = createPagesServerClient({ req, res });

  if (req.method === "POST") {
    const buf = await buffer(req);
    const sig = req.headers["stripe-signature"]!;

    let event: Stripe.Event;

    try {
      event = stripe.webhooks.constructEvent(
        buf.toString(),
        sig,
        webhookSecret
      );
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Unknown error";
      res.status(400).send(`Webhook Error: ${errorMessage} ${sig}`);
      return;
    }

    if (event.type === "charge.succeeded") {
      const payment = event.data.object as Stripe.Charge;
      const { error: supabaseError } = await supabase.from("orders").insert({
        user_id: payment.metadata.supabase_user_id,
        stripe_payment_id: payment.id,
        payment_amount: payment.amount,
        node_env: payment.metadata.environment,
        assessment_id: payment.metadata.assessment_id,
      });

      if (supabaseError) {
        res.status(400).send(`Supabase Error: ${supabaseError.message}`);
      }
    }

    res.status(200).json({ received: true });
  } else {
    res.setHeader("Allow", "POST");
    res.status(405).end("Method Not Allowed");
  }
};

export default handler as RequestHandler;
