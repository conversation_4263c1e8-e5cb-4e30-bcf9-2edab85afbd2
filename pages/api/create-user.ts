import { createPagesServerClient, User } from "@supabase/auth-helpers-nextjs";
import cors from "cors";
import { NextApiRequest, NextApiResponse } from "next";
import { createRouter } from "next-connect";

import { IS_PROD } from "@/constants/constants";
import { ASSESSMENT_DATA_TABLE } from "@/data/constants";
import { sanitizeAgainstSQLInjection } from "@/utils";

const router = createRouter<NextApiRequest, NextApiResponse>();

router
  .use(cors())
  .use(async (req, res, next) => {
    const start = Date.now();
    await next(); // call next in chain
    const end = Date.now();
  })
  .post(async (req: NextApiRequest, res: NextApiResponse) => {
    const supabase = await createPagesServerClient(
      { req, res },
      {
        supabaseUrl: process.env.NEXT_PUBLIC_SUPABASE_V2_URL,
        supabaseKey: process.env.NEXT_PUBLIC_SUPABASE_V2_ANON_KEY,
      }
    );

    // --------------------
    // REQUEST BODY
    // --------------------
    // Raw
    const { email, password, isOptedIn } = req.body?.data || {};
    if (!email || !password) {
      return res.status(400).send({ message: "Invalid request body." });
    }

    // Attempt to sanitize
    let _signUpData: any = {};
    let _email: string = "";
    let _password: string = "";
    try {
      _email = sanitizeAgainstSQLInjection(email);
      _password = password;
    } catch (err) {
      return res
        .status(400)
        .send({ message: "Invalid request body [1]", error: String(err) });
    }

    // --------------------
    // SIGN OUT
    // --------------------
    try {
      await supabase.auth.signOut();
    } catch (err) {}

    // --------------------
    // SIGN UP
    // --------------------
    try {
      const signUpResponse = await supabase.auth.signUp({
        email: _email,
        password: _password,
        options: {
          data: {
            is_research: false,
            is_opted_in: isOptedIn,
          },
        },
      });
      _signUpData = signUpResponse.data;

      if (signUpResponse.error) {
        return res.status(400).send({ error: signUpResponse.error });
      }

      await supabase.auth.signInWithPassword({
        email: _email,
        password: _password,
      });
    } catch (err) {}

    // --------------------
    // CREATE DB ENTRY
    // --------------------
    let userEntry: any = null;
    try {
      const { user }: { user: User } = _signUpData;
      const { data, error } = await supabase
        .from(ASSESSMENT_DATA_TABLE)
        .upsert({
          group: IS_PROD ? "PAID_USER_PROD" : "PAID_USER_DEV",
          email: _email,
          user_id: user?.id,
          scales_unlocked: { scales_unlocked: [] },
          scales_completed: { scales_completed: [] },
          assessment_progress: { assessment_progress: [] },
          parents_bq_complete: false,
          assessment_takers: {
            [String(user?.id)]: {
              email: true,
            },
          },
        })
        .select();
      userEntry = data;
      if (error) {
        throw new Error(JSON.stringify(error, null, 2));
      }
    } catch (err) {
      return res
        .status(400)
        .send({ message: "sign up error", error: String(err) });
    }

    // --------------------
    // SIGN IN
    // --------------------
    try {
      const response = await supabase.auth.signInWithPassword({
        email: _email,
        password: _password,
      });
      return res.status(200).send(response);
    } catch (err) {
      return res
        .status(401)
        .send({ message: "Server sign in error", error: String(err) });
    }
  });

export default router.handler({
  onError: (err: any, req, res) => {
    console.error(err.stack);
    res.status(err.statusCode || 500).end(err.message);
  },
});
