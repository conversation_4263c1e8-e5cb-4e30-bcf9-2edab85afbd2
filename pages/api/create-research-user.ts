/* eslint-disable @typescript-eslint/no-explicit-any */
import { User } from "@supabase/auth-helpers-nextjs";
import cors from "cors";
import { NextApiRequest, NextApiResponse } from "next";
import { createRouter } from "next-connect";

import { RESEARCH_TABLE } from "@/data/constants";
import { getGroupFromEmail, sanitizeAgainstSQLInjection } from "@/utils";
import { initSupabase } from "@/utils/security/supabase";

const router = createRouter<NextApiRequest, NextApiResponse>();

router
  .use(cors())
  .use(async (req, res, next) => {
    await next(); // call next in chain
  })
  .post(async (req: NextApiRequest, res: NextApiResponse) => {
    const supabase = await initSupabase({ req, res });
    // --------------------
    // REQUEST BODY
    // --------------------
    // Raw
    const { email, password } = req.body?.data || {};
    if (!email || !password) {
      return res.status(400).send({ message: "Invalid request body." });
    }
    // Attempt to sanitize
    let _signUpData: any = {};
    let _email: string = "";
    let _password: string = "";
    try {
      _email = sanitizeAgainstSQLInjection(email);
      _password = password;
    } catch (err) {
      return res
        .status(400)
        .send({ message: "Invalid request body [1]", error: String(err) });
    }

    // --------------------
    // SIGN OUT
    // --------------------
    try {
      await supabase.auth.signOut();
    } catch (err) {
      console.error("could not sign out");
    }

    // --------------------
    // SIGN UP
    // --------------------
    try {
      const response = await supabase.auth.signUp({
        email: _email,
        password: _password,
        options: {
          data: {
            is_research: true,
          },
        },
      });
      _signUpData = response.data;
      if (response.error) {
        if (response.error.message === "User already registered") {
          const response = await supabase.auth.signInWithPassword({
            email: _email,
            password: _password,
          });
          return res.status(200).send(response);
        }
        return res.status(400).send({ error: response?.error });
      }
    } catch (err) {
      return res.status(400).send({ message: "sign up error", error: err });
    }

    // --------------------
    // CREATE DB ENTRY
    // --------------------
    let userEntry: any = null;
    try {
      const { user }: { user: User } = _signUpData;
      const { data, error } = await supabase
        .from(RESEARCH_TABLE)
        .upsert({
          group: getGroupFromEmail(_email),
          email: _email,
          user_id: user?.id,
          scales_unlocked: { scales_unlocked: [] },
          scales_completed: { scales_completed: [] },
          assessment_progress: { assessment_progress: [] },
          parents_bq_complete: true,
          assessment_takers: {
            [String(user?.id)]: {
              email: true,
            },
          },
        })
        .select();
      userEntry = data;
      if (error) {
        throw new Error(JSON.stringify(error, null, 2));
      }
    } catch (err) {
      return res
        .status(400)
        .send({ message: "sign up error", error: String(err) });
    }

    // --------------------
    // SIGN IN
    // --------------------
    try {
      const response = await supabase.auth.signInWithPassword({
        email: _email,
        password: _password,
      });
      return res.status(200).send(response);
    } catch (err) {
      return res
        .status(401)
        .send({ message: "Server sign in error", error: String(err) });
    }
  });

export default router.handler({
  onError: (err: any, req, res) => {
    console.error(err.stack);
    res.status(err.statusCode || 500).end(err.message);
  },
});
