import { useSupabaseClient } from "@supabase/auth-helpers-react";
import { useRouter } from "next/navigation";
import posthog from "posthog-js";
import { useContext, useEffect } from "react";

import { Loading } from "@/components/Loading/Loading";
import { useStudentStore } from "@/context/student-store";
import { UserContext } from "@/context/user-context";
import { useAssessmentStore } from "@/hooks";

export default function LogoutPage() {
  const { clearUser } = useContext(UserContext);
  const supabase = useSupabaseClient();
  const router = useRouter();
  const resetStudentStore = useStudentStore((state) => state.reset);
  const { resetAssessmentStore } = useAssessmentStore();

  useEffect(() => {
    const handleLogout = async () => {
      await supabase.auth.signOut();
      sessionStorage.removeItem("onboardingComplete");
      posthog.reset();
      clearUser();
      resetStudentStore();
      resetAssessmentStore();
      router.push("/login");
    };

    handleLogout();
  }, [supabase, router, clearUser, resetStudentStore, resetAssessmentStore]);

  return <Loading />;
}
