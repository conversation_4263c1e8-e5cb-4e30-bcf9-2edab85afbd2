import { useEffect, useState } from "react";

import { ResearchLogin } from "@/components/ResearchLogin/ResearchLogin";
import { StudentLogin } from "@/components/StudentLogin";
import { IS_RESEARCH_ENV, IS_STUDENT_ENV } from "@/constants/constants";
import { LoginLayout } from "@/layouts";

export default function LoginPage() {
  const [origin, setOrigin] = useState("");

  useEffect(() => {
    if (typeof window !== "undefined") {
      setOrigin(window.location.origin);
    }
  }, []);

  if (IS_STUDENT_ENV || origin === "https://test.talamo.co.uk") {
    return <StudentLogin />;
  }
  if (IS_RESEARCH_ENV) {
    return <ResearchLogin />;
  }
  return <LoginLayout />;
}
