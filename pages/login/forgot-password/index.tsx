import { createPagesServerClient } from "@supabase/auth-helpers-nextjs";
import Head from "next/head";

import { ForgotPasswordCard } from "@/components/Card/Card/Card";
import { AuthLayout } from "@/layouts";

export async function getServerSideProps(ctx) {
  const supabase = createPagesServerClient(ctx);

  if (!supabase.auth.getSession()) {
    return {
      redirect: {
        destination: "/login",
        permanent: false,
      },
    };
  }
  return {
    props: {},
  };
}

export default function ForgotPasswordPage() {
  return (
    <>
      <Head>
        <title>Reset password</title>
      </Head>
      <AuthLayout variant="password-reset">
        <ForgotPasswordCard />
      </AuthLayout>
    </>
  );
}
