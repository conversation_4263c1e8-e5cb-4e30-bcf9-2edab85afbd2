import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

import { Loading } from "@/components/Loading/Loading";
import { TALAMO_CREW_STORAGE_KEY } from "@/constants/constants";
import { CreateAccountLayout } from "@/layouts";

export async function getServerSideProps() {
  return {
    props: {},
  };
}

export default function CreateAccountPage() {
  const router = useRouter();
  const [hasAccess, setHasAccess] = useState<boolean | null>(null);

  useEffect(() => {
    if (localStorage.getItem(TALAMO_CREW_STORAGE_KEY) === "true") {
      setHasAccess(true);
    } else {
      setHasAccess(false);
    }
  }, [router, hasAccess]);

  // Loading
  if (hasAccess === null) {
    return <Loading />;
  }

  // Forbidden - Reinstate to disable account creation
  // if (hasAccess === false) {
  //   return router.push("/login");
  // }

  // Authorized
  return <CreateAccountLayout hasAccess={true} />;
}
