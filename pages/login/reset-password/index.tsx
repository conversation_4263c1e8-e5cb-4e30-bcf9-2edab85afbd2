import { createClientComponentClient } from "@supabase/auth-helpers-nextjs";
import Head from "next/head";
import { useState } from "react";

import { ResetPasswordCard } from "@/components/Card/Card/Card";
import { AuthLayout } from "@/layouts";

export default function ResetPasswordPage() {
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [validationMessage, setValidationMessage] = useState("");
  const supabase = createClientComponentClient();

  const handlepasswordReset = async ({
    password,
    confirmPassword,
  }: {
    password: string;
    confirmPassword: string;
  }) => {
    if (password === confirmPassword) {
      setIsSubmitted(true);
      const { error } = await supabase.auth.updateUser({ password });
      if (error) {
        setValidationMessage(error?.message || "");
      }
    } else {
      setValidationMessage("Passwords do not match");
    }
  };
  return (
    <>
      <Head>
        <title>Reset password</title>
      </Head>
      <AuthLayout variant="password-reset">
        <ResetPasswordCard
          isSubmitted={isSubmitted}
          errorMsg={validationMessage}
          onSubmit={(values) => handlepasswordReset(values)}
        />
      </AuthLayout>
    </>
  );
}
