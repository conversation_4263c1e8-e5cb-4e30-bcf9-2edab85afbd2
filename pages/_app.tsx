import "@/styles/globals.css";

import { ChakraProvider, extendTheme } from "@chakra-ui/react";
import { GoogleTagManager } from "@next/third-parties/google";
import { createPagesBrowserClient } from "@supabase/auth-helpers-nextjs";
import { SessionContextProvider } from "@supabase/auth-helpers-react";
import { getCookie } from "cookies-next";
import type { AppProps } from "next/app";
import posthog from "posthog-js";
import { PostHogProvider } from "posthog-js/react";
import { useEffect, useState } from "react";

import { CookieConsent } from "@/components/CookieConsent/CookieConsent";
import { Loader } from "@/components/v2/Loader/Loader";
import {
  GA_MEASUREMENT_ID,
  IS_PROD,
  IS_RESEARCH_ENV,
  IS_STUDENT_ENV,
} from "@/constants/constants";
import UserProvider from "@/context/user-context";
import { objectivity } from "@/fonts/fonts";
import { useLoader } from "@/hooks/useLoader";
import { extendedTheme } from "@/styles/extendedTheme";
import { theme } from "@/styles/theme";

/* theme.ts */
export const themeExtended = extendTheme({
  fonts: {
    heading: "var(--font-objectivity)",
    body: "var(--font-objectivity)",
  },
  zIndices: {
    tooltip: 1000,
  },
  components: {
    Heading: {
      sizes: null,
    },
    Checkbox: {
      baseStyle: {
        control: {
          borderColor: theme.colors.ui.grey_01.hex,
          borderRadius: theme.border.radius.xs.rem,
          bg: theme.colors.primary.white.hex,
          _checked: {
            bg: theme.colors.primary.purple.hex,
          },
        },
      },
    },
  },
});

if (typeof window !== "undefined") {
  posthog.init(process.env.NEXT_PUBLIC_POSTHOG_KEY!, {
    api_host: process.env.NEXT_PUBLIC_POSTHOG_HOST || "https://app.posthog.com",
  });
}

const App = ({ Component, pageProps }: AppProps) => {
  const [supabaseClient] = useState(() =>
    createPagesBrowserClient({
      supabaseUrl: process.env.NEXT_PUBLIC_SUPABASE_V2_URL,
      supabaseKey: process.env.NEXT_PUBLIC_SUPABASE_V2_ANON_KEY,
    })
  );
  const [session] = useState(pageProps.initialSession);
  const { loading, loaderVariant, loaderText } = useLoader();

  const [loadGAScripts, setLoadGAScripts] = useState(
    getCookie("cookieConsent") === "true"
  );

  useEffect(() => {
    const interval = setInterval(() => {
      const currentConsent = getCookie("cookieConsent");
      if (currentConsent === "true" && !loadGAScripts) {
        setLoadGAScripts(true);
      } else if (currentConsent !== "true" && loadGAScripts) {
        setLoadGAScripts(false);
      }
    }, 1000);

    return () => clearInterval(interval);
  }, [loadGAScripts]);

  return (
    <>
      <style jsx global>
        {`
          :root {
            --font-objectivity: ${objectivity.style.fontFamily};
          }
        `}
      </style>

      {loading && (
        <Loader
          isActive={true}
          variant={loaderVariant}
          loadingText={loaderText}
        />
      )}
      <SessionContextProvider
        supabaseClient={supabaseClient}
        initialSession={session}
      >
        <UserProvider>
          <ChakraProvider theme={extendedTheme}>
            <PostHogProvider client={posthog}>
              <Component {...pageProps} />
            </PostHogProvider>
            <CookieConsent />
            <GoogleTagManager gtmId={GA_MEASUREMENT_ID} />
          </ChakraProvider>
        </UserProvider>
      </SessionContextProvider>
    </>
  );
};

export default !IS_PROD && !IS_RESEARCH_ENV && !IS_STUDENT_ENV ? App : App;
