"use client";

import { Box, VStack } from "@chakra-ui/react";
import Image from "next/image";
import React from "react";

import { StarsBg } from "@/components/StarsBg/StarsBg";
import { PrimaryNavigation } from "@/components/v2/PrimaryNavigation/PrimaryNavigation";

interface AuthLayoutProps {
  variant?: "login" | "sign-up" | "password-reset" | "student-login";
  imagePos?: "left" | "right";
  children: React.ReactNode;
  student?: boolean;
}
export const AuthLayout = ({
  variant,
  children,
  imagePos = "left",
  student = false,
}: AuthLayoutProps) => {
  const imageUrl = `/images/auth/${variant}.svg`;

  return (
    <>
      <Box top="20px" position="absolute" w="100%" left={0}>
        <PrimaryNavigation
          theme="light"
          transparent
          hideLogout
          student={student}
        />
      </Box>
      <VStack
        justifyContent={"center"}
        alignContent={"center"}
        w="100vw"
        h="100vh"
        pos="relative"
        zIndex={10}
        gap={0}
      >
        <Box>{children}</Box>
      </VStack>
      <StarsBg zIndex={-1} />
      {variant && (
        <Box
          position="absolute"
          bottom={0}
          left={imagePos === "right" ? "auto" : 0}
          right={imagePos === "left" ? "auto" : 0}
          display={{ base: "none", md: "block" }}
        >
          <Image src={imageUrl} width={400} height={400} alt="" />
        </Box>
      )}
    </>
  );
};
