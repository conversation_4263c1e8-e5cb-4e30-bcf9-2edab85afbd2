import { useSupabaseClient } from "@supabase/auth-helpers-react";
import Head from "next/head";
import { useRouter } from "next/navigation";
import React, { useEffect, useState } from "react";

import { LoginCard } from "@/components/Card/Card/Card";
import { Loader } from "@/components/v2/Loader/Loader";
import { AuthLayout } from "@/layouts/AuthLayout/AuthLayout";

export const LoginLayout = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [userType, setUserType] = useState<string | null>(null);
  const [authData, setAuthData] = useState({ signInSuccess: false });
  const [authError, setAuthError] = useState("");
  const { push } = useRouter();
  const supabase = useSupabaseClient();

  async function handleSignIn({ email, password }, setFn, setError) {
    setIsLoading(true);
    const response: any = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (response?.data?.session?.access_token) {
      if (response?.data?.user?.id) {
        const { data: adminData } = await supabase
          .from("admins")
          .select("*")
          .eq("user_id", response.data.user.id)
          .eq("permissions", "Administrator");

        if (adminData && adminData.length > 0) {
          setUserType("ADMIN");
        } else if (response?.data?.user?.user_metadata?.orgType === "school") {
          setUserType("SCHOOL");
        } else {
          setUserType("USER");
        }
      }
      setFn({ signInSuccess: true });
    } else {
      setFn({ signInSuccess: false });
      setError(response?.error?.message || "There was an error");
      setIsLoading(false);
    }
  }

  useEffect(() => {
    if (authData.signInSuccess && userType) {
      if (userType === "SCHOOL") {
        push("/school");
      } else if (userType === "ADMIN") {
        push("/admin");
      } else {
        push("/dashboard");
      }
    }
  }, [authData, userType, push]);

  return (
    <>
      <Head>
        <title>Login to Talamo</title>
      </Head>
      <AuthLayout variant="login">
        <LoginCard
          errorMsg={authError}
          onSubmit={(values) => {
            handleSignIn(
              { email: values.email, password: values.password },
              setAuthData,
              setAuthError
            );
          }}
        />
        <Loader isActive={isLoading} />
      </AuthLayout>
    </>
  );
};
