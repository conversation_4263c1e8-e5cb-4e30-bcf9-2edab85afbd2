import axios from "axios";
import { useEffect, useState } from "react";

import { CreateAccountCard } from "@/components/Card/Card/Card";
import { IS_RESEARCH_ENV } from "@/constants/constants";
import { AuthLayout } from "@/layouts";

export function CreateAccountLayout({ hasAccess }) {
  const [authData, setAuthData] = useState({ signInSuccess: false });
  const [authError, setAuthError] = useState("");

  const handleSignUp = async ({ email, password, isOptedIn = false }) => {
    const response: any = await axios.post("/api/create-user", {
      method: "POST",
      data: {
        email,
        password,
        isOptedIn,
      },
      headers: {
        "Content-Type": "application/json",
      },
    });
    if (response?.data?.data?.user?.aud === "authenticated") {
      setAuthData({ signInSuccess: true });
    } else {
      setAuthData({ signInSuccess: false });
      setAuthError(response?.error?.message || "There was an error");
    }

    if (isOptedIn === true) {
      //Subscribe user to brevo if opted in
      await fetch("/api/brevo/add-subscriber", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "x-internal": "true",
        },
        body: JSON.stringify({
          email: email,
        }),
      });
    }
  };

  useEffect(() => {
    if (!!authData?.signInSuccess) {
      window.location.href = "/dashboard";
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [authData]);

  if (IS_RESEARCH_ENV) {
    window.location.href = "/dashboard";
    return null;
  }

  if (!!hasAccess) {
    return (
      <AuthLayout variant="sign-up" imagePos="right">
        <CreateAccountCard
          errorMsg={authError}
          onSubmit={(values) => {
            handleSignUp({
              email: values.email,
              password: values.password,
              isOptedIn: values.isOptedIn,
            });
          }}
        />
      </AuthLayout>
    );
  } else {
    return <>Forbidden</>;
  }
}
