# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

.env

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local
.env*.local.backup
.env*.production

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

data/generated/*.json
# next-video
videos/*
!videos/*.json
!videos/*.js
!videos/*.ts
public/_next-video


*storybook.log
