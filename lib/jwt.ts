import { JWTPayload, jwtVerify, SignJWT } from "jose";

const JWT_SECRET = process.env.JWT_SECRET!;
if (!JWT_SECRET) {
  throw new Error("JWT_SECRET is not defined");
}

const secretKey = new TextEncoder().encode(JWT_SECRET);

export const createToken = async (payload: {
  studentId: string;
  first_names: string;
  surname: string;
  date_of_birth: string;
  code: string;
  school_id: number;
}) => {
  return new SignJWT(payload)
    .setProtectedHeader({ alg: "HS256" })
    .setExpirationTime("1hr")
    .setIssuedAt()
    .sign(secretKey);
};

export const verifyToken = async (token: string) => {
  try {
    const { payload } = await jwtVerify(token, secretKey);
    return payload as JWTPayload;
  } catch (error) {
    return null;
  }
};
