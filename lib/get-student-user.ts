"use server";
import { cookies } from "next/headers";

import { verifyToken } from "@/lib/jwt";
import { calculateAge } from "@/utils/students/calculateAge";

type Student = {
  id: string;
  first_names: string;
  surname: string;
  date_of_birth: string;
  age: number;
  code: string;
  school_id: number;
  exp?: number;
} | null;

export const getStudentUser = async (): Promise<Student> => {
  const token = cookies().get("student_session")?.value;
  if (!token) return null;

  const decoded = await verifyToken(token);

  if (!decoded || typeof decoded !== "object") return null;

  const studentAge = calculateAge(decoded.date_of_birth);

  return {
    id: decoded.studentId as string,
    first_names: decoded.first_names as string,
    surname: decoded.surname as string,
    date_of_birth: decoded.date_of_birth as string,
    age: studentAge,
    code: decoded.code as string,
    school_id: decoded.school_id as number,
    exp: decoded.exp as number,
  };
};
