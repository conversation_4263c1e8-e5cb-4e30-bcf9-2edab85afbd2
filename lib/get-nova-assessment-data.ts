"use server";

import fs from "fs/promises";
import path from "path";

import { GetNovaAssessmentQuery } from "@/types/graphql/graphql";

export const getNovaAssessment =
  async (): Promise<GetNovaAssessmentQuery | null> => {
    try {
      const filePath = path.join(
        process.cwd(),
        "data/generated/novaAssessment.json"
      );
      const file = await fs.readFile(filePath, "utf8");
      return JSON.parse(file);
    } catch (error) {
      console.error("Failed to load assessments:", error);
      return null;
    }
  };
