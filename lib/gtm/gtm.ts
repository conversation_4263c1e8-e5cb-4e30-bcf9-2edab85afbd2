/* eslint-disable no-unused-expressions */
import type { GTMEvent, PageViewEvent } from "./types";

export const sendGTMEvent = (event: GTMEvent) => {
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment, @typescript-eslint/no-unused-expressions
  window && window.dataLayer && window.dataLayer.push(event);
};

export const trackPageView = (url: string) => {
  console.log("PageViewEvent");
  const event: PageViewEvent = {
    event: "pageViewTriggered",
    page: url,
  };
  sendGTMEvent(event);
  return event;
};
