"use client";

import posthog from "posthog-js";
import { PostHog<PERSON><PERSON>ider as <PERSON><PERSON><PERSON>ider } from "posthog-js/react";
import { useEffect } from "react";

export function PostHogProvider({ children }: { children: React.ReactNode }) {
  useEffect(() => {
    const key = process.env.NEXT_PUBLIC_POSTHOG_KEY;
    const host = process.env.NEXT_PUBLIC_POSTHOG_HOST;

    if (!key || !host) {
      throw new Error("PostHog environment variables are missing");
    }

    posthog.init(key, {
      api_host: host,
    });
  }, []);

  return <PHProvider client={posthog}>{children}</PHProvider>;
}
