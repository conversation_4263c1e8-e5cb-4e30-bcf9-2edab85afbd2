import { supabaseConfig } from "@/data/constants";
import {
  AIReportRecord,
  getOrCreateAIReportRecord,
  updateAIReportStatus
} from "@/utils/ai-report/ai-report-record.utils";
import {
  constructWebhookPayload,
  fetchAssessmentData,
  fetchSchoolData,
  parseAssessmentJsonFields,
  validateStudentData
} from "@/utils/ai-report/assessment.utils";
import {
  fetchAllRecommendations,
  filterRecommendationsByProfile,
  groupRecommendationsByCategory
} from "@/utils/ai-report/recommendations.utils";
import { sendWebhookRequest } from "@/utils/ai-report/webhook.utils";
import { createClient, SupabaseClient } from "@supabase/supabase-js";

export interface AIReportRequest {
  reportId: string;
  authToken?: string;
}

export interface AIReportResponse {
  success: boolean;
  message: string;
  aiReportId?: string;
  webhookResponse?: {
    status: number;
    statusText: string;
    data: any;
  };
  recommendations?: any;
  error?: string;
  details?: string;
}

export class AIReportService {
  private supabase: SupabaseClient;
  private webhookUrl: string;

  constructor() {
    this.supabase = createClient(
      supabaseConfig.supabaseUrl,
      supabaseConfig.supabaseKey
    );
    this.webhookUrl = process.env.N8N_WEBHOOK__URL || "";
  }

  /**
   * Generate AI report for a student assessment
   */
  async generateReport(request: AIReportRequest): Promise<AIReportResponse> {
    try {
      // Validate input
      if (!request.reportId) {
        return this.createErrorResponse("Report ID is required", 400);
      }

      // Fetch and validate assessment data
      const assessmentData = await fetchAssessmentData(this.supabase, request.reportId);
      if (!assessmentData) {
        return this.createErrorResponse("Assessment data not found", 404);
      }

      // Validate student data
      const studentData = validateStudentData(assessmentData);
      if (!studentData) {
        return this.createErrorResponse("Student data not found", 404);
      }

      // Parse JSON fields
      const parsedData = parseAssessmentJsonFields(assessmentData);

      // Fetch school data
      const schoolData = await fetchSchoolData(this.supabase, assessmentData.school_id);

      // Fetch and filter recommendations
      const recommendations = await this.processRecommendations(
        studentData,
        parsedData
      );

      // Get or create AI-report record with PENDING status
      let aiReportRecord: AIReportRecord | null = null;
      let requestId = "temp-" + Date.now(); // fallback requestId

      try {
        aiReportRecord = await getOrCreateAIReportRecord(
          this.supabase,
          request.reportId
        );

        if (aiReportRecord) {
          requestId = aiReportRecord.id;
          console.log(`AI Report Record: ${aiReportRecord.id} (Status: ${aiReportRecord.status})`);
        } else {
          console.warn("Failed to create or update AI report record, continuing with temp requestId");
        }
      } catch (error) {
        console.warn("AI report table not available, continuing without record management:", error);
      }

      // Construct webhook payload with requestId
      const payload = constructWebhookPayload(
        assessmentData,
        studentData,
        parsedData,
        recommendations?.recommendations,
        requestId,
        schoolData || undefined
      );

      // Send to webhook
      const webhookResponse = await sendWebhookRequest(this.webhookUrl, payload);

      // Update AI report status to IN_PROGRESS after successful webhook call
      if (aiReportRecord) {
        const statusUpdated = await updateAIReportStatus(
          this.supabase,
          aiReportRecord.id,
          'IN_PROGRESS',
          {
            recommendation_list: recommendations,
            workflow_metadata: {
              ...aiReportRecord.workflow_metadata,
              webhook_sent_at: new Date().toISOString(),
              webhook_status: webhookResponse.status,
              webhook_response: webhookResponse.statusText
            }
          }
        );

        if (!statusUpdated) {
          console.warn("Failed to update AI report status to IN_PROGRESS");
        } else {
          console.log(`AI Report ${aiReportRecord.id} status updated to IN_PROGRESS`);
        }
      }

      return {
        success: true,
        message: "Assessment data submitted successfully",
        aiReportId: aiReportRecord?.id,
        webhookResponse: {
          status: webhookResponse.status,
          statusText: webhookResponse.statusText,
          data: webhookResponse.data
        },
        recommendations
      };

    } catch (error) {
      console.error("AIReportService.generateReport error:", error);
      return this.createErrorResponse(
        "Failed to generate AI report",
        500,
        error instanceof Error ? error.message : "Unknown error"
      );
    }
  }

  /**
   * Process recommendations based on student profile
   */
  private async processRecommendations(
    studentData: any,
    parsedData: any
  ): Promise<any> {
    try {
      // Fetch all recommendations from CMS
      const allRecommendations = await fetchAllRecommendations();

      // Filter based on student profile
      const filteredRecommendations = filterRecommendationsByProfile(
        studentData,
        parsedData,
        allRecommendations
      );

      // Group by category
      return groupRecommendationsByCategory(filteredRecommendations);

    } catch (error) {
      console.error("Error processing recommendations:", error);
      return null;
    }
  }

  /**
   * Create standardized error response
   */
  private createErrorResponse(
    message: string,
    statusCode: number,
    details?: string
  ): AIReportResponse {
    return {
      success: false,
      message,
      error: message,
      details
    };
  }
} 