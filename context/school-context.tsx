"use client";

import {
  createContext,
  ReactNode,
  useCallback,
  useContext,
  useMemo,
  useState,
} from "react";

export interface SchoolUser {
  id: string;
  email: string;
  orgId: number;
}

interface SchoolUserContextValue {
  user: SchoolUser | null;
  logout: () => void;
}

const SchoolUserContext = createContext<SchoolUserContextValue>({
  user: null,
  logout: () => {},
});

export const useSchoolUser = () => useContext(SchoolUserContext);

export const SchoolUserProvider = ({
  children,
  initialUser,
}: {
  children: ReactNode;
  initialUser: SchoolUser;
}) => {
  const [user, setUser] = useState<SchoolUser | null>(initialUser);

  const logout = useCallback(() => {
    setUser(null);
    window.location.href = "/login";
  }, []);

  const value = useMemo(() => ({ user, logout }), [user, logout]);

  return (
    <SchoolUserContext.Provider value={value}>
      {children}
    </SchoolUserContext.Provider>
  );
};
