import { create } from "zustand";
import { persist, StateStorage } from "zustand/middleware";

interface Credit {
  status: string;
  assessment_id: string;
}

interface StoreState {
  firstName: string;
  dateOfBirth: Date | null;
  age: number | null;
  credits: Credit[];
  setFirstName: (firstName: string) => void;
  setCredits: (credits: Credit[]) => void;
  setDateOfBirth: (dateOfBirth: Date | null) => void;
  setAge: (age: number | null) => void;
  reset: () => void;
}

const useStudentStore = create<StoreState>()(
  persist(
    (set) => ({
      firstName: "",
      credits: [],
      dateOfBirth: null,
      age: null,
      setFirstName: (firstName) => set({ firstName }),
      setCredits: (credits) => set({ credits }),
      setDateOfBirth: (dateOfBirth) => set({ dateOfBirth }),
      setAge: (age) => set({ age }),
      reset: () => {
        set({ firstName: "", credits: [], dateOfBirth: null, age: null });
        localStorage.removeItem("student-storage");
      },
    }),
    {
      name: "student-storage",
      getStorage: () => localStorage as StateStorage,
    }
  )
);

export { useStudentStore };
