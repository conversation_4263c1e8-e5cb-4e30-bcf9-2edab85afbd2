"use client";

import { useRouter } from "next/navigation";
import {
  createContext,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";

import { getStudentUser } from "@/lib/get-student-user";
import { useAssessmentResponsesStore } from "@/stores/use-assessment-responses";

export type Student = {
  id: string;
  first_names: string;
  surname: string;
  date_of_birth: string;
  age: number;
  code: string;
  school_id: number;
  exp?: number;
} | null;

interface IStudentProviderProps {
  children: React.ReactNode;
  initialStudent: Student;
}

interface IStudentContextProps {
  student: Student;
  inactivityTimeout: number;
  refreshSession: () => Promise<void>;
  logout: () => void;
}

const INACTIVITY_TIMEOUT_MINS = 10;
const INACTIVITY_TIMEOUT_MS = INACTIVITY_TIMEOUT_MINS * 60 * 1000;

export const StudentContext = createContext<IStudentContextProps>({
  student: null,
  inactivityTimeout: INACTIVITY_TIMEOUT_MS,
  refreshSession: async () => {},
  logout: () => {},
});

export const StudentProvider = ({
  initialStudent,
  children,
}: IStudentProviderProps) => {
  const [student, setStudent] = useState<Student>(initialStudent);
  const router = useRouter();
  const timeoutIdRef = useRef<NodeJS.Timeout | null>(null);

  const resetStore = useAssessmentResponsesStore((s) => s.reset);

  const clearInactivityTimeout = () => {
    if (timeoutIdRef.current) {
      clearTimeout(timeoutIdRef.current);
      timeoutIdRef.current = null;
    }
  };

  const logout = useCallback(() => {
    fetch("/api/student/logout", { method: "POST" }).finally(() => {
      setStudent(null);
      resetStore();
      sessionStorage.removeItem("assessmentData");
      clearInactivityTimeout();
      router.replace("/student/login");
    });
  }, [router, resetStore]);

  const resetInactivityTimer = useCallback(() => {
    clearInactivityTimeout();
    timeoutIdRef.current = setTimeout(logout, INACTIVITY_TIMEOUT_MS);
  }, [logout]);

  const refreshSession = useCallback(async () => {
    try {
      const newStudent = await getStudentUser();
      if (!newStudent?.exp || newStudent.exp * 1000 < Date.now()) {
        logout();
      } else {
        setStudent(newStudent);
        resetInactivityTimer();
      }
    } catch {
      logout();
    }
  }, [logout, resetInactivityTimer]);

  useEffect(() => {
    const handleActivity = () => resetInactivityTimer();
    const handleVisibilityChange = () => {
      if (!document.hidden) resetInactivityTimer();
    };

    document.addEventListener("mousemove", handleActivity);
    document.addEventListener("keydown", handleActivity);
    document.addEventListener("touchstart", handleActivity);
    document.addEventListener("click", handleActivity);
    document.addEventListener("visibilitychange", handleVisibilityChange);

    resetInactivityTimer();

    return () => {
      document.removeEventListener("mousemove", handleActivity);
      document.removeEventListener("keydown", handleActivity);
      document.removeEventListener("touchstart", handleActivity);
      document.removeEventListener("click", handleActivity);
      document.removeEventListener("visibilitychange", handleVisibilityChange);
      clearInactivityTimeout();
    };
  }, [resetInactivityTimer]);

  const contextValue = useMemo(
    () => ({
      student,
      inactivityTimeout: INACTIVITY_TIMEOUT_MS,
      refreshSession,
      logout,
    }),
    [student, refreshSession, logout]
  );

  return (
    <StudentContext.Provider value={contextValue}>
      {children}
    </StudentContext.Provider>
  );
};
