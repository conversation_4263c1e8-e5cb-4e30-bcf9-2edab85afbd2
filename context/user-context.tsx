import { useSupabaseClient } from "@supabase/auth-helpers-react";
import posthog from "posthog-js";
import { createContext, useEffect, useState } from "react";

export type UserContextType = {
  clearUser: () => void;
  loading: boolean;
  user: {
    lastSignIn: string;
    userId: string;
    email: string;
    metadata: {
      is_research?: boolean;
      is_student?: boolean;
      school_id?: number;
      student_code?: number;
      student_id?: number;
    } & object;
    confirmed: string;
  };
};

export const UserContext = createContext<
  { user: null; clearUser: () => void; loading: false } | UserContextType
>({
  user: null,
  clearUser: () => {},
  loading: false,
});

export const UserProvider = ({ children }) => {
  const supabase = useSupabaseClient();
  const [user, setUser] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const { data: authListener } = supabase.auth.onAuthStateChange(
      (_, session) => {
        const userData = session?.user
          ? {
              lastSignIn: session.user.last_sign_in_at,
              userId: session.user.id,
              email: session.user.email,
              metadata: session.user.user_metadata,
              confirmed: session.user.confirmed_at,
            }
          : null;
        setUser(userData);
        setLoading(false);
      }
    );

    return () => {
      authListener.subscription.unsubscribe();
    };
  }, [supabase.auth]);

  const clearUser = () => setUser(null);

  return (
    <UserContext.Provider value={{ user, clearUser, loading }}>
      {children}
    </UserContext.Provider>
  );
};

export default UserProvider;
