/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: false,
  experimental: {
    outputFileTracingExcludes: {
      "*": ["**swc/core**"],
      "/*": ["./public/content/**/*.jpg"],
      "/**/*": ["./public/content/**/*.jpg"],
    },
  },
  swcMinify: false,
  env: {
    PASSWORD_PROTECT: (
      process.env.NEXT_PUBLIC_VERCEL_ENV !== "production"
    ).toString(),
  },
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "www.datocms-assets.com",
      },
    ],
  },
};

module.exports = nextConfig;
