import "chart.js";

declare module "chart.js" {
  interface ScaleTypeRegistry {
    customScale: {
      new (cfg: any): LinearScale;
      defaults: any;
    };
  }

  interface CartesianScaleTypeRegistry {
    customScale: ScaleTypeRegistry["linear"];
  }

  interface DefaultScaleOptions {
    customScale: LinearScaleOptions & {
      border?: {
        dash?: number[];
        display?: boolean;
      };
      grid?: {
        color?: string;
        lineWidth?: number;
      };
      afterBuildTicks?: (axis: any) => void;
    };
  }

  interface ScaleOptionsByType {
    customScale: DefaultScaleOptions["customScale"];
  }
}
