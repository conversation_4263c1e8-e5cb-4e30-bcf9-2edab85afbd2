import { create } from "zustand";

interface AudioState {
  src: string | null;
  isPlaying: boolean;
  audioRef: HTMLAudioElement | null;
  setRef: (ref: HTMLAudioElement) => void;
  setPlaying: (playing: boolean) => void;
  play: (src: string) => void;
  stop: () => void;
}

export const useAudioStore = create<AudioState>((set, get) => ({
  src: null,
  isPlaying: false,
  audioRef: null,
  setRef: (ref) => set({ audioRef: ref }),
  setPlaying: (isPlaying) => set({ isPlaying }),
  play: (src: string) => {
    const audio = get().audioRef;
    if (!audio) return;

    audio.pause();
    audio.currentTime = 0;
    audio.src = src;
    audio.play().catch(console.warn);

    set({ src });
  },
  stop: () => {
    const audio = get().audioRef;
    if (!audio) return;

    audio.pause();
    audio.currentTime = 0;
    set({ src: null, isPlaying: false });
  },
}));
