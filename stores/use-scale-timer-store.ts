import { create } from "zustand";

interface TimerState {
  totalTime: number | null;
  timeLeft: number | null;
  isRunning: boolean;
  previousScaleId: string | null;
  timerInterval: NodeJS.Timeout | null;
  startTimer: (duration: number, scaleId: string, paused?: boolean) => void;
  pauseTimer: () => void;
  resumeTimer: () => void;
  stopTimer: () => void;
  resetTimer: () => void;
}

export const useScaleTimerStore = create<TimerState>((set, get) => ({
  totalTime: null,
  timeLeft: null,
  isRunning: false,
  previousScaleId: null,
  timerInterval: null,

  startTimer: (duration, scaleId, paused = false) => {
    set((state) => {
      if (
        state.previousScaleId === scaleId &&
        state.timeLeft !== null &&
        state.isRunning
      )
        return state;

      if (state.timerInterval) {
        clearInterval(state.timerInterval);
      }

      const interval = setInterval(() => {
        set((currentState) => {
          if (!currentState.isRunning) return currentState;

          if (currentState.timeLeft !== null && currentState.timeLeft > 0) {
            const nextTime = currentState.timeLeft - 1;
            if (nextTime !== currentState.timeLeft) {
              return { timeLeft: nextTime };
            }
            return currentState;
          } else {
            clearInterval(interval);
            return { timeLeft: 0, isRunning: false, timerInterval: null };
          }
        });
      }, 1000);

      return {
        totalTime: duration,
        timeLeft: duration,
        isRunning: !paused,
        previousScaleId: scaleId,
        timerInterval: interval,
      };
    });
  },

  pauseTimer: () => {
    set({ isRunning: false });
  },

  resumeTimer: () => {
    set({ isRunning: true });
  },

  stopTimer: () => {
    const { timerInterval } = get();
    if (timerInterval) {
      clearInterval(timerInterval);
    }

    set({
      totalTime: null,
      timeLeft: null,
      isRunning: false,
      previousScaleId: null,
      timerInterval: null,
    });
  },

  resetTimer: () => {
    const { timerInterval } = get();
    if (timerInterval) {
      clearInterval(timerInterval);
    }

    set({
      totalTime: null,
      timeLeft: null,
      isRunning: false,
      previousScaleId: null,
      timerInterval: null,
    });
  },
}));
