import { create } from "zustand";
import { persist } from "zustand/middleware";

type ItemResponse = {
  id: string;
  name: string;
  answer: string[];
  score: 1 | 0 | null;
  practice: boolean;
  skipped: boolean;
  timeTakenMs: number;
  seen: boolean;
};

type ScaleResponse = {
  id: string;
  name: string;
  totalTimeMs: number;
  reasonForExit: "kickout" | "timeout" | "finished";
  items: ItemResponse[];
};

export type AssessmentResponse = {
  id: string;
  name: string;
  dateCompleted?: string;
  totalTimeMs: number;
  scales: ScaleResponse[];
  isComplete?: boolean;
  assessmentType?: string;
  assessmentAudience?: string;
};

interface AssessmentResponsesState {
  assessment: AssessmentResponse | null;
  initAssessment: (args: { id: string; name: string }) => void;
  loadAssessmentFromDB: (assessment: AssessmentResponse) => void;
  addItemResponse: (args: {
    scaleId: string;
    scaleName: string;
    item: ItemResponse;
  }) => void;
  completeScale: (args: {
    scaleId: string;
    reasonForExit: ScaleResponse["reasonForExit"];
  }) => void;
  completeAssessment: (args: {
    dateCompleted: string;
    totalTimeMs: number;
  }) => void;
  removeScale: (scaleId: string) => void;
  getAssessment: () => AssessmentResponse | null;
  reset: () => void;
}

export const useAssessmentResponsesStore = create<AssessmentResponsesState>()(
  persist(
    (set, get) => ({
      assessment: null,

      initAssessment: ({ id, name }) => {
        set({
          assessment: {
            id,
            name,
            totalTimeMs: 0,
            scales: [],
            assessmentType: "dyslexia",
            assessmentAudience: "schools",
          },
        });
      },

      loadAssessmentFromDB: (assessment) => {
        set({
          assessment: {
            ...assessment,
            assessmentType: assessment.assessmentType || "dyslexia",
            assessmentAudience: assessment.assessmentAudience || "schools",
          },
        });
      },

      addItemResponse: ({ scaleId, scaleName, item }) => {
        set((state) => {
          const current = state.assessment;
          if (!current) return state;

          const scaleIndex = current.scales.findIndex((s) => s.id === scaleId);

          if (scaleIndex === -1) {
            return {
              assessment: {
                ...current,
                scales: [
                  ...current.scales,
                  {
                    id: scaleId,
                    name: scaleName,
                    totalTimeMs: 0,
                    reasonForExit: "finished",
                    items: [item],
                  },
                ],
              },
            };
          }

          const updatedScales = [...current.scales];
          const scale = updatedScales[scaleIndex];
          const existingItemIndex = scale.items.findIndex(
            (i) => i.id === item.id
          );

          if (existingItemIndex === -1) {
            scale.items.push(item);
          } else {
            scale.items[existingItemIndex] = item;
          }

          updatedScales[scaleIndex] = scale;

          return {
            assessment: {
              ...current,
              scales: updatedScales,
            },
          };
        });
      },

      completeScale: ({ scaleId, reasonForExit }) => {
        set((state) => {
          const current = state.assessment;
          if (!current) return state;

          const scaleIndex = current.scales.findIndex((s) => s.id === scaleId);
          if (scaleIndex === -1) return state;

          const updatedScales = [...current.scales];
          const scale = updatedScales[scaleIndex];

          const allItemsCompleted = scale.items.every(
            (item) => item.score !== null || item.skipped
          );

          const finalReasonForExit =
            reasonForExit === "finished" && !allItemsCompleted
              ? scale.reasonForExit
              : reasonForExit;

          const totalTimeMs = scale.items.reduce(
            (acc, item) => acc + item.timeTakenMs,
            0
          );

          updatedScales[scaleIndex] = {
            ...scale,
            totalTimeMs,
            reasonForExit: finalReasonForExit,
          };

          return {
            assessment: {
              ...current,
              scales: updatedScales,
            },
          };
        });
      },

      completeAssessment: ({ dateCompleted }) => {
        set((state) => {
          const current = state.assessment;
          if (!current) return state;

          const isComplete = current.scales.every(
            (scale) => !!scale.reasonForExit
          );

          const totalTimeMs = current.scales.reduce((acc, scale) => {
            return (
              acc +
              scale.items.reduce(
                (itemAcc, item) => itemAcc + item.timeTakenMs,
                0
              )
            );
          }, 0);

          return {
            assessment: {
              ...current,
              dateCompleted,
              totalTimeMs,
              isComplete,
            },
          };
        });
      },

      getAssessment: () => get().assessment,

      removeScale: (scaleId) => {
        set((state) => {
          const current = state.assessment;
          if (!current) return state;

          return {
            assessment: {
              ...current,
              scales: current.scales.filter((scale) => scale.id !== scaleId),
            },
          };
        });
      },

      reset: () => {
        sessionStorage.removeItem("assessmentData");
        set({ assessment: null });
      },
    }),
    {
      name: "assessmentData",
      storage: {
        getItem: (name) => {
          const stored = sessionStorage.getItem(name);
          return stored ? JSON.parse(stored) : null;
        },
        setItem: (name, value) => {
          sessionStorage.setItem(name, JSON.stringify(value));
        },
        removeItem: (name) => {
          sessionStorage.removeItem(name);
        },
      },
    }
  )
);
