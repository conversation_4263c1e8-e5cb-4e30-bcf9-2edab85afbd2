import { create } from "zustand";

interface TimerState {
  elapsedTimeMs: number;
  isRunning: boolean;
  timerInterval: NodeJS.Timeout | null;
  startTime: number | null;
  startTimer: () => void;
  stopTimer: () => void;
  resetTimer: () => void;
}

export const useTimeTakenStore = create<TimerState>((set, get) => ({
  elapsedTimeMs: 0,
  isRunning: false,
  timerInterval: null,
  startTime: null,

  startTimer: () => {
    const { isRunning, timerInterval } = get();
    if (isRunning || timerInterval) return;

    const startTime = Date.now();

    const interval = setInterval(() => {
      set(() => ({
        elapsedTimeMs: Date.now() - startTime,
      }));
    }, 50);

    set({
      isRunning: true,
      timerInterval: interval,
      startTime,
    });
  },

  stopTimer: () => {
    const { timerInterval, startTime } = get();
    if (timerInterval) {
      clearInterval(timerInterval);
    }

    const finalElapsed = startTime ? Date.now() - startTime : 0;

    set({
      elapsedTimeMs: finalElapsed,
      isRunning: false,
      timerInterval: null,
      startTime: null,
    });
  },

  resetTimer: () => {
    const { timerInterval } = get();
    if (timerInterval) {
      clearInterval(timerInterval);
    }

    set({
      elapsedTimeMs: 0,
      isRunning: false,
      timerInterval: null,
      startTime: null,
    });
  },
}));
