import { create } from "zustand";

interface ResponseDisabledState {
  responseDisabled: boolean;
  setResponseDisabled: (disabled: boolean) => void;
  reset: () => void;
}

export const useResponseDisabledStore = create<ResponseDisabledState>(
  (set) => ({
    responseDisabled: true,
    setResponseDisabled: (disabled) => set({ responseDisabled: disabled }),
    reset: () => set({ responseDisabled: false }),
  })
);
