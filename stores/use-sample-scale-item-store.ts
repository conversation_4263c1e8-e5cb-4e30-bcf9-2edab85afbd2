import { create } from "zustand";

interface SampleScaleItemState {
  shouldPlayStimulusForId: string | null;
  triggerPlayStimulus: (id: string) => void;
  replayStimulus: (id: string) => void;
  reset: () => void;
}

export const useSampleScaleItemStore = create<SampleScaleItemState>((set) => ({
  shouldPlayStimulusForId: null,
  triggerPlayStimulus: (id) => set({ shouldPlayStimulusForId: id }),
  replayStimulus: (id) => {
    set({ shouldPlayStimulusForId: null });
    set({ shouldPlayStimulusForId: id });
  },
  reset: () => set({ shouldPlayStimulusForId: null }),
}));
